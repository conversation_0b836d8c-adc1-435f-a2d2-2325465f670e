# Contributing to DIS Projects

Thank you for your interest in contributing to Dynamic Innovative Studio projects! This document provides guidelines and instructions to help you contribute effectively.

## Table of Contents

- [Contributing to DIS Projects](#contributing-to-dis-projects)
  - [Table of Contents](#table-of-contents)
  - [Code of Conduct](#code-of-conduct)
  - [Getting Started](#getting-started)
    - [Setting Up Your Environment](#setting-up-your-environment)
    - [Finding Issues to Work On](#finding-issues-to-work-on)
  - [Development Workflow](#development-workflow)
    - [Branch Naming Conventions](#branch-naming-conventions)
      - [Allowed Prefixes](#allowed-prefixes)
      - [Format](#format)
      - [Examples](#examples)
    - [Commit Message Guidelines](#commit-message-guidelines)
      - [Types](#types)
      - [Examples of commit messages](#examples-of-commit-messages)
    - [Pull Request Process](#pull-request-process)
    - [Code Review Guidelines](#code-review-guidelines)
  - [Coding Standards](#coding-standards)
  - [Testing Guidelines](#testing-guidelines)
  - [Documentation](#documentation)
  - [Community](#community)

## Code of Conduct

We expect all contributors to adhere to our Code of Conduct. Please read [CODE_OF_CONDUCT.md](CODE_OF_CONDUCT.md) before participating.

## Getting Started

### Setting Up Your Environment

1. Fork the repository to your GitHub account
2. Clone your fork locally:

   ```bash
   git clone https://github.com/Dynamic-Innovative-Studio/REPOSITORY-NAME.git
   cd REPOSITORY-NAME
   ```

3. Add the original repository as an upstream remote:

   ```bash
   git remote add upstream https://github.com/Dynamic-Innovative-Studio/REPOSITORY-NAME.git
   ```

4. Install dependencies according to the project's README

### Finding Issues to Work On

- Check the [Issues](https://github.com/Dynamic-Innovative-Studio/REPOSITORY-NAME/issues) tab for open tasks
- Look for issues labeled `good first issue` or `help wanted`
- Comment on an issue to express your interest before starting work

## Development Workflow

### Branch Naming Conventions

All branch names must follow our standardized format to maintain consistency and clarity:

#### Allowed Prefixes

- `feature/` - For new features or enhancements
- `bugfix/` - For bug fixes
- `hotfix/` - For urgent fixes to production code

#### Format

The general format should be:
`<prefix>/<brief-description>`

- Use lowercase letters
- Separate words with hyphens
- Keep descriptions concise but meaningful
- Avoid special characters

#### Examples

✅ Good examples:

```git
feature/user-authentication
bugfix/login-validation
hotfix/security-patch
```

❌ Invalid examples:

```git
Feature/NewLogin (uppercase letters)
bug/fix (incorrect prefix)
feature_payment (missing slash, using underscore)
```

### Commit Message Guidelines

We follow the [Conventional Commits](https://www.conventionalcommits.org/) specification for commit messages:

```git
<type>(<scope>): <description>

[optional body]

[optional footer(s)]
```

#### Types

- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, missing semi-colons, etc)
- `refactor`: Code changes that neither fix bugs nor add features
- `test`: Adding or modifying tests
- `chore`: Changes to the build process or auxiliary tools

#### Examples of commit messages

✅ Good examples:

```git
feat(auth): implement JWT authentication
fix(dashboard): resolve data loading issue
docs(readme): update installation instructions
```

❌ Invalid examples:

```git
added new feature
Fixed bug
Update code
```

### Pull Request Process

1. Update your fork to include the latest changes from the upstream repository:

   ```bash
   git fetch upstream
   git checkout develop
   git merge upstream/develop
   ```

2. Create a new branch following our naming conventions:

   ```bash
   git checkout -b feature/your-feature-name
   ```

3. Make your changes, following our coding standards

4. Write tests for your changes when applicable

5. Commit your changes using conventional commit messages

6. Push your branch to your fork:

   ```bash
   git push origin feature/your-feature-name
   ```

7. Create a pull request against the `develop` branch of the original repository

8. Fill out the pull request template completely

9. Address any feedback from code reviews

### Code Review Guidelines

- Be respectful and constructive in reviews
- Focus on code, not the contributor
- Explain the reasoning behind your suggestions
- Respond to review comments promptly

## Coding Standards

- Follow the language-specific style guide for the project
- Write self-documenting code with clear variable and function names
- Keep functions small and focused on a single responsibility
- Comment complex logic, but prefer readable code over excessive comments
- Remove debugging code before submitting PRs

## Testing Guidelines

- Write unit tests for new features and bug fixes
- Ensure all tests pass before submitting a PR
- Aim for high test coverage, especially for critical functionality
- Include both positive and negative test cases

## Documentation

- Update documentation when changing functionality
- Document public APIs, functions, and classes
- Include examples where appropriate
- Keep README and other documentation up to date

## Community

- Join our [Discord server](https://discord.gg/example) for discussions
- Participate in code reviews to learn and help others
- Share your knowledge through blog posts or presentations
- Mentor new contributors

---

Thank you for contributing to Dynamic Innovative Studio projects! Your efforts help us build better software together.
