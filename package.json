{"name": "site-112", "version": "1.0.0", "description": "SCP: Site-112 plunges players into the chaotic aftermath of a containment breach at a clandestine SCP Foundation facility. 2 Game Modes: Multiplayer & Campaign.", "private": true, "scripts": {"lint": "./scripts/lint.sh", "lint:fix": "./scripts/lint.sh --fix", "lint:report": "./scripts/lint.sh --report", "format": "stylua src/", "format:check": "stylua --check src/", "studio": "echo 'Open Roblox Studio and enable Script Sync in File > Beta Features'", "setup": "./scripts/setup-dev.sh", "test": "selene src/ && stylua --check src/", "validate": "./scripts/setup-dev.sh --tools", "pre-commit": "./scripts/pre-commit.sh", "tools:install": "aftman install", "tools:update": "aftman install --global"}, "repository": {"type": "git", "url": "https://github.com/Dynamic-Innovative-Studio/Site-112.git"}, "keywords": ["roblo<PERSON>", "luau", "game-development", "scp", "multiplayer", "studio-script-sync"], "author": "Dynamic Innovative Studio", "license": "COMMERCIAL", "engines": {"node": ">=22.0.0 <23.0.0"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "husky": "^8.0.3"}, "commitlint": {"extends": ["@commitlint/config-conventional"], "rules": {"type-enum": [2, "always", ["feat", "fix", "docs", "style", "refactor", "test", "chore", "perf", "ci", "build"]], "scope-enum": [2, "always", ["mafs", "tbrds", "cgs", "mcs", "client", "server", "shared", "config", "docs", "ci"]]}}, "husky": {"hooks": {"pre-commit": "./scripts/pre-commit.sh", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}}