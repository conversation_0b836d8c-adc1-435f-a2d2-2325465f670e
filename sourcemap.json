{"name": "site-112-centralized-repo", "className": "DataModel", "filePaths": ["default.project.json"], "children": [{"name": "ReplicatedStorage", "className": "ReplicatedStorage", "children": [{"name": "Configurations", "className": "Folder", "children": [{"name": "Features", "className": "Folder"}, {"name": "Mechanics", "className": "Folder"}, {"name": "Other-NonDefined", "className": "Folder"}, {"name": "Systems", "className": "Folder", "children": [{"name": "MAFS_Configuration", "className": "ModuleScript", "filePaths": ["src/shared/Configurations/Systems/MAFS_Configuration.luau"]}]}]}, {"name": "MAFS", "className": "Folder", "children": [{"name": "Examples", "className": "Folder", "children": [{"name": "BasicUsage", "className": "ModuleScript", "filePaths": ["src/shared/MAFS/Examples/BasicUsage.luau"]}, {"name": "CompleteSetup", "className": "ModuleScript", "filePaths": ["src/shared/MAFS/Examples/CompleteSetup.luau"]}]}, {"name": "Shared", "className": "Folder", "children": [{"name": "API", "className": "ModuleScript", "filePaths": ["src/shared/MAFS/Shared/API.luau"]}, {"name": "MaterialConfig", "className": "ModuleScript", "filePaths": ["src/shared/MAFS/Shared/MaterialConfig.luau"]}, {"name": "PerformanceMonitor", "className": "ModuleScript", "filePaths": ["src/shared/MAFS/Shared/PerformanceMonitor.luau"]}, {"name": "Utils.init", "className": "ModuleScript", "filePaths": ["src/shared/MAFS/Shared/Utils.init.luau"]}]}, {"name": "Tests", "className": "Folder", "children": [{"name": "MAFS_Tests", "className": "ModuleScript", "filePaths": ["src/shared/MAFS/Tests/MAFS_Tests.luau"]}]}, {"name": "Remotes.init", "className": "ModuleScript", "filePaths": ["src/shared/MAFS/Remotes.init.luau"]}]}, {"name": "MCS", "className": "Folder", "children": [{"name": "Remotes", "className": "Folder"}, {"name": "Shared", "className": "Folder", "children": [{"name": "Constants", "className": "ModuleScript", "filePaths": ["src/shared/MCS/Shared/Constants/init.luau"]}, {"name": "UI_Constants", "className": "ModuleScript", "filePaths": ["src/shared/MCS/Shared/UI_Constants/init.luau"]}, {"name": "Utils", "className": "ModuleScript", "filePaths": ["src/shared/MCS/Shared/Utils/init.luau"]}]}]}, {"name": "TBRDS", "className": "Folder", "children": [{"name": "Remotes", "className": "Folder"}, {"name": "Shared", "className": "Folder", "children": [{"name": "Utils", "className": "ModuleScript", "filePaths": ["src/shared/TBRDS/Shared/Utils/init.luau"]}]}]}]}, {"name": "ServerScriptService", "className": "ServerScriptService", "children": [{"name": "CGS", "className": "Folder"}, {"name": "MAFS", "className": "Folder", "children": [{"name": "FootStepServerInitialization.init", "className": "<PERSON><PERSON><PERSON>", "filePaths": ["src/server/MAFS/FootStepServerInitialization.init.server.luau"]}, {"name": "FootStepServerManager.init", "className": "ModuleScript", "filePaths": ["src/server/MAFS/FootStepServerManager.init.luau"]}]}, {"name": "MCS", "className": "Folder", "children": [{"name": "Commands", "className": "Folder", "children": [{"name": "ModerationCommands", "className": "Folder", "children": [{"name": "Ban", "className": "ModuleScript", "filePaths": ["src/server/MCS/Commands/ModerationCommands/Ban/init.luau"]}]}, {"name": "System", "className": "Folder", "children": [{"name": "Reload", "className": "ModuleScript", "filePaths": ["src/server/MCS/Commands/System/Reload/init.luau"]}]}]}, {"name": "Server", "className": "Folder", "children": [{"name": "Command<PERSON><PERSON><PERSON><PERSON><PERSON>", "className": "ModuleScript", "filePaths": ["src/server/MCS/Server/CommandDispatcher/init.luau"]}, {"name": "MCS_ServerInitlization", "className": "<PERSON><PERSON><PERSON>", "filePaths": ["src/server/MCS/Server/MCS_ServerInitlization/init.server.luau"]}, {"name": "Middleware", "className": "Folder", "children": [{"name": "Analytics", "className": "ModuleScript", "filePaths": ["src/server/MCS/Server/Middleware/Analytics/init.luau"]}, {"name": "Authentication", "className": "ModuleScript", "filePaths": ["src/server/MCS/Server/Middleware/Authentication/init.luau"]}, {"name": "<PERSON><PERSON>", "className": "ModuleScript", "filePaths": ["src/server/MCS/Server/Middleware/Logger/init.luau"]}, {"name": "MCS_MiddlewareInitiliazation", "className": "ModuleScript", "filePaths": ["src/server/MCS/Server/Middleware/MCS_MiddlewareInitiliazation/init.luau"]}, {"name": "RateLimiter", "className": "ModuleScript", "filePaths": ["src/server/MCS/Server/Middleware/RateLimiter/init.luau"]}]}, {"name": "PermissionService", "className": "ModuleScript", "filePaths": ["src/server/MCS/Server/PermissionService/init.luau"]}]}]}, {"name": "TBRDS", "className": "Folder", "children": [{"name": "TBRDS_Server.init", "className": "<PERSON><PERSON><PERSON>", "filePaths": ["src/server/TBRDS/TBRDS_Server.init.server.luau"]}, {"name": "TBRDS_RoleHandlers.init", "className": "ModuleScript", "filePaths": ["src/server/TBRDS/TBRDS_RoleHandlers.init.luau"]}]}, {"name": "Stam.init", "className": "<PERSON><PERSON><PERSON>", "filePaths": ["src/server/Stam.init.server.luau"]}]}, {"name": "StarterPlayer", "className": "StarterPlayer", "children": [{"name": "StarterPlayerScripts", "className": "StarterPlayerScripts", "children": [{"name": "CGS", "className": "Folder", "children": [{"name": "Systems", "className": "Folder", "children": [{"name": "InteractionController", "className": "LocalScript"}, {"name": "InventoryController", "className": "LocalScript"}, {"name": "MovementController", "className": "LocalScript"}, {"name": "ObservationController", "className": "LocalScript"}]}]}, {"name": "MAFS", "className": "Folder", "children": [{"name": "FootStepClient.init", "className": "ModuleScript", "filePaths": ["src/client/MAFS/FootStepClient.init.luau"]}, {"name": "FootStepInitialization", "className": "LocalScript", "filePaths": ["src/client/MAFS/FootStepInitialization.client.luau"]}]}, {"name": "MCS", "className": "Folder", "children": [{"name": "Client", "className": "Folder", "children": [{"name": "Command<PERSON><PERSON><PERSON>", "className": "ModuleScript", "filePaths": ["src/client/MCS/Client/CommandParser/init.luau"]}, {"name": "MCS_AutocompleteService", "className": "ModuleScript", "filePaths": ["src/client/MCS/Client/MCS_AutocompleteService/init.luau"]}, {"name": "MCS_ClientInitilization", "className": "LocalScript", "filePaths": ["src/client/MCS/Client/MCS_ClientInitilization/init.client.luau"]}]}, {"name": "UI", "className": "Folder", "children": [{"name": "MCS_Console", "className": "ModuleScript", "filePaths": ["src/client/MCS/UI/MCS_Console/init.luau"]}, {"name": "MCS_FeedbackDisplay", "className": "ModuleScript", "filePaths": ["src/client/MCS/UI/MCS_FeedbackDisplay/init.luau"]}]}]}, {"name": "TBRDS", "className": "Folder", "children": [{"name": "TBRDS_Client.init", "className": "LocalScript", "filePaths": ["src/client/TBRDS/TBRDS_Client.init.client.luau"]}]}]}]}]}