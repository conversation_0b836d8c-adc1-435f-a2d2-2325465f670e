# Selene Configuration for Site-112 Project
std = "roblox"

# Exclude certain directories from linting
exclude = ["packages/", "node_modules/", ".git/", "build/", "dist/"]

# Custom rules configuration
[rules]
# Code Quality Rules
undefined_variable = "deny"
incorrect_standard_library_use = "deny"

# Style Rules
mixed_table = "warn"
empty_if = "warn"
unbalanced_assignments = "warn"
suspicious_reverse_loop = "warn"

# Roblox Specific Rules
roblox_incorrect_roact_usage = "warn"
roblox_incorrect_color3_new_usage = "deny"
roblox_suspicious_udim2_new = "warn"

# Performance Rules
multiple_statements = "allow" # Allow for compact code
shadowing = "warn"

# Variable and global usage rules
unused_variable = "warn"
global_usage = "warn"
