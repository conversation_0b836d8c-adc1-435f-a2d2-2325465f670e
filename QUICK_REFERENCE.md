# 🚀 Site-112 Quick Reference

> **Fast access to essential commands, links, and information for Site-112 development**

---

## ⚡ Quick Commands

### Development Setup

```bash
# Initial setup
./scripts/setup-dev.sh

# Install tools only
./scripts/setup-dev.sh --tools

# Setup Git hooks only
./scripts/setup-dev.sh --hooks
```

### Code Quality

```bash
# Run linting
npm run lint
./scripts/lint.sh

# Auto-fix formatting
npm run lint:fix
./scripts/lint.sh --fix

# Format code
npm run format
stylua src/

# Run all checks
npm test
```

### Development Workflow

```bash
# Open workspace
code site-112-workspace.code-workspace

# Check tool status
selene --version
stylua --version
luau-lsp --version

# Generate lint report
npm run lint:report
./scripts/lint.sh --report
```

---

## 🏗️ System Quick Reference

### MAFS (Audio FootStep System)

```lua
local MAFSAPI = require(ReplicatedStorage.MAFS.Shared.API)

-- Configure materials
MAFSAPI.SetPartMaterial(part, "Snow")
MAFSAPI.SetModelMaterial(model, "Gravel")
MAFSAPI.SetPartsByPattern(workspace, "^Mud_", "Mud")

-- Client controls
MAFSAPI.SetVolume(0.8)
MAFSAPI.SetEnabled(false)
MAFSAPI.SetDebugMode(true)
```

### TBRDS (Tag-Based Role Display)

```lua
-- Server-side tag assignment
local TBRDS = require(ServerScriptService.TBRDS.TBRDS_Main_Server)
TBRDS.AssignTag(player, "Researcher")

-- Check player roles
local roleHandlers = require(ServerScriptService.TBRDS.TBRDS_Role_Handlers)
local hasAccess = roleHandlers.Check(player)
```

### CGS (Cognitive Game System)

```lua
-- Document interaction
local ObservationController = require(StarterPlayerScripts.CGS.Systems.ObservationController)
ObservationController.Initialize()

-- Server-side document management
local ObservationSystem = require(ServerScriptService.CGS.Systems.ObservationSystem_Server)
ObservationSystem.SetPlayerFOV(player, 70)
```

### MCS (Modular Command System)

```lua
-- Command processing
local CommandDispatcher = require(ServerScriptService.MCS.Server.CommandDispatcher)
CommandDispatcher.processCommand(player, "ban username reason")

-- Permission checking
local PermissionService = require(ServerScriptService.MCS.Server.PermissionService)
local hasPermission = PermissionService.hasPermission(player, "moderation.ban")
```

---

## 📁 Important File Locations

### Configuration Files

- **Luau Config**: `.luaurc`
- **Linting**: `selene.toml`
- **Formatting**: `stylua.toml`
- **Tools**: `aftman.toml`
- **Project**: `package.json`

### System Configurations

- **MAFS**: `src/shared/Configurations/Systems/MAFS-Configuration.luau`
- **All Systems**: `src/shared/Configurations/Systems/init.luau`

### Documentation

- **Development**: `docs/development/`
- **Systems**: `docs/project/systems/`
- **Company**: `docs/company/`

### Scripts

- **Setup**: `scripts/setup-dev.sh`
- **Linting**: `scripts/lint.sh`
- **Pre-commit**: `scripts/pre-commit.sh`

---

## 🔗 Essential Links

### Development

- **[VSCode Setup Guide](docs/development/VSCODE_SETUP_GUIDE.md)**
- **[Linting & CI/CD Guide](docs/development/LINTING_AND_CICD_GUIDE.md)**
- **[MAFS Documentation](docs/project/systems/MAFS/MAFS-README.md)**

### External Resources

- **[Roblox Creator Docs](https://create.roblox.com/docs)**
- **[Studio Script Sync](https://create.roblox.com/docs/studio/script-sync)**
- **[Luau Language](https://luau-lang.org/)**
- **[SCP Foundation](https://scp-wiki.wikidot.com/)**

### Organization

- **[Website](https://dynamic-innovative-studio.web.app)**
- **[Discord](https://discord.gg/nGEnj6abUs)**
- **[GitHub](https://github.com/Dynamic-Innovative-Studio/Site-112)**

---

## 🐛 Troubleshooting

### Common Issues

**Selene errors about unknown fields**

```bash
# Update roblox.yml if needed
# Check selene.toml configuration
```

**StyLua formatting issues**

```bash
# Auto-fix formatting
stylua src/
npm run lint:fix
```

**Tools not found**

```bash
# Reinstall tools
aftman install
npm run tools:install
```

**Studio Script Sync not working**

1. Enable Script Sync in File → Beta Features
2. Open your place file
3. Check that src/ folder is syncing

### Getting Help

1. **Check documentation** in `docs/`
2. **Run diagnostics**: `npm test`
3. **Ask in Discord**: [Community Server](https://discord.gg/nGEnj6abUs)
4. **Contact team**: <<EMAIL>>

---

## 📊 Project Status

- **Version**: 1.0.0
- **Platform**: Roblox
- **Language**: Luau
- **Sync Method**: Studio Script Sync
- **Code Quality**: Selene + StyLua + Luau LSP

### System Status

- ✅ **MAFS**: Active & Documented
- ✅ **TBRDS**: Active
- ✅ **CGS**: Active
- ✅ **MCS**: Active
- ✅ **Development Tools**: Configured
- ✅ **Code Quality**: Enforced

---

**Need something added to this quick reference? Contact the development team!**
