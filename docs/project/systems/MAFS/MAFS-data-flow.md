# MAFS Data Flow Diagrams

This document provides comprehensive data flow diagrams for the Modular Audio FootStep System (MAFS), illustrating how data flows through each subsystem and component.

## 1. Overall MAFS System Architecture

```mermaid
graph TB
    subgraph "Client Side"
        CM[Movement Detection]
        CP[Sound Playback]
        CC[Client Configuration]
        CV[Volume Control]
        CS[Sound Cache/Pool]
    end

    subgraph "Server Side"
        SV[Request Validation]
        SM[Material Detection]
        SB[Broadcasting Logic]
        SP[Player State Tracking]
    end

    subgraph "Shared Components"
        SC[MAFS Configuration]
        SMC[Material Config]
        SU[Utilities]
        SPM[Performance Monitor]
        SR[Remote Events]
    end

    subgraph "External Systems"
        WS[Workspace Parts]
        PL[Players]
        RS[ReplicatedStorage]
    end

    CM -->|Position Data| SR
    SR -->|Position Data| SV
    SV -->|Validated Request| SM
    SM -->|Material Data| SB
    SB -->|Audio Data| SR
    SR -->|Audio Data| CP
    CP -->|Sound Instance| CS

    SC -.->|Config Data| CM
    SC -.->|Config Data| SV
    SMC -.->|Material Mapping| SM
    SU -.->|Utilities| SM
    SPM -.->|Metrics| CM
    SPM -.->|Metrics| SV

    WS -.->|Part Materials| SM
    PL -.->|Player Data| SP
    RS -.->|Storage| SR
```

## 2. Client-Side Movement Detection System

```mermaid
graph TB
    subgraph "Movement Detection Flow"
        RB[RenderStep Binding]
        MC[Movement Check]
        CD[Cooldown Check]
        VT[Velocity Threshold]
        FM[Floor Material Check]
        PR[Position Request]
    end

    subgraph "Character Components"
        CH[Character]
        HU[Humanoid]
        RP[HumanoidRootPart]
        HF[Humanoid.FloorMaterial]
    end

    subgraph "Configuration"
        CT[CLIENT_COOLDOWN]
        MT[MovementThreshold]
        EN[footstepEnabled]
    end

    subgraph "Output"
        RE[RemoteEvent:FireServer]
        PD[Position Data]
    end

    RB --> MC
    MC --> CD
    CD -->|Pass| VT
    CD -->|Fail| RB
    VT -->|Pass| FM
    VT -->|Fail| RB
    FM -->|Pass| PR
    FM -->|Fail| RB
    PR --> RE

    MC -.-> CH
    MC -.-> HU
    MC -.-> RP
    FM -.-> HF

    CD -.-> CT
    VT -.-> MT
    MC -.-> EN

    RE --> PD
```

## 3. Server-Side Validation & Broadcasting System

```mermaid
graph TB
    subgraph "Request Validation"
        IR[Incoming Request]
        PV[Player Validation]
        CV[Character Validation]
        CD[Cooldown Check]
        POS[Position Validation]
        DD[Distance Delta Check]
    end

    subgraph "Material Resolution"
        MR[Material Resolver]
        CA[Custom Attributes]
        FM[Floor Material]
        DM[Default Material]
        MD[Material Data]
    end

    subgraph "Audio Data Creation"
        SI[Sound ID Selection]
        VS[Volume Setting]
        PS[Playback Speed]
        RO[RollOff Settings]
        AD[Audio Data Package]
    end

    subgraph "Broadcasting"
        PF[Player Filtering]
        DR[Distance Radius Check]
        BC[Broadcast to Clients]
        PM[Performance Metrics]
    end

    IR --> PV
    PV -->|Valid| CV
    PV -->|Invalid| PM
    CV -->|Valid| CD
    CV -->|Invalid| PM
    CD -->|Valid| POS
    CD -->|Invalid| PM
    POS -->|Valid| DD
    POS -->|Invalid| PM
    DD -->|Valid| MR
    DD -->|Invalid| PM

    MR --> CA
    CA -->|Found| MD
    CA -->|Not Found| FM
    FM -->|Found| MD
    FM -->|Not Found| DM
    DM --> MD

    MD --> SI
    SI --> VS
    VS --> PS
    PS --> RO
    RO --> AD

    AD --> PF
    PF --> DR
    DR --> BC
    BC --> PM
```

## 4. Material Resolution System

```mermaid
graph TB
    subgraph "Material Resolution Hierarchy"
        MR[Material Resolution Request]
        POS[Position Input]
        HUM[Humanoid Input]
    end

    subgraph "Step 1: Custom Attributes"
        RC[Raycast from Position]
        HP[Hit Part Detection]
        CA[Check FootstepMaterial Attribute]
        CM[Custom Material Found]
    end

    subgraph "Step 2: Parent Model Attributes"
        PM[Check Parent Model]
        PA[Parent Attribute Check]
        PMA[Parent Material Found]
    end

    subgraph "Step 3: Humanoid Floor Material"
        HF[Humanoid.FloorMaterial]
        RM[Roblox Material Mapping]
        RMF[Roblox Material Found]
    end

    subgraph "Step 4: Default Fallback"
        DF[Default Material]
        DM[Default Material Data]
    end

    subgraph "Output"
        MD[Material Data Package]
        SI[Sound IDs]
        VOL[Volume Settings]
        PS[Playback Speed Range]
        RO[RollOff Settings]
    end

    MR --> POS
    MR --> HUM
    POS --> RC
    RC --> HP
    HP --> CA
    CA -->|Found| CM
    CA -->|Not Found| PM

    PM --> PA
    PA -->|Found| PMA
    PA -->|Not Found| HF

    HF --> RM
    RM -->|Found| RMF
    RM -->|Not Found| DF

    DF --> DM

    CM --> MD
    PMA --> MD
    RMF --> MD
    DM --> MD

    MD --> SI
    MD --> VOL
    MD --> PS
    MD --> RO
```

## 5. Sound Pooling & Playback System

```mermaid
graph TB
    subgraph "Sound Pool Management"
        SR[Sound Request]
        SC[Sound Cache Check]
        AU[Available Sound Check]
        CS[Create New Sound]
        RS[Reuse Existing Sound]
    end

    subgraph "Sound Configuration"
        AD[Audio Data Input]
        SID[Sound ID Setting]
        VOL[Volume Setting]
        PBS[Playback Speed Setting]
        POS[3D Position Setting]
        ROF[RollOff Configuration]
    end

    subgraph "Playback Management"
        SG[Sound Group Assignment]
        PL[Play Sound]
        PM[Performance Monitoring]
        RT[Return to Pool]
    end

    subgraph "Cache Optimization"
        MC[Max Cache Check]
        CL[Cache Limit Enforcement]
        SD[Sound Destruction]
        ST[Statistics Tracking]
    end

    SR --> SC
    SC -->|Hit| AU
    SC -->|Miss| CS
    AU -->|Available| RS
    AU -->|Busy| CS
    CS --> AD
    RS --> AD

    AD --> SID
    SID --> VOL
    VOL --> PBS
    PBS --> POS
    POS --> ROF

    ROF --> SG
    SG --> PL
    PL --> PM
    PL --> RT

    RT --> MC
    MC -->|Under Limit| ST
    MC -->|Over Limit| CL
    CL --> SD
    SD --> ST
```

## 6. Performance Monitoring System

```mermaid
graph TB
    subgraph "Data Collection"
        SC[Sound Creation Events]
        SD[Sound Destruction Events]
        NR[Network Requests]
        MR[Material Resolutions]
        ER[Error Events]
    end

    subgraph "Metrics Tracking"
        SM[Sound Metrics]
        NM[Network Metrics]
        MM[Material Metrics]
        PM[Performance Metrics]
        EM[Error Metrics]
    end

    subgraph "Analysis & Alerts"
        TH[Threshold Checking]
        AL[Alert Generation]
        EF[Efficiency Calculation]
        RP[Report Generation]
    end

    subgraph "Output"
        MT[Metrics Table]
        WA[Warnings]
        ST[Statistics]
        DB[Debug Output]
    end

    SC --> SM
    SD --> SM
    NR --> NM
    MR --> MM
    ER --> EM

    SM --> TH
    NM --> TH
    MM --> TH
    PM --> TH
    EM --> TH

    TH --> AL
    TH --> EF
    EF --> RP
    AL --> WA

    SM --> MT
    NM --> MT
    MM --> MT
    PM --> MT
    EM --> MT

    MT --> ST
    AL --> DB
    RP --> DB
```

## 7. Remote Communication System

```mermaid
graph TB
    subgraph "Server Side"
        SF[Server Folder Creation]
        SE[Server Event Creation]
        SL[Server Event Listener]
        SH[Server Event Handler]
    end

    subgraph "Client Side"
        CF[Client Folder Access]
        CE[Client Event Access]
        CR[Client Request Sender]
        CL[Client Event Listener]
    end

    subgraph "Communication Flow"
        REQ[Footstep Request]
        VAL[Server Validation]
        RES[Audio Response]
        BRD[Broadcast to Others]
    end

    subgraph "Error Handling"
        TO[Timeout Handling]
        RT[Retry Logic]
        ER[Error Reporting]
        FB[Fallback Mechanisms]
    end

    SF --> SE
    SE --> SL
    SL --> SH

    CF --> CE
    CE --> CR
    CE --> CL

    CR --> REQ
    REQ --> VAL
    VAL --> RES
    RES --> CL
    VAL --> BRD

    CE --> TO
    TO --> RT
    RT --> ER
    ER --> FB

    SH --> VAL
    SH --> BRD
```

## Summary

These data flow diagrams illustrate the complete architecture of the MAFS system:

1. **Overall Architecture** - Shows the high-level interaction between client, server, and shared components
2. **Movement Detection** - Details how client-side movement is detected and validated
3. **Server Validation & Broadcasting** - Shows the server-side validation pipeline and broadcasting logic
4. **Material Resolution** - Illustrates the hierarchical material resolution system
5. **Sound Pooling & Playback** - Details the efficient sound management and playback system
6. **Performance Monitoring** - Shows how metrics are collected and analyzed
7. **Remote Communication** - Details the client-server communication layer

Each system operates independently while maintaining clear interfaces with other components, ensuring modularity and maintainability.

Use Markdown Enhanced Preview to view the diagrams.
