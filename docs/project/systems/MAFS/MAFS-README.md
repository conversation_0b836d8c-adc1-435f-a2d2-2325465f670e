# MAFS - Modular Audio FootStep System

A comprehensive, high-performance footstep audio system for Roblox games with advanced material support, security features, and seamless integration capabilities.

## 🎯 Features

### Core Features

- **Client-Server Architecture**: Optimized for both performance and security
- **Material-Based Audio**: Different sounds for different surface materials
- **Custom Material Support**: Define custom materials via part attributes
- **Sound Object Pooling**: High-performance audio management
- **Security Validation**: Server-side validation prevents audio exploits
- **Distance-Based Broadcasting**: Efficient network usage
- **Debug Mode**: Comprehensive logging for development

### Advanced Features

- **Hierarchical Material Resolution**: Custom attributes → Roblox materials → Default
- **Performance Monitoring**: Track sound usage and efficiency
- **Volume Control**: Client-side volume management
- **Enable/Disable**: Temporary footstep muting for cutscenes
- **Gun System Integration**: Framework-ready for weapon systems
- **Batch Configuration**: Bulk material assignment tools

## 🏗️ Architecture

MAFS follows a modular client-server architecture:

```mmd
┌─────────────────┐    ┌─────────────────┐
│   Client Side   │    │   Server Side   │
├─────────────────┤    ├─────────────────┤
│ Movement        │◄──►│ Validation      │
│ Detection       │    │ Material        │
│ Sound Playback  │    │ Detection       │
│ Volume Control  │    │ Broadcasting    │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────────────────┘
                   │
         ┌─────────────────┐
         │ Shared Config   │
         │ Material Defs   │
         │ RemoteEvents    │
         └─────────────────┘
```

## 🚀 Quick Start

### 1. Basic Setup

The system initializes automatically when the game starts. No manual setup required!

### 2. Configure Custom Materials

```lua
local MAFSAPI = require(ReplicatedStorage.MAFS.Shared.API)

-- Configure a snow area
MAFSAPI.SetModelMaterial(workspace.SnowArea, "Snow")

-- Configure individual parts
MAFSAPI.SetPartMaterial(workspace.MetalGrate, "MetalGrate")

-- Configure by name pattern
MAFSAPI.SetPartsByPattern(workspace, "^Mud_", "Mud")

-- Configure by Roblox material
MAFSAPI.SetPartsByMaterial(workspace, Enum.Material.Rock, "Gravel")
```

### 3. Client-Side Control

```lua
-- Set volume (client only)
MAFSAPI.SetVolume(0.8) -- 80% volume

-- Disable for cutscenes
MAFSAPI.SetEnabled(false)

-- Re-enable
MAFSAPI.SetEnabled(true)
```

## 📚 Available Materials

### Standard Roblox Materials

- Grass, Sand, Concrete, Wood, Metal, Stone, Water
- Automatically detected from part materials

### Custom Materials

- **Snow**: Soft, muffled footsteps
- **Gravel**: Crunchy, scattered sounds
- **Mud**: Wet, squelchy footsteps
- **MetalGrate**: Hollow, metallic clanks

## 🔧 API Reference

### Material Configuration

```lua
-- Configure single part
MAFSAPI.SetPartMaterial(part, materialName) -> boolean

-- Configure entire model
MAFSAPI.SetModelMaterial(model, materialName) -> number

-- Configure by name pattern
MAFSAPI.SetPartsByPattern(parent, pattern, materialName) -> number

-- Configure by Roblox material
MAFSAPI.SetPartsByMaterial(parent, material, materialName) -> number

-- Clear custom material
MAFSAPI.ClearPartMaterial(part) -> boolean

-- Get current material
MAFSAPI.GetPartMaterial(part) -> string|nil
```

### System Control (Client Only)

```lua
-- Volume control
MAFSAPI.SetVolume(volume) -> boolean

-- Enable/disable
MAFSAPI.SetEnabled(enabled) -> boolean
```

### Information & Debugging

```lua
-- Get available materials
MAFSAPI.GetAvailableMaterials() -> table

-- Validate material name
MAFSAPI.IsValidMaterial(materialName) -> boolean

-- Debug mode
MAFSAPI.SetDebugMode(enabled)
MAFSAPI.IsDebugMode() -> boolean

-- Performance metrics
MAFSAPI.GetPerformanceMetrics() -> table

-- System information
MAFSAPI.GetSystemInfo() -> table
```

## 🎮 Integration Examples

### Gun System Integration

```lua
-- Setup during gun system initialization
local function setupGunSystemFootsteps()
    -- Configure tactical environments
    MAFSAPI.SetPartsByPattern(workspace.TacticalArea, "Metal_", "Metal")
    MAFSAPI.SetPartsByPattern(workspace.TacticalArea, "Concrete_", "Concrete")
    
    -- Set appropriate volume for gameplay
    MAFSAPI.SetVolume(0.6)
end

-- Mute during intense combat
local function muteForCombat()
    MAFSAPI.SetEnabled(false)
    -- Re-enable when combat ends
end
```

### Environment Configuration

```lua
-- Configure a complete environment
local function setupDungeon()
    local dungeon = workspace.Dungeon
    
    -- Stone corridors
    MAFSAPI.SetPartsByPattern(dungeon, "Floor_Stone", "Stone")
    
    -- Metal grates
    MAFSAPI.SetPartsByPattern(dungeon, "Grate_", "MetalGrate")
    
    -- Water areas
    MAFSAPI.SetPartsByMaterial(dungeon, Enum.Material.Water, "Water")
end
```

## ⚙️ Configuration

### System Settings

Located in `src/shared/Configurations/Systems/MAFS.luau`:

```lua
Settings = {
    DebugMode = false,
    EnablePerformanceMetrics = true,
    MaxCachedSounds = 20,
    BroadcastRadius = 50,
    ServerCooldown = 0.25,
    ClientCooldown = 0.27,
    MovementThreshold = 0.1,
    StepInterval = 0.3,
    MaxDistanceDelta = 10,
}
```

### Adding Custom Materials

```lua
CustomMaterials = {
    ["YourMaterial"] = {
        SoundIds = {
            "rbxassetid://123456789",
            "rbxassetid://123456790",
        },
        Volume = 0.8,
        PlaybackSpeedRange = { 0.9, 1.1 },
        RollOffMinDistance = 5,
        RollOffMaxDistance = 50,
    },
}
```

## 🔒 Security Features

- **Server-Side Validation**: All footstep requests validated on server
- **Position Verification**: Anti-teleport protection
- **Cooldown Enforcement**: Prevents audio spam
- **Distance Validation**: Maximum movement delta checking
- **Proximity Broadcasting**: Only nearby players receive audio

## 📊 Performance

- **Sound Object Pooling**: Reuses sound instances for efficiency
- **Distance-Based Filtering**: Reduces network traffic
- **Optimized Raycasting**: Minimal performance impact
- **Cached Material Lookups**: Fast material resolution
- **Performance Metrics**: Monitor system efficiency

## 🐛 Debugging

Enable debug mode to see detailed logging:

```lua
MAFSAPI.SetDebugMode(true)
```

Debug output includes:

- Material resolution steps
- Sound playback events
- Configuration changes
- Performance metrics
- Error conditions

## 📁 File Structure

```zsh
src/
├── client/MAFS/
│   ├── FootStep_Client/           # Client-side footstep detection
│   └── FootStep_Initilization/    # Client initialization
├── server/MAFS/
│   ├── FootStep_Server_Manager/   # Server-side validation & broadcasting
│   └── FootStep_Server_Initialization/ # Server initialization
└── shared/
    ├── Configurations/Systems/MAFS.luau # System configuration
    └── MAFS/
        ├── Remotes/               # RemoteEvent management
        ├── Shared/
        │   ├── API.luau          # Public API
        │   ├── MaterialConfig.luau # Material configuration system
        │   └── Utils/            # Utility functions
        └── Examples/             # Usage examples
```

## 🤝 Contributing

When extending MAFS:

1. Follow the existing architecture patterns
2. Add comprehensive documentation
3. Include usage examples
4. Test with multiple players
5. Consider performance impact
6. Maintain security standards

---

For more examples, see `src/shared/MAFS/Examples/BasicUsage.luau`
