# TBRDS API Reference

Complete API reference for the Tag-Based Role Display System (TBRDS) Service-Oriented Architecture.

## Table of Contents

1. [Public API](#public-api)
2. [Service APIs](#service-apis)
3. [Type Definitions](#type-definitions)
4. [Event System](#event-system)
5. [Administrative Interface](#administrative-interface)
6. [Integration Patterns](#integration-patterns)

## Public API

The main TBRDS API provides a clean interface for external systems to interact with the tag system.

### Import

```lua
local TBRDSAPI = require(ReplicatedStorage.TBRDS.Shared.API)
```

### Role Management

#### `GetPlayerRole(player: Player): string`

Gets the current role for a player.

```lua
local role = TBRDSAPI.GetPlayerRole(player)
print("Player role:", role) -- "Developer", "Moderator", etc.
```

**Returns:** Role name as string, defaults to "User" if no role found.

#### `SetPlayerRole(player: Player, roleName: string): ValidationResult`

Sets a player's role (server-side only).

```lua
local result = TBRDSAPI.SetPlayerRole(player, "Developer")
if result.Success then
    print("Role assigned successfully:", result.Role)
else
    warn("Failed to assign role:", result.ErrorMessage)
end
```

**Parameters:**

- `player`: Target player
- `roleName`: Role to assign (must exist in configuration)

**Returns:** `ValidationResult` with success status and details.

#### `RefreshPlayerTag(player: Player): ValidationResult`

Forces a refresh of a player's tag, re-evaluating their role.

```lua
local result = TBRDSAPI.RefreshPlayerTag(player)
if result.Success then
    print("Tag refreshed, new role:", result.Role)
end
```

#### `IsRoleValid(player: Player, roleName: string): boolean`

Checks if a player qualifies for a specific role.

```lua
local canBeDeveloper = TBRDSAPI.IsRoleValid(player, "Developer")
if canBeDeveloper then
    print("Player qualifies for Developer role")
end
```

### Tag Data Management

#### `GetPlayerTagData(player: Player): PlayerTagData?`

Gets comprehensive tag data for a player.

```lua
local tagData = TBRDSAPI.GetPlayerTagData(player)
if tagData then
    print("Role:", tagData.Role)
    print("Last updated:", tagData.LastUpdated)
    print("Validation count:", tagData.ValidationCount)
end
```

**Returns:** `PlayerTagData` object or `nil` if not found.

#### `GetPlayersWithRole(roleName: string): {Player}`

Gets all players currently assigned to a specific role.

```lua
local developers = TBRDSAPI.GetPlayersWithRole("Developer")
print("Current developers:", #developers)
```

#### `GetRoleStatistics(): {[string]: number}`

Gets statistics about role distribution.

```lua
local stats = TBRDSAPI.GetRoleStatistics()
for role, count in pairs(stats) do
    print(string.format("%s: %d players", role, count))
end
```

### Role Information

#### `GetRoleStyle(roleName: string): RoleStyle?`

Gets the visual style configuration for a role.

```lua
local style = TBRDSAPI.GetRoleStyle("Developer")
if style then
    print("Color:", style.Color)
    print("Font:", style.Font)
    print("Has image:", style.Image ~= nil)
end
```

### Event System

#### `SubscribeToTagChanges(callback: EventCallback): string`

Subscribes to tag change events.

```lua
local subscriptionId = TBRDSAPI.SubscribeToTagChanges(function(eventData)
    print(string.format("%s role changed from %s to %s",
        eventData.Player.Name,
        eventData.OldRole or "None",
        eventData.NewRole))
end)
```

**Returns:** Subscription ID for later unsubscription.

#### `UnsubscribeFromTagChanges(subscriptionId: string): boolean`

Unsubscribes from tag change events.

```lua
local success = TBRDSAPI.UnsubscribeFromTagChanges(subscriptionId)
```

### System Information

#### `GetPerformanceMetrics(): PerformanceMetrics`

Gets current system performance metrics.

```lua
local metrics = TBRDSAPI.GetPerformanceMetrics()
print("Tag assignments:", metrics.TagAssignments)
print("Cache hits:", metrics.CacheHits)
print("Errors:", metrics.ErrorCount)
```

#### `GetSystemHealth(): {[string]: any}`

Gets comprehensive system health information.

```lua
local health = TBRDSAPI.GetSystemHealth()
print("Services healthy:", health.servicesHealthy)
print("Players tracked:", health.playersTracked)
```

#### `SetDebugMode(enabled: boolean): ()`

Enables or disables debug mode for detailed logging.

```lua
TBRDSAPI.SetDebugMode(true) -- Enable debug logging
```

## Service APIs

Direct access to individual services (primarily for internal use).

### ServiceManager

```lua
local ServiceManager = require(ServerScript.Parent.Services.ServiceManager)

-- Initialize all services
local success = ServiceManager.Initialize()

-- Get a specific service
local tagService = ServiceManager.GetService("Tag")

-- Check service health
local allHealthy = ServiceManager.AreAllServicesHealthy()

-- Get status summary
local status = ServiceManager.GetServiceStatusSummary()
```

### ConfigurationService

```lua
local ConfigurationService = ServiceManager.GetService("Configuration")

-- Get configuration sections
local settings = ConfigurationService.GetSettings()
local rolePriority = ConfigurationService.GetRolePriority()

-- Check feature flags
local debugMode = ConfigurationService.IsDebugMode()
local metricsEnabled = ConfigurationService.IsPerformanceMetricsEnabled()

-- Subscribe to configuration changes
local listenerId = ConfigurationService.SubscribeToChanges(function(newConfig)
    print("Configuration updated")
end)
```

### RoleService

```lua
local RoleService = ServiceManager.GetService("Role")

-- Get player role with caching
local role = RoleService.GetPlayerRole(player)

-- Validate role assignment
local validation = RoleService.ValidatePlayerRole(player, "Developer")

-- Get role information
local style = RoleService.GetRoleStyle("Developer")
local availableRoles = RoleService.GetAvailableRoles()

-- Cache management
local cacheInfo = RoleService.GetCacheInfo()
RoleService.CleanupCache()
```

### BillboardService

```lua
local BillboardService = ServiceManager.GetService("Billboard")

-- Create billboard
local billboard = BillboardService.CreateBillboard(player, "Developer", style)

-- Update existing billboard
local success = BillboardService.UpdateBillboard(player, "Moderator", newStyle)

-- Remove billboard
BillboardService.RemoveBillboard(player)

-- Get statistics
local stats = BillboardService.GetBillboardStatistics()
```

### TagService

```lua
local TagService = ServiceManager.GetService("Tag")

-- Assign tag to player
local result = TagService.AssignTag(player)

-- Get tag data
local tagData = TagService.GetPlayerTagData(player)

-- Get all player tags
local allTags = TagService.GetAllPlayerTags()

-- Get statistics
local stats = TagService.GetTagStatistics()
```

## Type Definitions

### Core Types

```lua
-- Player role and validation
type PlayerId = number
type RoleName = string

type ValidationResult = {
    Success: boolean,
    Role: RoleName?,
    ErrorCode: string?,
    ErrorMessage: string?,
    SecurityFlags: {string}?,
}

-- Player tag data
type PlayerTagData = {
    Role: RoleName,
    BillboardGUI: BillboardGui?,
    LastUpdated: number,
    ValidationCount: number,
    SecurityFlags: {string}?,
}

-- Role styling
type RoleStyle = {
    Color: Color3,
    Font: Enum.Font,
    Image: string?,
    Gradient: GradientStyle?,
    FontStyle: string?,
    TextStroke: TextStrokeStyle?,
    GetText: ((Player) -> string)?,
}

type GradientStyle = {
    Colors: {Color3},
    Rotation: number,
}

type TextStrokeStyle = {
    Color: Color3,
    Transparency: number,
}
```

### Event Types

```lua
type TagEventType = "TagChanged" | "TagAssigned" | "TagRemoved" | "RoleValidated" | "SecurityViolation"

type TagEventData = {
    Player: Player,
    OldRole: RoleName?,
    NewRole: RoleName,
    Timestamp: number,
    Source: string,
    Metadata: {[string]: any}?,
}

type EventCallback = (TagEventData) -> ()
```

### Performance Types

```lua
type PerformanceMetrics = {
    TagAssignments: number,
    ValidationTime: number,
    SecurityEvents: number,
    CacheHits: number,
    CacheMisses: number,
    ErrorCount: number,
    LastReset: number,
}
```

## Event System

The TBRDS event system enables loose coupling between components and external system integration.

### Event Types

- **TagChanged**: Fired when a player's role changes
- **TagAssigned**: Fired when a tag is initially assigned
- **TagRemoved**: Fired when a tag is removed
- **RoleValidated**: Fired when role validation occurs
- **SecurityViolation**: Fired when security issues are detected

### Event Data Structure

All events include:

- `Player`: The affected player
- `NewRole`: The new role name
- `OldRole`: The previous role (if applicable)
- `Timestamp`: When the event occurred
- `Source`: What triggered the event
- `Metadata`: Additional event-specific data

### Usage Examples

```lua
-- Subscribe to all tag changes
TBRDSAPI.SubscribeToTagChanges(function(eventData)
    if eventData.NewRole == "Developer" then
        -- Grant developer privileges
        DeveloperSystem.GrantAccess(eventData.Player)
    end
end)

-- Direct event system access
local EventSystem = require(ReplicatedStorage.TBRDS.Shared.EventSystem)

EventSystem.Subscribe("SecurityViolation", function(eventData)
    warn("Security violation detected:", eventData.Player.Name)
    SecuritySystem.LogViolation(eventData)
end)
```

## Administrative Interface

### Chat Commands

Available to administrators (rank 252+):

```lua
/tbrds status    -- System health report
/tbrds restart   -- Emergency system restart
/tbrds metrics   -- Performance metrics
/tbrds refresh   -- Refresh all player tags
```

### Programmatic Access

```lua
-- Access the system interface (server-side)
local TBRDSSystem = _G.TBRDSSystem

-- Get system health
local healthReport = TBRDSSystem.GetSystemHealth()
print(healthReport)

-- Check if system is healthy
local isHealthy = TBRDSSystem.IsSystemHealthy()

-- Refresh a specific player's tag
local success = TBRDSSystem.RefreshPlayerTag(player)
```

## Integration Patterns

### Basic Integration

```lua
-- Simple role-based feature toggling
local role = TBRDSAPI.GetPlayerRole(player)
if role == "Developer" then
    -- Enable developer features
end
```

### Event-Driven Integration

```lua
-- React to role changes
TBRDSAPI.SubscribeToTagChanges(function(eventData)
    YourSystem.UpdatePlayerPermissions(eventData.Player, eventData.NewRole)
end)
```

### Service Integration

```lua
-- Access services directly for advanced integration
local ServiceManager = require(ServerScript.Parent.Services.ServiceManager)
local roleService = ServiceManager.GetService("Role")

-- Custom role validation
local customValidation = roleService.ValidatePlayerRole(player, "CustomRole")
```

### Error Handling

```lua
-- Always handle potential failures
local result = TBRDSAPI.SetPlayerRole(player, "Developer")
if not result.Success then
    warn("Failed to set role:", result.ErrorMessage)
    -- Handle the error appropriately
end
```

---

*This API reference covers the complete TBRDS interface. For implementation examples, see the integration examples in the `examples/` directory.*
