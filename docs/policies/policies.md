# POLICIES

## Purpose of Policies

The purpose of this document is to outline the policies and best practices for managing repositories within the DIS organization. These policies are designed to ensure consistency, security, and efficiency across all projects.

### 1. Access Control

**Objective:** Restrict and manage who can view, modify, or administrate repositories to protect intellectual property and maintain workflow integrity.

**Guidelines:**

- **Role-Based Permissions:**  
  - Define clear roles such as Owner, <PERSON><PERSON>, Dev<PERSON>per, and Read-Only.  
  - Use GitHub’s team structure to assign repositories to teams with pre-defined permissions.  
  - Regularly review and audit team memberships.
  - Implement principle of least privilege - grant only the permissions necessary for each role.

- **Repository Creation:**  
  - Limit repository creation to a select group of administrators to prevent sprawl and misconfiguration.  
  - Use organizational policies to set default access permissions (e.g., private or internal) for new repositories.
  - Require approval for public repositories to prevent accidental exposure of proprietary code.

- **Access Reviews:**  
  - Implement scheduled quarterly audits of repository access.
  - Use automated scripts that notify owners when a user's access level remains too permissive for an extended period.  
  - Maintain a log or audit trail for access changes to track modifications over time.
  - Remove access immediately when team members leave the organization.

- **External Collaborators:**  
  - Set strict guidelines for when and how external contributors are invited.  
  - Use time-bound or scoped access for contractors and third-party collaborators.
  - Require NDAs for all external collaborators before granting access.

---

### 2. Naming

**Objective:** Establish a clear and consistent naming convention to facilitate discoverability, clarity, and maintainability across the organization’s digital assets.

**Guidelines:**

- **Repository Names:**  
  - Use a structured format that reflects the project type. For example:  
    - For game engines: `GameName-Engine` (e.g., `GalaxyQuest-Unity` or `Arcadia-UE4`)  
    - For auxiliary projects: `Project-Feature` (e.g., `AI-Pathfinding`).
  - Avoid ambiguous abbreviations; clarity is preferred over brevity.
  - Use kebab-case (lowercase with hyphens) for all repository names.

- **Branch Naming:**  
  - Use standard prefixes such as `feature/`, `bugfix/`, `hotfix/` so branches are immediately identifiable.  
  - Incorporate issue numbers when applicable (e.g., `feature/123-add-new-character`).
  - Keep branch names concise but descriptive.

- **Tags and Releases:**  
  - Adopt semantic versioning (e.g., `v1.0.0`) to clearly indicate releases.  
  - Ensure that tag names are in a consistent format to simplify searches and automation.
  - Include release notes with each tag describing changes, fixes, and new features.

- **File and Directory Naming:**
  - Use consistent casing within each language ecosystem (e.g., PascalCase for C# classes, snake_case for Python).
  - Group related files in logically named directories.
  - Avoid special characters and spaces in filenames.

---

### 3. Branch Protection Rules

**Objective:** Ensure code integrity on critical branches by enforcing review processes, testing, and other quality checks.

**Guidelines:**

- **Protected Branch Setup:**  
  - Identify primary branches such as `main`, `master`, or `release` for strict protection.  
  - Disallow direct commits; require all contributions via pull requests.
  - Enable force-push protection on all protected branches.

- **Pull Request Requirements:**  
  - Mandate that each pull request has at least one or two peer reviews before merging.  
  - Require that all CI/CD checks (e.g., tests, linters, builds) pass before the pull request can be merged.
  - Enforce a rule that pull requests must not be merged if there are unresolved merge conflicts or outdated base branches.
  - Implement size limits on PRs to encourage smaller, more manageable changes.

- **Commit Standards:**  
  - Require commit signing on sensitive branches to ensure authenticity.  
  - Enforce conventional commit message format (e.g., `feat(login): add password validation`).
  - Encourage detailed commit messages that reference specific issues or tasks.

- **Additional Safeguards:**  
  - Consider automatic branch backup or snapshot tools before applying major changes.  
  - Implement automated alerts for unusual branch activity to preempt potential security breaches.
  - Set up branch expiration policies to automatically delete stale branches.

---

### 4. Repository Deletion and Transfer

**Objective:** Prevent accidental loss or unauthorized movement of repositories by imposing strict controls on deletion or transfer operations.

**Guidelines:**

- **Permission Restrictions:**  
  - Allow repository deletion and transfer actions only for organization administrators.  
  - Use role-based policies that restrict these actions from being performed by developers or other lower-level access groups.
  - Implement two-factor authentication for critical operations.

- **Deletion Procedures:**  
  - Require a formal review process before deletion; for example, a ticket or a documented approval process from at least one admin and one project lead.
  - Maintain an archival process: Before deletion, ensure that the repository is archived and associated data (e.g., issues, pull requests, commit history) is backed up.
  - Implement a cooling-off period of at least 7 days before permanent deletion.

- **Transfer Protocols:**  
  - When transferring repositories between organizations or owners, require a formal approval process and clear documentation of the transfer rationale.
  - Notify all stakeholders of the impending transfer and ensure that access configurations are synchronized post-transfer.
  - Verify that intellectual property rights are properly maintained during transfers.

---

### 5. Code Quality and CI/CD

**Objective:** Maintain high standards for code quality and streamline the build-deploy-feedback cycle with robust CI/CD practices.

**Guidelines:**

- **Automated Testing:**  
  - Enforce that all new code contributions come with unit and integration tests.  
  - Configure CI pipelines (e.g., GitHub Actions, Jenkins, Travis CI) to run tests automatically on every pull request.
  - Set minimum code coverage requirements (e.g., 80%) for critical components.

- **Static Analysis and Linting:**  
  - Integrate automated linters, code formatters, and static analysis tools to catch common issues before code review.
  - Prevent merging if quality checks fall below a predefined threshold.
  - Use language-specific tools (e.g., ESLint for JavaScript, Pylint for Python).

- **Continuous Integration Pipelines:**  
  - Standardize build scripts and CI configurations across projects to promote consistency.  
  - Use the CI system to compile, run tests, and deploy preview builds for faster feedback loops.
  - Implement parallel testing to reduce pipeline execution time.

- **Code Review Standards:**  
  - Create a checklist for reviewers that includes code quality, architecture, performance, security, and compliance considerations.  
  - Use automated peer review bots to help enforce certain style guidelines.
  - Encourage constructive feedback and knowledge sharing during reviews.

- **Knowledge Sharing:**  
  - Maintain a shared repository or documentation page that contains best practices for writing tests, using CI/CD, and code review procedures.
  - Schedule regular tech talks or workshops on code quality topics.

---

### 6. Documentation and Metadata

**Objective:** Ensure that every repository is self-descriptive, easy to onboard new contributors, and maintained with up-to-date information.

**Guidelines:**

- **Mandatory Files:**  
  - **README.md:**  
    - Provide an overview of the project, installation instructions, usage examples, and any special requirements (e.g., game engine specifics).  
    - Include badges (build, license, version) where applicable to quickly convey the repository status.
  - **CONTRIBUTING.md:**  
    - Offer guidelines on how to contribute, including coding standards, pull request processes, and branch strategies.
  - **LICENSE:**  
    - Clearly state the licensing information for the project to define usage rights and obligations.
  - **CODEOWNERS:**
    - Define ownership of different parts of the codebase to automatically assign reviewers.

- **Metadata Standards:**  
  - Enforce the addition of topics and tags in GitHub to improve repository searchability (e.g., `game-development`, `Unity`, `Unreal`, etc.).
  - Add a `CHANGELOG.md` for tracking version changes and release notes in a structured format.
  - Keep additional documentation files (e.g., `INSTALL.md`, `ARCHITECTURE.md`) up to date, especially when significant changes to the project occur.

- **Review and Maintenance:**  
  - Set a policy to review and update documentation at dedicated intervals or as part of sprint retrospectives.  
  - Encourage contributors to update documentation when submitting pull requests that impact public interfaces or functionality.
  - Use documentation linters to ensure consistent formatting and completeness.

---

### 7. Security Practices

**Objective:** Protect code, data, and infrastructure from security threats through proactive measures and regular audits.

**Guidelines:**

- **Dependency Management:**
  - Regularly scan dependencies for vulnerabilities using tools like Dependabot or Snyk.
  - Set up automated alerts for security issues in dependencies.
  - Establish a process for promptly addressing critical security updates.

- **Secret Management:**
  - Never commit secrets (API keys, passwords, tokens) to repositories.
  - Use environment variables or secure secret management services for sensitive information.
  - Implement pre-commit hooks to prevent accidental secret exposure.

- **Code Security:**
  - Conduct regular security code reviews focusing on common vulnerabilities.
  - Implement security scanning in CI/CD pipelines.
  - Follow language-specific security best practices.

- **Access Security:**
  - Require two-factor authentication for all organization members.
  - Regularly rotate access tokens and credentials.
  - Implement the principle of least privilege for all access controls.

---

### 8. Release Management

**Objective:** Establish a structured approach to releasing software that ensures quality, traceability, and clear communication.

**Guidelines:**

- **Release Planning:**
  - Define clear release cycles (e.g., monthly, quarterly).
  - Maintain a release calendar visible to all stakeholders.
  - Establish freeze periods before releases for stabilization.

- **Versioning:**
  - Follow semantic versioning (MAJOR.MINOR.PATCH).
  - Document breaking changes prominently in release notes.
  - Tag all releases in the repository with the version number.

- **Release Process:**
  - Use dedicated release branches for preparation and testing.
  - Implement automated release pipelines where possible.
  - Require sign-off from QA, product, and engineering before production deployment.

- **Hotfix Procedure:**
  - Define a streamlined process for critical fixes to production.
  - Ensure hotfixes are merged back to development branches.
  - Document all hotfixes with their rationale and impact.

---

### 9. Collaboration and Communication

**Objective:** Foster effective teamwork through clear communication channels and collaborative practices.

**Guidelines:**

- **Issue Tracking:**
  - Use a consistent issue template with clear sections for description, steps to reproduce, and acceptance criteria.
  - Label issues appropriately for priority, type, and status.
  - Link issues to related pull requests and vice versa.

- **Discussion Forums:**
  - Use GitHub Discussions or similar tools for architectural decisions and broader topics.
  - Document decisions and their rationale for future reference.
  - Keep technical discussions in public channels where possible.

- **Code Reviews:**
  - Provide constructive, specific feedback focused on the code, not the author.
  - Use a consistent review checklist to ensure thoroughness.
  - Respond to review comments promptly to maintain momentum.

- **Knowledge Sharing:**
  - Schedule regular tech talks or brown bags for sharing expertise.
  - Maintain an internal wiki or knowledge base for common procedures.
  - Encourage pair programming and mentorship for complex tasks.
