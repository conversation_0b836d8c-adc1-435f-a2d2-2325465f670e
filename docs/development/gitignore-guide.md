# GitIgnore Guide

This document explains how to use the `.gitignore` file in this repository template.

## Overview

The `.gitignore` file is structured in two main sections:

1. **General Ignores**: Always active for all projects
2. **Project-Specific Ignores**: Commented out by default, to be uncommented based on your project type

## How to Use

### For All Projects

The general section at the top of the file is already active and covers common files that should be ignored regardless of project type, including:

- IDE and editor files
- Temporary files
- Log files
- Environment variables and secrets
- Build and output directories
- Common dependency directories
- Testing and coverage files
- Cache directories

### For Specific Project Types

1. Locate the section for your project type (Node.js, Python, Java, etc.)
2. Uncomment the lines by removing the `#` at the beginning of each line
3. Save the file

### Example: Setting Up for a Node.js Project

To set up the `.gitignore` for a Node.js project:

1. Open the `.gitignore` file
2. Find the Node.js section:

   ```.gitignore
   # ===== NODE.JS =====
   # Uncomment this section for Node.js projects

   # node_modules/
   # jspm_packages/
   # ...
   ```

3. Remove the `#` from each line:

   ```.gitignore
   # ===== NODE.JS =====
   # Uncomment this section for Node.js projects

   node_modules/
   jspm_packages/
   ...
   ```

4. Save the file

### Multiple Project Types

If your project uses multiple technologies (e.g., Node.js frontend with Python backend), you can uncomment multiple sections.

## Custom Ignores

Add any project-specific ignores at the bottom of the file in the "CUSTOM PROJECT IGNORES" section.

## Updating the GitIgnore

As your project evolves, you may need to update the `.gitignore` file:

1. To ignore a new file pattern, add it to the appropriate section
2. If you're adding a new technology to your project, uncomment the relevant section
3. For temporary ignores (not to be committed), use `.git/info/exclude` instead

## Best Practices

- **Be specific**: Avoid overly broad patterns like `*.log` if you only need to ignore specific log files
- **Use negation**: Use `!` to include files that would otherwise be ignored (e.g., `!.vscode/settings.json`)
- **Test your ignores**: Before committing, check that the right files are being ignored with `git status --ignored`
- **Document custom ignores**: Add comments explaining why specific files are ignored

## Resources

- [GitHub's collection of .gitignore templates](https://github.com/github/gitignore)
- [Git documentation on .gitignore](https://git-scm.com/docs/gitignore)
- [gitignore.io](https://www.toptal.com/developers/gitignore) - Generate .gitignore files for your project
