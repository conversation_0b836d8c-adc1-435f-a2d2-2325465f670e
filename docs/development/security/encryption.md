# Encryption

This document outlines the encryption practices and standards for Dynamic Innovative Studio (DIS). It provides comprehensive guidelines for implementing cryptographic controls to protect data confidentiality, integrity, and authenticity across all systems and applications.

## Table of Contents

- [Overview](#overview)
- [Encryption Standards](#encryption-standards)
- [Key Management](#key-management)
- [Data at Rest Encryption](#data-at-rest-encryption)
- [Data in Transit Encryption](#data-in-transit-encryption)
- [Application-Level Encryption](#application-level-encryption)
- [Database Encryption](#database-encryption)

## Overview

Encryption is a fundamental security control that protects sensitive data from unauthorized access and ensures data integrity. This framework establishes standards for cryptographic implementations across all DIS systems and applications.

### Encryption Principles

- **Strong Cryptography**: Use industry-standard, proven encryption algorithms
- **Key Security**: Implement secure key generation, storage, and management
- **Defense in Depth**: Apply encryption at multiple layers
- **Performance Balance**: Balance security requirements with performance needs
- **Compliance**: Meet regulatory and industry encryption requirements
- **Future-Proofing**: Plan for cryptographic agility and algorithm updates

## Encryption Standards

### Approved Algorithms

#### Symmetric Encryption

- **AES (Advanced Encryption Standard)**
  - **AES-256**: Primary standard for high-security applications
  - **AES-128**: Acceptable for standard security requirements
  - **Modes**: GCM (preferred), CBC, CTR
  - **Key Sizes**: 128-bit (minimum), 256-bit (recommended)

- **ChaCha20-Poly1305**
  - **Use Cases**: High-performance applications, mobile devices
  - **Key Size**: 256-bit
  - **Authentication**: Built-in authenticated encryption

#### Asymmetric Encryption

- **RSA (Rivest-Shamir-Adleman)**
  - **Key Sizes**: 2048-bit (minimum), 3072-bit (recommended), 4096-bit (high security)
  - **Padding**: OAEP with SHA-256 (preferred), PKCS#1 v1.5 (legacy only)
  - **Use Cases**: Key exchange, digital signatures, legacy systems

- **Elliptic Curve Cryptography (ECC)**
  - **Curves**: P-256 (minimum), P-384 (recommended), P-521 (high security)
  - **Algorithms**: ECDSA (signatures), ECDH (key exchange)
  - **Use Cases**: Mobile applications, IoT devices, performance-critical systems

#### Hash Functions

- **SHA-2 Family**
  - **SHA-256**: Standard for most applications
  - **SHA-384**: Security applications
  - **SHA-512**: High-security applications
  - **Use Cases**: Data integrity, digital signatures, key derivation

- **SHA-3 (Keccak)**
  - **SHA3-256**: Alternative to SHA-256
  - **Use Cases**: Future-proofing, specific compliance requirements

### Deprecated Algorithms

#### Prohibited Algorithms

- **DES/3DES**: Insufficient key length and security
- **MD5**: Cryptographically broken hash function
- **SHA-1**: Vulnerable to collision attacks
- **RC4**: Stream cipher with known vulnerabilities
- **RSA < 2048-bit**: Insufficient key length for current threats

### Algorithm Selection Guidelines

```python
# Example: Encryption algorithm selection
class EncryptionStandards:
    def __init__(self):
        self.approved_symmetric = {
            'AES-256-GCM': {'security_level': 'high', 'performance': 'good'},
            'AES-128-GCM': {'security_level': 'standard', 'performance': 'excellent'},
            'ChaCha20-Poly1305': {'security_level': 'high', 'performance': 'excellent'}
        }

        self.approved_asymmetric = {
            'RSA-2048': {'security_level': 'standard', 'performance': 'fair'},
            'RSA-3072': {'security_level': 'high', 'performance': 'poor'},
            'ECDSA-P256': {'security_level': 'standard', 'performance': 'good'},
            'ECDSA-P384': {'security_level': 'high', 'performance': 'good'}
        }

        self.approved_hash = {
            'SHA-256': {'security_level': 'standard', 'performance': 'excellent'},
            'SHA-384': {'security_level': 'high', 'performance': 'good'},
            'SHA-512': {'security_level': 'high', 'performance': 'good'}
        }

    def select_encryption(self, data_classification, performance_requirements):
        """Select appropriate encryption based on requirements"""
        if data_classification == 'restricted':
            if performance_requirements == 'high':
                return 'ChaCha20-Poly1305'
            else:
                return 'AES-256-GCM'
        elif data_classification == 'confidential':
            return 'AES-256-GCM'
        else:
            return 'AES-128-GCM'

    def select_key_exchange(self, compatibility_requirements):
        """Select key exchange algorithm"""
        if compatibility_requirements == 'legacy':
            return 'RSA-2048'
        else:
            return 'ECDSA-P256'

    def validate_algorithm(self, algorithm):
        """Validate if algorithm is approved"""
        all_approved = {**self.approved_symmetric, **self.approved_asymmetric, **self.approved_hash}
        return algorithm in all_approved
```

## Key Management

### Key Lifecycle Management

#### Key Generation

- **Random Number Generation**: Use cryptographically secure random number generators
- **Key Strength**: Generate keys with appropriate entropy
- **Key Derivation**: Use approved key derivation functions (PBKDF2, scrypt, Argon2)
- **Seed Sources**: Use multiple entropy sources for key generation
- **Hardware Security**: Use hardware security modules (HSMs) for critical keys

#### Key Storage

- **Secure Storage**: Store keys in secure key management systems
- **Separation**: Separate keys from encrypted data
- **Access Controls**: Implement strict access controls for keys
- **Encryption**: Encrypt keys when stored (key encryption keys)
- **Backup**: Secure backup and recovery procedures for keys

#### Key Distribution

- **Secure Channels**: Distribute keys over secure channels
- **Authentication**: Authenticate key recipients
- **Key Escrow**: Implement key escrow for business continuity
- **Split Knowledge**: Use split knowledge for critical keys
- **Dual Control**: Require dual control for sensitive key operations

#### Key Rotation

- **Regular Rotation**: Rotate keys according to policy
- **Automated Rotation**: Implement automated key rotation where possible
- **Emergency Rotation**: Procedures for emergency key rotation
- **Backward Compatibility**: Maintain backward compatibility during rotation
- **Audit Trail**: Log all key rotation activities

#### Key Destruction

- **Secure Deletion**: Securely delete keys when no longer needed
- **Verification**: Verify complete key destruction
- **Documentation**: Document key destruction activities
- **Compliance**: Meet regulatory requirements for key destruction
- **Recovery Prevention**: Ensure keys cannot be recovered after destruction

### Key Management Implementation

```python
# Example: Key management system
import os
import secrets
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import base64
from datetime import datetime, timedelta

class KeyManager:
    def __init__(self, hsm_config=None):
        self.backend = default_backend()
        self.hsm_config = hsm_config
        self.key_store = {}  # In production, use secure key store
        self.key_metadata = {}

    def generate_symmetric_key(self, algorithm='AES-256', purpose='data_encryption'):
        """Generate symmetric encryption key"""
        if algorithm == 'AES-256':
            key = secrets.token_bytes(32)  # 256 bits
        elif algorithm == 'AES-128':
            key = secrets.token_bytes(16)  # 128 bits
        else:
            raise ValueError(f"Unsupported algorithm: {algorithm}")

        key_id = self.generate_key_id()

        # Store key metadata
        self.key_metadata[key_id] = {
            'algorithm': algorithm,
            'purpose': purpose,
            'created_at': datetime.utcnow(),
            'rotation_due': datetime.utcnow() + timedelta(days=365),
            'status': 'active',
            'usage_count': 0
        }

        # Store key securely (in production, use HSM or secure key store)
        self.key_store[key_id] = key

        return key_id

    def derive_key(self, password, salt=None, iterations=100000):
        """Derive key from password using PBKDF2"""
        if salt is None:
            salt = secrets.token_bytes(16)

        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=iterations,
            backend=self.backend
        )

        key = kdf.derive(password.encode('utf-8'))
        return key, salt

    def encrypt_data(self, key_id, plaintext):
        """Encrypt data using specified key"""
        if key_id not in self.key_store:
            raise ValueError("Key not found")

        key = self.key_store[key_id]

        # Generate random IV
        iv = secrets.token_bytes(12)  # 96 bits for GCM

        # Create cipher
        cipher = Cipher(
            algorithms.AES(key),
            modes.GCM(iv),
            backend=self.backend
        )

        encryptor = cipher.encryptor()
        ciphertext = encryptor.update(plaintext.encode('utf-8')) + encryptor.finalize()

        # Update usage count
        self.key_metadata[key_id]['usage_count'] += 1

        # Return encrypted data with IV and tag
        return {
            'ciphertext': base64.b64encode(ciphertext).decode('utf-8'),
            'iv': base64.b64encode(iv).decode('utf-8'),
            'tag': base64.b64encode(encryptor.tag).decode('utf-8'),
            'key_id': key_id
        }

    def decrypt_data(self, encrypted_data):
        """Decrypt data using stored key"""
        key_id = encrypted_data['key_id']

        if key_id not in self.key_store:
            raise ValueError("Key not found")

        key = self.key_store[key_id]

        # Decode components
        ciphertext = base64.b64decode(encrypted_data['ciphertext'])
        iv = base64.b64decode(encrypted_data['iv'])
        tag = base64.b64decode(encrypted_data['tag'])

        # Create cipher
        cipher = Cipher(
            algorithms.AES(key),
            modes.GCM(iv, tag),
            backend=self.backend
        )

        decryptor = cipher.decryptor()
        plaintext = decryptor.update(ciphertext) + decryptor.finalize()

        return plaintext.decode('utf-8')

    def rotate_key(self, old_key_id):
        """Rotate encryption key"""
        if old_key_id not in self.key_metadata:
            raise ValueError("Key not found")

        old_metadata = self.key_metadata[old_key_id]

        # Generate new key
        new_key_id = self.generate_symmetric_key(
            algorithm=old_metadata['algorithm'],
            purpose=old_metadata['purpose']
        )

        # Mark old key as deprecated
        old_metadata['status'] = 'deprecated'
        old_metadata['deprecated_at'] = datetime.utcnow()
        old_metadata['replacement_key'] = new_key_id

        return new_key_id

    def generate_key_id(self):
        """Generate unique key identifier"""
        return f"key_{secrets.token_hex(16)}"

    def check_key_rotation_due(self):
        """Check for keys that need rotation"""
        due_keys = []
        current_time = datetime.utcnow()

        for key_id, metadata in self.key_metadata.items():
            if metadata['status'] == 'active' and current_time >= metadata['rotation_due']:
                due_keys.append(key_id)

        return due_keys
```

## Data at Rest Encryption

### Storage Encryption

#### File System Encryption

- **Full Disk Encryption**: Encrypt entire disk volumes
- **File-Level Encryption**: Encrypt individual files
- **Folder Encryption**: Encrypt specific directories
- **Transparent Encryption**: Operating system-level encryption
- **Application-Level**: Application-controlled encryption

#### Database Encryption

- **Transparent Data Encryption (TDE)**: Database-level encryption
- **Column-Level Encryption**: Encrypt specific database columns
- **Table-Level Encryption**: Encrypt entire database tables
- **Backup Encryption**: Encrypt database backups
- **Log Encryption**: Encrypt database transaction logs

#### Cloud Storage Encryption

- **Server-Side Encryption**: Cloud provider managed encryption
- **Client-Side Encryption**: Customer managed encryption
- **Key Management**: Cloud key management services
- **Envelope Encryption**: Multiple layers of encryption
- **Cross-Region Encryption**: Encryption for data replication

### Implementation Examples

#### File Encryption

```python
# Example: File encryption implementation
import os
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

class FileEncryption:
    def __init__(self, password=None):
        if password:
            self.key = self.derive_key_from_password(password)
        else:
            self.key = Fernet.generate_key()
        self.cipher = Fernet(self.key)

    def derive_key_from_password(self, password):
        """Derive encryption key from password"""
        salt = b'stable_salt_for_demo'  # In production, use random salt
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key

    def encrypt_file(self, file_path, output_path=None):
        """Encrypt file contents"""
        if output_path is None:
            output_path = file_path + '.encrypted'

        with open(file_path, 'rb') as file:
            file_data = file.read()

        encrypted_data = self.cipher.encrypt(file_data)

        with open(output_path, 'wb') as file:
            file.write(encrypted_data)

        return output_path

    def decrypt_file(self, encrypted_file_path, output_path=None):
        """Decrypt file contents"""
        if output_path is None:
            output_path = encrypted_file_path.replace('.encrypted', '.decrypted')

        with open(encrypted_file_path, 'rb') as file:
            encrypted_data = file.read()

        decrypted_data = self.cipher.decrypt(encrypted_data)

        with open(output_path, 'wb') as file:
            file.write(decrypted_data)

        return output_path

    def encrypt_directory(self, directory_path, output_directory=None):
        """Encrypt all files in directory"""
        if output_directory is None:
            output_directory = directory_path + '_encrypted'

        os.makedirs(output_directory, exist_ok=True)

        for root, dirs, files in os.walk(directory_path):
            for file in files:
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path, directory_path)
                output_path = os.path.join(output_directory, relative_path + '.encrypted')

                # Create subdirectories if needed
                os.makedirs(os.path.dirname(output_path), exist_ok=True)

                self.encrypt_file(file_path, output_path)

        return output_directory
```

## Data in Transit Encryption

### Transport Layer Security (TLS)

#### TLS Configuration

- **TLS Version**: Use TLS 1.2 minimum, TLS 1.3 preferred
- **Cipher Suites**: Use strong cipher suites only
- **Perfect Forward Secrecy**: Enable PFS for all connections
- **Certificate Validation**: Implement proper certificate validation
- **HSTS**: Use HTTP Strict Transport Security

#### TLS Implementation

```python
# Example: Secure TLS configuration
import ssl
import socket
import requests
from urllib3.util.ssl_ import create_urllib3_context

class SecureTLSConfig:
    def __init__(self):
        self.min_tls_version = ssl.TLSVersion.TLSv1_2
        self.preferred_tls_version = ssl.TLSVersion.TLSv1_3

        # Approved cipher suites
        self.approved_ciphers = [
            'ECDHE-RSA-AES256-GCM-SHA384',
            'ECDHE-RSA-AES128-GCM-SHA256',
            'ECDHE-RSA-AES256-SHA384',
            'ECDHE-RSA-AES128-SHA256',
            'DHE-RSA-AES256-GCM-SHA384',
            'DHE-RSA-AES128-GCM-SHA256'
        ]

    def create_secure_context(self):
        """Create secure SSL context"""
        context = ssl.create_default_context()

        # Set minimum TLS version
        context.minimum_version = self.min_tls_version

        # Set cipher suites
        context.set_ciphers(':'.join(self.approved_ciphers))

        # Enable certificate verification
        context.check_hostname = True
        context.verify_mode = ssl.CERT_REQUIRED

        # Disable weak protocols
        context.options |= ssl.OP_NO_SSLv2
        context.options |= ssl.OP_NO_SSLv3
        context.options |= ssl.OP_NO_TLSv1
        context.options |= ssl.OP_NO_TLSv1_1

        return context

    def secure_https_request(self, url, **kwargs):
        """Make secure HTTPS request"""
        session = requests.Session()

        # Create secure SSL context
        context = self.create_secure_context()

        # Configure session with secure context
        adapter = requests.adapters.HTTPAdapter()
        adapter.init_poolmanager(ssl_context=context)
        session.mount('https://', adapter)

        # Make request
        response = session.get(url, **kwargs)
        return response

    def validate_certificate(self, hostname, port=443):
        """Validate server certificate"""
        context = self.create_secure_context()

        with socket.create_connection((hostname, port)) as sock:
            with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                cert = ssock.getpeercert()

                # Validate certificate fields
                validation_result = {
                    'valid': True,
                    'subject': cert.get('subject'),
                    'issuer': cert.get('issuer'),
                    'version': cert.get('version'),
                    'serial_number': cert.get('serialNumber'),
                    'not_before': cert.get('notBefore'),
                    'not_after': cert.get('notAfter'),
                    'san': cert.get('subjectAltName', [])
                }

                return validation_result
```

### Application-Level Encryption

#### Message Encryption

- **End-to-End Encryption**: Encrypt messages between endpoints
- **Message Authentication**: Verify message integrity and authenticity
- **Forward Secrecy**: Ensure past communications remain secure
- **Key Exchange**: Secure key exchange protocols
- **Protocol Security**: Use secure messaging protocols

#### API Encryption

- **Request Encryption**: Encrypt API request payloads
- **Response Encryption**: Encrypt API response data
- **Header Protection**: Protect sensitive headers
- **Token Encryption**: Encrypt authentication tokens
- **Payload Signing**: Sign API payloads for integrity

## Database Encryption

### Database-Level Encryption

#### Transparent Data Encryption (TDE)

```sql
-- Example: SQL Server TDE implementation
-- Create database master key
CREATE MASTER KEY ENCRYPTION BY PASSWORD = 'StrongPassword123!';

-- Create certificate for TDE
CREATE CERTIFICATE TDE_Certificate
WITH SUBJECT = 'Database Encryption Certificate';

-- Create database encryption key
USE [MyDatabase];
CREATE DATABASE ENCRYPTION KEY
WITH ALGORITHM = AES_256
ENCRYPTION BY SERVER CERTIFICATE TDE_Certificate;

-- Enable TDE
ALTER DATABASE [MyDatabase] SET ENCRYPTION ON;

-- Verify encryption status
SELECT
    db_name(database_id) as database_name,
    encryption_state,
    encryption_state_desc,
    percent_complete
FROM sys.dm_database_encryption_keys;
```

#### Column-Level Encryption

```sql
-- Example: Column-level encryption
-- Create column master key
CREATE COLUMN MASTER KEY [CMK_Auto1]
WITH (
    KEY_STORE_PROVIDER_NAME = 'MSSQL_CERTIFICATE_STORE',
    KEY_PATH = 'CurrentUser/My/A66BB0F6DD70BDFF02B62D0F87E340288E6F9305'
);

-- Create column encryption key
CREATE COLUMN ENCRYPTION KEY [CEK_Auto1]
WITH VALUES (
    COLUMN_MASTER_KEY = [CMK_Auto1],
    ALGORITHM = 'RSA_OAEP',
    ENCRYPTED_VALUE = 0x016E000001630075007200720065006E00740075007300650072002F006D0079002F006100360036006200620030006600360064006400370030006200640066006600300032006200360032006400300066003800370065003300340030003200380038006500360066003900330030003500
);

-- Create table with encrypted columns
CREATE TABLE Customers (
    CustomerID int IDENTITY(1,1) PRIMARY KEY,
    FirstName varchar(50) COLLATE Latin1_General_BIN2
        ENCRYPTED WITH (ENCRYPTION_TYPE = DETERMINISTIC,
                       ALGORITHM = 'AEAD_AES_256_CBC_HMAC_SHA_256',
                       COLUMN_ENCRYPTION_KEY = CEK_Auto1),
    LastName varchar(50) COLLATE Latin1_General_BIN2
        ENCRYPTED WITH (ENCRYPTION_TYPE = DETERMINISTIC,
                       ALGORITHM = 'AEAD_AES_256_CBC_HMAC_SHA_256',
                       COLUMN_ENCRYPTION_KEY = CEK_Auto1),
    SSN char(11) COLLATE Latin1_General_BIN2
        ENCRYPTED WITH (ENCRYPTION_TYPE = DETERMINISTIC,
                       ALGORITHM = 'AEAD_AES_256_CBC_HMAC_SHA_256',
                       COLUMN_ENCRYPTION_KEY = CEK_Auto1),
    Email varchar(100)
);
```

### Application-Level Database Encryption

```python
# Example: Application-level database encryption
from sqlalchemy import create_engine, Column, Integer, String, LargeBinary
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from cryptography.fernet import Fernet
import base64

Base = declarative_base()

class EncryptedCustomer(Base):
    __tablename__ = 'encrypted_customers'

    id = Column(Integer, primary_key=True)
    encrypted_name = Column(LargeBinary)
    encrypted_email = Column(LargeBinary)
    encrypted_phone = Column(LargeBinary)

    def __init__(self, name, email, phone, encryption_key):
        self.cipher = Fernet(encryption_key)
        self.encrypted_name = self.cipher.encrypt(name.encode())
        self.encrypted_email = self.cipher.encrypt(email.encode())
        self.encrypted_phone = self.cipher.encrypt(phone.encode())

    def decrypt_data(self, encryption_key):
        cipher = Fernet(encryption_key)
        return {
            'name': cipher.decrypt(self.encrypted_name).decode(),
            'email': cipher.decrypt(self.encrypted_email).decode(),
            'phone': cipher.decrypt(self.encrypted_phone).decode()
        }

class DatabaseEncryption:
    def __init__(self, database_url, encryption_key):
        self.engine = create_engine(database_url)
        self.Session = sessionmaker(bind=self.engine)
        self.encryption_key = encryption_key
        Base.metadata.create_all(self.engine)

    def store_customer(self, name, email, phone):
        """Store encrypted customer data"""
        session = self.Session()
        try:
            customer = EncryptedCustomer(name, email, phone, self.encryption_key)
            session.add(customer)
            session.commit()
            return customer.id
        finally:
            session.close()

    def get_customer(self, customer_id):
        """Retrieve and decrypt customer data"""
        session = self.Session()
        try:
            customer = session.query(EncryptedCustomer).filter_by(id=customer_id).first()
            if customer:
                return customer.decrypt_data(self.encryption_key)
            return None
        finally:
            session.close()
```

---

**Document Version**: 1.0
**Last Updated**: 30-05-2025
**Next Review**: 30-08-2025
**Owner**: Dynamic Innovative Studio
**Approved By**: Bleck, Founder & CEO
