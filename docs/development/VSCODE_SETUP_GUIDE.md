# 🚀 VSCode Development Environment Setup Guide

## Overview

This guide will help you set up a professional VSCode environment for Roblox/Luau development with Studio Script Sync.

## 📦 Required Extensions

### Core Extensions (Auto-installed via `.vscode/extensions.json`)

1. **Luau Language Server** (`johnnymorganz.luau-lsp`) - Essential for Luau development
2. **Selene VSCode** (`kampfkarren.selene-vscode`) - Luau linting
3. **GitLens** (`eamodio.gitlens`) - Enhanced Git integration
4. **Error Lens** (`usernamehw.errorlens`) - Inline error display
5. **Todo Tree** (`gruntfuggly.todo-tree`) - TODO/FIXME tracking
6. **Code Spell Checker** (`streetsidesoftware.code-spell-checker`) - Spell checking
7. **Indent Rainbow** (`oderwat.indent-rainbow`) - Visual indentation guide

### Installation

When you open this workspace, VSCode will automatically prompt you to install recommended extensions.

## ⚙️ Configuration Features

### 🎯 Luau Language Server

- **Roblox API Integration**: Full Roblox API autocomplete and documentation
- **Type Checking**: Strict type checking for better code quality
- **Import Suggestions**: Automatic import suggestions for services
- **Inlay Hints**: Parameter names and type hints
- **Directory Aliases**: Use `shared`, `client`, `server` for imports (configured in `.luaurc`)

### 🔍 Linting with Selene

- **Roblox Standard**: Configured for Roblox development
- **Custom Rules**: Optimized for our codebase
- **Performance Focused**: Warns about common performance issues
- **Code Quality**: Catches undefined variables and incorrect API usage

### 📁 File Management

- **Smart Nesting**: Related files are grouped together
- **Type-based Sorting**: Files organized by type
- **Search Optimization**: Excludes unnecessary directories

### 🎨 Code Formatting

- **Auto-format on Save**: Consistent code style
- **2-space Indentation**: Standard for Luau
- **Trim Whitespace**: Removes trailing spaces
- **Organize Imports**: Keeps imports clean

## 🛠️ Quick Setup Steps

### 1. Clone and Open Workspace

```bash
git clone [repository-url]
cd site-112-centralized-repo
code .
```

### 2. Install Extensions

- VSCode will prompt to install recommended extensions
- Click "Install All" when prompted

### 3. Enable Studio Script Sync

1. Open Roblox Studio
2. Go to **File > Beta Features**
3. Enable **"Studio Script Sync [Early Preview]"**
4. Restart Studio

### 4. Start Syncing Scripts

1. Right-click any script in Studio
2. Select sync option
3. Choose location in your workspace
4. Start coding in VSCode!

## 📋 Keyboard Shortcuts

| Action | Shortcut | Description |
|--------|----------|-------------|
| Format Document | `Shift+Alt+F` | Format current file |
| Quick Fix | `Ctrl+.` | Show code actions |
| Go to Definition | `F12` | Navigate to definition |
| Find References | `Shift+F12` | Find all references |
| Rename Symbol | `F2` | Rename variable/function |
| Command Palette | `Ctrl+Shift+P` | Access all commands |
| Toggle Terminal | `Ctrl+`` | Show/hide terminal |

## 🎯 Best Practices

### File Organization

```zsh
workspace/
├── synced-scripts/
│   ├── client/
│   │   ├── MAFS-Client.luau
│   │   └── MCS-Client.luau
│   ├── server/
│   │   ├── MAFS-Server.luau
│   │   └── MCS-Server.luau
│   └── shared/
│       ├── MAFS-Shared.luau
│       └── Configurations.luau
```

### Code Quality

- Use **type annotations** for better autocomplete
- Follow **consistent naming conventions**
- Add **TODO comments** for future improvements
- Use **meaningful variable names**
- Keep **functions small and focused**

### Git Workflow

- **Commit synced files** to version control
- Use **descriptive commit messages**
- **Branch per feature** for better collaboration
- **Review code** before merging

## 🔧 Troubleshooting

### Common Issues

**Extension not working?**

- Reload VSCode window (`Ctrl+Shift+P` → "Reload Window")
- Check extension is enabled in Extensions panel

**Luau LSP not providing autocomplete?**

- Ensure file has `.luau` extension
- Check if Roblox types are enabled in settings
- Restart Luau LSP (`Ctrl+Shift+P` → "Luau LSP: Restart Server")

**Selene not linting?**

- Check `selene.toml` exists in workspace root
- Ensure Selene extension is installed
- Check file is not in excluded directories

**Studio Script Sync not working?**

- Verify beta feature is enabled
- Check file permissions
- Restart Studio if needed

### Performance Tips

- **Close unused tabs** to improve performance
- **Use workspace folders** for large projects
- **Exclude large directories** from search
- **Disable unused extensions** for better speed

## 📚 Additional Resources

- [Luau Language Server Documentation](https://github.com/JohnnyMorganz/luau-lsp)
- [Selene Linter Rules](https://kampfkarren.github.io/selene/)
- [Studio Script Sync Forum](https://devforum.roblox.com/t/early-preview-studio-script-sync/3257548)
- [VSCode Luau Development Guide](https://create.roblox.com/docs/projects/external-tools)

## 🆘 Getting Help

1. **Check this guide first**
2. **Search existing issues** in our repository
3. **Ask in #engineering-help** channel
4. **Create detailed bug reports** with screenshots
