# 🔍 Linting and CI/CD Guide for Site-112

## Overview

This guide covers the comprehensive linting and CI/CD setup for the Site-112 project, designed to ensure code quality, consistency, and automated deployment.

## 🛠️ Tools and Technologies

### Linting Tools

- **<PERSON><PERSON>** - <PERSON><PERSON>/Lua linter for code quality and best practices
- **<PERSON>y<PERSON>ua** - Code formatter for consistent styling
- **<PERSON><PERSON> LSP** - Language server for type checking and analysis

### CI/CD Platform

- **GitHub Actions** - Automated workflows for testing, linting, and deployment
- **<PERSON><PERSON>man** - Tool manager for Roblox development tools

## 📁 File Structure

```zsh
.github/workflows/
├── lint-and-format.yml    # Main linting and formatting checks
├── test.yml               # Type analysis and project validation
├── pr-checks.yml          # Pull request quality checks
└── release.yml            # Automated release workflow

scripts/
├── lint.sh               # Local linting script
├── pre-commit.sh         # Git pre-commit hook
└── setup-dev.sh          # Development environment setup

Configuration Files:
├── selene.toml           # Selene linter configuration
├── stylua.toml           # StyLua formatter configuration
├── aftman.toml           # Tool version management
└── .luaurc               # Luau language configuration
```

## 🚀 Quick Start

### 1. Initial Setup

```bash
# Run the setup script
./scripts/setup-dev.sh

# Or install tools manually
aftman install
```

### 2. Local Development

```bash
# Run linting
./scripts/lint.sh

# Auto-fix formatting
./scripts/lint.sh --fix

# Generate detailed report
./scripts/lint.sh --report
```

### 3. Git Integration

The setup script automatically installs Git hooks that will:

- Run linting on staged files before commit
- Check commit message format
- Prevent commits with linting errors

## 🔍 Linting Configuration

### Selene Rules

Our Selene configuration (`selene.toml`) enforces:

- **Code Quality**: Undefined variables, incorrect API usage
- **Style**: Mixed tables, empty statements
- **Roblox Specific**: Proper API usage, performance patterns
- **Custom Rules**: Project-specific best practices

### StyLua Formatting

Our StyLua configuration (`stylua.toml`) ensures:

- **Consistent Indentation**: 2 spaces
- **Line Width**: 100 characters
- **Quote Style**: Auto-prefer double quotes
- **Call Parentheses**: Always required
- **Require Sorting**: Alphabetical organization

## 🤖 CI/CD Workflows

### 1. Lint and Format (`lint-and-format.yml`)

**Triggers**: Push to main/dev branches, Pull requests
**Actions**:

- Runs Selene linter on all Luau files
- Checks StyLua formatting
- Generates detailed reports
- Comments on PRs with issues
- Auto-formats code when `[auto-format]` is in commit message

### 2. Test Suite (`test.yml`)

**Triggers**: Push to main/dev branches, Pull requests
**Actions**:

- Luau type analysis
- Project structure validation
- Code quality metrics
- Security scanning

### 3. PR Quality Checks (`pr-checks.yml`)

**Triggers**: Pull request events
**Actions**:

- Validates PR title format (conventional commits)
- Analyzes change impact
- Checks documentation requirements
- Evaluates PR size
- Tests affected core systems

### 4. Release (`release.yml`)

**Triggers**: Version tags, Manual dispatch
**Actions**:

- Runs all quality checks
- Builds project files
- Generates release notes
- Creates GitHub release
- Notifies team

## 📋 Code Quality Standards

### Commit Message Format

We use conventional commits:

```zsh
feat: add new MAFS sound system
fix: resolve client-server sync issue
docs: update API documentation
style: format code with StyLua
refactor: reorganize server modules
test: add unit tests for TBRDS
chore: update dependencies
```

### Code Style Guidelines

- **Indentation**: 2 spaces (no tabs)
- **Line Length**: 100 characters maximum
- **Naming**: camelCase for variables, PascalCase for types
- **Comments**: Use `--` for single line, `--[[]]` for blocks
- **TODOs**: Format as `-- TODO: description`

### File Organization

```lua
--!strict
-- Module description and purpose

-- Type definitions
type PlayerData = {
    name: string,
    level: number,
}

-- Constants
local DEFAULT_TIMEOUT = 30

-- Dependencies
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Local functions
local function processPlayer(player: Player): PlayerData
    -- Implementation
end

-- Module exports
local Module = {}

function Module.publicFunction(): ()
    -- Implementation
end

return Module
```

## 🔧 Local Development Workflow

### Daily Development

1. **Start Development**:

   ```bash
   git checkout -b feature/new-feature
   rojo serve
   ```

2. **Code and Test**:
   - Write code in VSCode with Studio Script Sync
   - Test in Roblox Studio
   - Run local linting: `./scripts/lint.sh`

3. **Commit Changes**:

   ```bash
   git add .
   git commit -m "feat: add new feature"
   # Pre-commit hooks run automatically
   ```

4. **Push and Create PR**:

   ```bash
   git push origin feature/new-feature
   # Create PR on GitHub
   # CI/CD workflows run automatically
   ```

### Fixing Linting Issues

```bash
# Check what's wrong
./scripts/lint.sh

# Auto-fix formatting
./scripts/lint.sh --fix

# Generate detailed report
./scripts/lint.sh --report
```

## 📊 Quality Metrics

### Automated Checks

- **Code Coverage**: Tracked through static analysis
- **Complexity**: Monitored via file size and function length
- **Technical Debt**: TODO/FIXME comment tracking
- **Security**: Pattern scanning for sensitive data

### PR Quality Gates

- ✅ All linting checks pass
- ✅ Code is properly formatted
- ✅ No security issues detected
- ✅ Documentation updated (if needed)
- ✅ Conventional commit format
- ✅ Reasonable PR size

## 🚨 Troubleshooting

### Common Issues

**Linting Fails Locally**:

```bash
# Ensure tools are installed
aftman install

# Check tool versions
selene --version
stylua --version
```

**CI/CD Workflow Fails**:

- Check GitHub Actions logs
- Ensure all required files are committed
- Verify aftman.toml has correct tool versions

**Pre-commit Hook Issues**:

```bash
# Reinstall hooks
./scripts/setup-dev.sh --hooks

# Bypass hooks (not recommended)
git commit --no-verify
```

**Formatting Conflicts**:

```bash
# Auto-fix all formatting
stylua src/

# Check what would change
stylua --check src/
```

## 🔄 Continuous Improvement

### Adding New Rules

1. Update `selene.toml` with new rules
2. Test locally: `./scripts/lint.sh`
3. Update documentation
4. Commit changes

### Updating Tools

1. Update versions in `aftman.toml`
2. Run `aftman install`
3. Test all workflows
4. Update CI/CD if needed

## 📚 Additional Resources

- [Selene Documentation](https://kampfkarren.github.io/selene/)
- [StyLua Configuration](https://github.com/JohnnyMorganz/StyLua)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Conventional Commits](https://www.conventionalcommits.org/)

---

**Remember**: Quality code is maintainable code. These tools help us write better software together! 🎯
