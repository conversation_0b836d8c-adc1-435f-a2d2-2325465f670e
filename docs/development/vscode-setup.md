# VS Code Setup Guide

This document provides guidance on setting up Visual Studio Code for optimal development experience with DIS projects.

## Recommended Extensions

Our repository includes a `.vscode/extensions.json` file that recommends extensions for this project. When you open the project in VS Code, you'll be prompted to install these extensions.

## Workspace Settings

The `.vscode/settings.json` file configures VS Code for optimal use with this project:

- Format on save is enabled
- <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> are configured to work together
- Git integration is optimized for team workflows
- Language-specific settings are pre-configured

## Debugging

The `.vscode/launch.json` file includes configurations for:

- Running the main application
- Debugging the current file
- Running tests for the current file

To start debugging:

1. Open the Debug panel (Ctrl+Shift+D / Cmd+Shift+D)
2. Select a configuration from the dropdown
3. Click the green play button or press F5

## Keyboard Shortcuts

Here are some useful keyboard shortcuts for VS Code:

- **Ctrl+Shift+P / Cmd+Shift+P**: Command Palette
- **Ctrl+P / Cmd+P**: Quick Open
- **F5**: Start Debugging
- **Ctrl+`/ Cmd+`**: Toggle Terminal
- **Ctrl+Shift+G / Cmd+Shift+G**: Source Control
- **Alt+Shift+F / Shift+Option+F**: Format Document

## Customization

Feel free to customize your personal VS Code settings, but avoid committing changes to the workspace settings unless they benefit the entire team.

## Troubleshooting

If you encounter issues with the VS Code setup:

1. Ensure all recommended extensions are installed
2. Reload VS Code window (Ctrl+Shift+P / Cmd+Shift+P → "Reload Window")
3. Check the "Output" panel for error messages
4. Consult the [VS Code documentation](https://code.visualstudio.com/docs)
