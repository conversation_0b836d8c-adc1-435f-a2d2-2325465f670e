# TypeScript Code Standards

This document outlines the TypeScript coding standards and conventions that should be followed across all projects within the Dynamic Innovative Studio organization. These standards are designed to ensure consistency, readability, quality, and maintainability of the TypeScript codebase while leveraging TypeScript's powerful type system.

## Table of Contents

- [Overview](#overview)
- [File Structure and Headers](#file-structure-and-headers)
- [Code Organization](#code-organization)
- [Type Definitions](#type-definitions)
- [Interfaces and Types](#interfaces-and-types)
- [Classes and Objects](#classes-and-objects)
- [Functions and Methods](#functions-and-methods)
- [Generics](#generics)
- [Modules and Imports](#modules-and-imports)
- [Error Handling](#error-handling)
- [Asynchronous Code](#asynchronous-code)
- [Comments and Documentation](#comments-and-documentation)
- [Code Quality and Linting](#code-quality-and-linting)
- [Testing Standards](#testing-standards)
- [Performance Guidelines](#performance-guidelines)
- [Security Considerations](#security-considerations)

## Overview

TypeScript is a strongly typed programming language that builds on JavaScript by adding static type definitions. These standards ensure that TypeScript code written within the DIS organization maintains high quality, consistency, and leverages TypeScript's type safety features effectively.

### Core Principles

- **Type Safety**: Leverage TypeScript's type system for better code reliability
- **Consistency**: Maintain consistent coding style across all TypeScript files
- **Readability**: Write code that is easy to read and understand
- **Maintainability**: Structure code for easy maintenance and updates
- **Performance**: Follow performance best practices
- **Security**: Implement secure coding practices
- **Modern Standards**: Use modern TypeScript features and ES6+ syntax

## File Structure and Headers

### File Header Format

All TypeScript files must include a standardized header comment block at the top of the file:

```typescript
/**
 * @file FILENAME.TS
 *
 * @version 1.0.0
 * <AUTHOR>
 * @contributors
 *
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * Brief description of the file's purpose and functionality.
 * Additional details about the module or component.
 */
```

### Header Guidelines

- **@file**: Use UPPERCASE filename with .TS or .TSX extension
- **@version**: Follow semantic versioning (MAJOR.MINOR.PATCH)
- **@author**: Primary author of the file
- **@contributors**: List additional contributors (one per line)
- **@copyright**: Always "Dynamic Innovative Studio"
- **@description**: Clear, concise description of file purpose

### File Organization Structure

```typescript
/**
 * @file USER-SERVICE.TS
 *
 * @version 1.0.0
 * <AUTHOR>
 * @contributors
 *
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * User service for managing user data and operations.
 * Provides CRUD operations and user validation functionality.
 */

// ------------ IMPORTS
import type { User, CreateUserRequest, UpdateUserRequest } from '../types/user';
import type { ApiResponse, PaginatedResponse } from '../types/api';
import { ApiClient } from '../clients/api-client';
import { ValidationError, NotFoundError } from '../errors/custom-errors';

// ------------ CONSTANTS
const DEFAULT_PAGE_SIZE = 20;
const MAX_PAGE_SIZE = 100;
const CACHE_TTL = 300000; // 5 minutes

// ------------ TYPES AND INTERFACES
interface UserServiceConfig {
  apiClient: ApiClient;
  cacheEnabled?: boolean;
  defaultPageSize?: number;
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

// ------------ HELPER FUNCTIONS
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// ------------ USER SERVICE CLASS IMPLEMENTATION
export class UserService {
  private readonly apiClient: ApiClient;
  private readonly cache = new Map<string, CacheEntry<User>>();
  private readonly config: Required<UserServiceConfig>;

  constructor(config: UserServiceConfig) {
    this.apiClient = config.apiClient;
    this.config = {
      cacheEnabled: true,
      defaultPageSize: DEFAULT_PAGE_SIZE,
      ...config
    };
  }

  async createUser(request: CreateUserRequest): Promise<User> {
    // Implementation
  }
}

// ------------ EXPORTS
export type { UserServiceConfig };
export { UserService as default };
```

## Code Organization

### Section Comments

Use standardized section comments to organize code within files:

```typescript
// ------------ IMPORTS
// ------------ CONSTANTS
// ------------ TYPES AND INTERFACES
// ------------ HELPER FUNCTIONS
// ------------ [FUNCTIONALITY NAME]
// ------------ EXPORTS
```

### Import Organization

Organize imports in the following order with type imports first:

1. **Type-only imports**
2. **Node.js built-in modules**
3. **Third-party libraries**
4. **Internal modules (relative imports)**

```typescript
// ------------ IMPORTS
// Type-only imports
import type { Request, Response, NextFunction } from 'express';
import type { User, UserRole } from '../types/user';
import type { DatabaseConnection } from '../types/database';

// Node.js built-ins
import { promises as fs } from 'fs';
import path from 'path';

// Third-party libraries
import express from 'express';
import { z } from 'zod';
import bcrypt from 'bcrypt';

// Internal modules
import { config } from '../config/app-config';
import { logger } from '../utils/logger';
import { validateUser } from './validation';
```

^ Do NOT include the comments in the import section.

## Type Definitions

### Basic Type Usage

Use TypeScript's built-in types and create custom types when needed:

```typescript
// ------------ TYPES AND INTERFACES
// Primitive types
type UserId = string;
type UserAge = number;
type IsActive = boolean;

// Union types
type UserStatus = 'active' | 'inactive' | 'pending' | 'suspended';
type Theme = 'light' | 'dark' | 'auto';

// Literal types
type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// Array types
type UserIds = string[];
type UserRoles = readonly string[];

// Object types
type UserPreferences = {
  readonly theme: Theme;
  readonly notifications: boolean;
  readonly language: string;
};

// Function types
type ValidationFunction<T> = (value: T) => boolean;
type AsyncValidator<T> = (value: T) => Promise<boolean>;
type EventHandler<T> = (event: T) => void;
```

### Advanced Type Patterns

```typescript
// Conditional types
type ApiResponse<T> = T extends string
  ? { message: T }
  : { data: T };

// Mapped types
type Partial<T> = {
  [P in keyof T]?: T[P];
};

type Required<T> = {
  [P in keyof T]-?: T[P];
};

// Template literal types
type EventName<T extends string> = `on${Capitalize<T>}`;
type ApiEndpoint<T extends string> = `/api/v1/${T}`;

// Utility types
type UserUpdate = Partial<Pick<User, 'name' | 'email' | 'preferences'>>;
type UserSummary = Omit<User, 'password' | 'internalNotes'>;
type CreateUserData = Omit<User, 'id' | 'createdAt' | 'updatedAt'>;
```

## Interfaces and Types

### Interface Definitions

Use interfaces for object shapes and contracts:

```typescript
// ------------ TYPES AND INTERFACES
interface User {
  readonly id: string;
  name: string;
  email: string;
  age: number;
  roles: UserRole[];
  preferences: UserPreferences;
  readonly createdAt: Date;
  readonly updatedAt: Date;
}

interface CreateUserRequest {
  name: string;
  email: string;
  age: number;
  roles?: UserRole[];
  preferences?: Partial<UserPreferences>;
}

interface UpdateUserRequest {
  name?: string;
  email?: string;
  age?: number;
  preferences?: Partial<UserPreferences>;
}

interface UserRepository {
  findById(id: string): Promise<User | null>;
  findByEmail(email: string): Promise<User | null>;
  create(user: CreateUserRequest): Promise<User>;
  update(id: string, updates: UpdateUserRequest): Promise<User>;
  delete(id: string): Promise<void>;
  findMany(filters: UserFilters): Promise<PaginatedResponse<User>>;
}
```

### Generic Interfaces

```typescript
interface Repository<T, TCreate, TUpdate> {
  findById(id: string): Promise<T | null>;
  create(data: TCreate): Promise<T>;
  update(id: string, data: TUpdate): Promise<T>;
  delete(id: string): Promise<void>;
  findMany(filters: Record<string, unknown>): Promise<T[]>;
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: Date;
}

interface PaginatedResponse<T> {
  items: T[];
  totalCount: number;
  pageSize: number;
  currentPage: number;
  totalPages: number;
}

interface EventEmitter<TEvents extends Record<string, unknown[]>> {
  on<K extends keyof TEvents>(event: K, listener: (...args: TEvents[K]) => void): void;
  emit<K extends keyof TEvents>(event: K, ...args: TEvents[K]): void;
  off<K extends keyof TEvents>(event: K, listener: (...args: TEvents[K]) => void): void;
}
```

### Extending Interfaces

```typescript
interface BaseEntity {
  readonly id: string;
  readonly createdAt: Date;
  readonly updatedAt: Date;
}

interface User extends BaseEntity {
  name: string;
  email: string;
  roles: UserRole[];
}

interface AdminUser extends User {
  adminLevel: number;
  permissions: Permission[];
  lastAdminAction?: Date;
}

interface ApiUser extends User {
  apiKey: string;
  rateLimit: number;
  quotaUsed: number;
}
```

## Classes and Objects

### Class Structure

Follow consistent class structure with proper typing:

```typescript
abstract class BaseService<T, TCreate, TUpdate> {
  protected readonly logger: Logger;
  protected readonly config: ServiceConfig;

  constructor(config: ServiceConfig) {
    this.config = config;
    this.logger = new Logger(this.constructor.name);
  }

  abstract create(data: TCreate): Promise<T>;
  abstract findById(id: string): Promise<T | null>;
  abstract update(id: string, data: TUpdate): Promise<T>;
  abstract delete(id: string): Promise<void>;

  protected validateId(id: string): void {
    if (!id || typeof id !== 'string') {
      throw new ValidationError('Invalid ID provided');
    }
  }
}

class UserService extends BaseService<User, CreateUserRequest, UpdateUserRequest> {
  private readonly userRepository: UserRepository;
  private readonly cache = new Map<string, CacheEntry<User>>();

  constructor(config: UserServiceConfig) {
    super(config);
    this.userRepository = new UserRepository(config.database);
  }

  async create(data: CreateUserRequest): Promise<User> {
    this.validateCreateRequest(data);

    const existingUser = await this.userRepository.findByEmail(data.email);
    if (existingUser) {
      throw new ConflictError('User with this email already exists');
    }

    const user = await this.userRepository.create(data);
    this.cache.set(user.id, { data: user, timestamp: Date.now(), ttl: CACHE_TTL });

    this.logger.info('User created', { userId: user.id });
    return user;
  }

  async findById(id: string): Promise<User | null> {
    this.validateId(id);

    const cached = this.cache.get(id);
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data;
    }

    const user = await this.userRepository.findById(id);
    if (user) {
      this.cache.set(id, { data: user, timestamp: Date.now(), ttl: CACHE_TTL });
    }

    return user;
  }

  private validateCreateRequest(data: CreateUserRequest): void {
    if (!data.name || data.name.trim().length < 2) {
      throw new ValidationError('Name must be at least 2 characters');
    }

    if (!isValidEmail(data.email)) {
      throw new ValidationError('Invalid email address');
    }

    if (data.age !== undefined && (data.age < 0 || data.age > 150)) {
      throw new ValidationError('Age must be between 0 and 150');
    }
  }
}
```

### Access Modifiers and Readonly

Use appropriate access modifiers and readonly where applicable:

```typescript
class ApiClient {
  private readonly baseUrl: string;
  private readonly timeout: number;
  private readonly headers: Record<string, string>;
  protected readonly logger: Logger;
  public readonly version: string;

  constructor(config: ApiClientConfig) {
    this.baseUrl = config.baseUrl;
    this.timeout = config.timeout ?? 5000;
    this.headers = { ...config.defaultHeaders };
    this.logger = new Logger('ApiClient');
    this.version = '1.0.0';
  }

  public async get<T>(endpoint: string, params?: Record<string, unknown>): Promise<T> {
    return this.request<T>('GET', endpoint, undefined, params);
  }

  public async post<T>(endpoint: string, data?: unknown): Promise<T> {
    return this.request<T>('POST', endpoint, data);
  }

  private async request<T>(
    method: HttpMethod,
    endpoint: string,
    data?: unknown,
    params?: Record<string, unknown>
  ): Promise<T> {
    const url = this.buildUrl(endpoint, params);
    const options = this.buildRequestOptions(method, data);

    try {
      const response = await fetch(url, options);
      return this.handleResponse<T>(response);
    } catch (error) {
      this.logger.error('Request failed', { method, endpoint, error });
      throw new NetworkError(`Request failed: ${error.message}`);
    }
  }

  private buildUrl(endpoint: string, params?: Record<string, unknown>): string {
    const url = new URL(endpoint, this.baseUrl);

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value));
        }
      });
    }

    return url.toString();
  }
}
```

## Functions and Methods

### Function Signatures

Use explicit type annotations for function parameters and return types:

```typescript
// Function declarations with types
function calculateTax(amount: number, rate: number): number {
  if (amount < 0 || rate < 0 || rate > 1) {
    throw new ValidationError('Invalid tax calculation parameters');
  }
  return amount * rate;
}

// Arrow functions with types
const formatCurrency = (amount: number, currency: string = 'USD'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency
  }).format(amount);
};

// Async functions
async function fetchUserData(userId: string): Promise<User> {
  const response = await fetch(`/api/users/${userId}`);

  if (!response.ok) {
    throw new NetworkError(`Failed to fetch user: ${response.statusText}`);
  }

  const userData = await response.json() as User;
  return userData;
}

// Function overloads
function processData(data: string): string;
function processData(data: number): number;
function processData(data: boolean): boolean;
function processData(data: string | number | boolean): string | number | boolean {
  if (typeof data === 'string') {
    return data.trim().toLowerCase();
  }

  if (typeof data === 'number') {
    return Math.round(data * 100) / 100;
  }

  return data;
}
```

### Higher-Order Functions

```typescript
// Generic higher-order functions
function createValidator<T>(
  validationRules: ValidationRule<T>[]
): (value: T) => ValidationResult {
  return (value: T): ValidationResult => {
    for (const rule of validationRules) {
      const result = rule.validate(value);
      if (!result.isValid) {
        return result;
      }
    }
    return { isValid: true };
  };
}

// Function composition
function compose<T, U, V>(
  f: (x: U) => V,
  g: (x: T) => U
): (x: T) => V {
  return (x: T): V => f(g(x));
}

// Curried functions
function createApiCall<TRequest, TResponse>(
  endpoint: string
): (client: ApiClient) => (data: TRequest) => Promise<TResponse> {
  return (client: ApiClient) =>
    (data: TRequest): Promise<TResponse> =>
      client.post<TResponse>(endpoint, data);
}

// Usage
const createUser = createApiCall<CreateUserRequest, User>('/users');
const userCreator = createUser(apiClient);
```

## Generics

### Generic Functions

```typescript
// Basic generic functions
function identity<T>(value: T): T {
  return value;
}

function toArray<T>(value: T | T[]): T[] {
  return Array.isArray(value) ? value : [value];
}

// Generic functions with constraints
function getProperty<T, K extends keyof T>(obj: T, key: K): T[K] {
  return obj[key];
}

function updateProperty<T, K extends keyof T>(
  obj: T,
  key: K,
  value: T[K]
): T {
  return { ...obj, [key]: value };
}

// Multiple type parameters
function merge<T, U>(obj1: T, obj2: U): T & U {
  return { ...obj1, ...obj2 };
}

function pick<T, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> {
  const result = {} as Pick<T, K>;
  keys.forEach(key => {
    result[key] = obj[key];
  });
  return result;
}
```

### Generic Classes

```typescript
class Cache<T> {
  private readonly storage = new Map<string, CacheEntry<T>>();
  private readonly defaultTtl: number;

  constructor(defaultTtl: number = 300000) {
    this.defaultTtl = defaultTtl;
  }

  set(key: string, value: T, ttl?: number): void {
    const entry: CacheEntry<T> = {
      data: value,
      timestamp: Date.now(),
      ttl: ttl ?? this.defaultTtl
    };
    this.storage.set(key, entry);
  }

  get(key: string): T | null {
    const entry = this.storage.get(key);

    if (!entry) {
      return null;
    }

    if (Date.now() - entry.timestamp > entry.ttl) {
      this.storage.delete(key);
      return null;
    }

    return entry.data;
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  clear(): void {
    this.storage.clear();
  }

  size(): number {
    return this.storage.size;
  }
}

// Generic repository pattern
abstract class Repository<T extends BaseEntity, TCreate, TUpdate> {
  protected abstract tableName: string;
  protected abstract db: DatabaseConnection;

  async findById(id: string): Promise<T | null> {
    const query = `SELECT * FROM ${this.tableName} WHERE id = ?`;
    const result = await this.db.query<T>(query, [id]);
    return result[0] ?? null;
  }

  async create(data: TCreate): Promise<T> {
    const id = generateId();
    const now = new Date();
    const entity = {
      ...data,
      id,
      createdAt: now,
      updatedAt: now
    } as T;

    const query = this.buildInsertQuery(entity);
    await this.db.execute(query.sql, query.params);

    return entity;
  }

  protected abstract buildInsertQuery(entity: T): { sql: string; params: unknown[] };
}
```

## Modules and Imports

### Type-Only Imports

Use `import type` for type-only imports to improve build performance:

```typescript
// ------------ IMPORTS
// Type-only imports
import type { User, UserRole, UserPreferences } from '../types/user';
import type { ApiResponse, PaginatedResponse } from '../types/api';
import type { DatabaseConnection, QueryResult } from '../types/database';

// Value imports
import { ApiClient } from '../clients/api-client';
import { Logger } from '../utils/logger';
import { ValidationError } from '../errors/custom-errors';
```

### Re-exports

Organize exports for clean module interfaces:

```typescript
// ------------ EXPORTS
// Re-export types
export type {
  User,
  CreateUserRequest,
  UpdateUserRequest,
  UserServiceConfig
} from './types';

// Re-export classes
export { UserService } from './user-service';
export { UserRepository } from './user-repository';
export { UserValidator } from './user-validator';

// Default export
export { UserService as default } from './user-service';
```

### Barrel Exports

Create index files for clean imports:

```typescript
/**
 * @file INDEX.TS
 * @version 1.0.0
 * <AUTHOR>
 * @contributors
 *
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * Barrel export file for user module.
 * Provides clean imports for external consumers.
 */

// ------------ EXPORTS
export type {
  User,
  CreateUserRequest,
  UpdateUserRequest,
  UserFilters,
  UserServiceConfig
} from './types/user';

export {
  UserService,
  UserRepository,
  UserValidator
} from './services';

export {
  ValidationError,
  NotFoundError,
  ConflictError
} from './errors';

export { UserService as default } from './services/user-service';
```

## Error Handling

### Typed Error Classes

Create strongly typed error classes:

```typescript
// ------------ ERROR CLASSES
abstract class BaseError extends Error {
  abstract readonly code: string;
  abstract readonly statusCode: number;
  readonly timestamp: Date;

  constructor(message: string, public readonly context?: Record<string, unknown>) {
    super(message);
    this.name = this.constructor.name;
    this.timestamp = new Date();
    Error.captureStackTrace(this, this.constructor);
  }

  toJSON(): Record<string, unknown> {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      statusCode: this.statusCode,
      timestamp: this.timestamp,
      context: this.context
    };
  }
}

class ValidationError extends BaseError {
  readonly code = 'VALIDATION_ERROR';
  readonly statusCode = 400;

  constructor(message: string, public readonly field?: string) {
    super(message, { field });
  }
}

class NotFoundError extends BaseError {
  readonly code = 'NOT_FOUND';
  readonly statusCode = 404;

  constructor(resource: string, id: string) {
    super(`${resource} with ID ${id} not found`, { resource, id });
  }
}

class ConflictError extends BaseError {
  readonly code = 'CONFLICT';
  readonly statusCode = 409;
}

class NetworkError extends BaseError {
  readonly code = 'NETWORK_ERROR';
  readonly statusCode = 500;

  constructor(message: string, public readonly originalError?: Error) {
    super(message, { originalError: originalError?.message });
  }
}
```

### Result Pattern

Use Result pattern for better error handling:

```typescript
// ------------ RESULT PATTERN
type Result<T, E = Error> = Success<T> | Failure<E>;

interface Success<T> {
  readonly success: true;
  readonly data: T;
}

interface Failure<E> {
  readonly success: false;
  readonly error: E;
}

function success<T>(data: T): Success<T> {
  return { success: true, data };
}

function failure<E>(error: E): Failure<E> {
  return { success: false, error };
}

// Usage in functions
async function fetchUser(id: string): Promise<Result<User, NotFoundError | NetworkError>> {
  try {
    const response = await fetch(`/api/users/${id}`);

    if (response.status === 404) {
      return failure(new NotFoundError('User', id));
    }

    if (!response.ok) {
      return failure(new NetworkError(`HTTP ${response.status}: ${response.statusText}`));
    }

    const user = await response.json() as User;
    return success(user);
  } catch (error) {
    return failure(new NetworkError('Network request failed', error as Error));
  }
}

// Usage with Result pattern
async function handleUserFetch(id: string): Promise<void> {
  const result = await fetchUser(id);

  if (result.success) {
    console.log('User found:', result.data.name);
  } else {
    console.error('Failed to fetch user:', result.error.message);
  }
}
```

## Asynchronous Code

### Promise Handling with Types

```typescript
// Typed promise utilities
async function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number,
  timeoutMessage: string = 'Operation timed out'
): Promise<T> {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error(timeoutMessage)), timeoutMs)
    )
  ]);
}

async function retry<T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;

      if (attempt === maxAttempts) {
        throw lastError;
      }

      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw lastError!;
}

// Parallel execution with proper typing
async function fetchUserData(userId: string): Promise<{
  user: User;
  preferences: UserPreferences;
  permissions: Permission[];
}> {
  const [user, preferences, permissions] = await Promise.all([
    fetchUser(userId),
    fetchUserPreferences(userId),
    fetchUserPermissions(userId)
  ]);

  return { user, preferences, permissions };
}
```

### Async Iterators

```typescript
// Typed async generators
async function* fetchUsersInBatches(
  batchSize: number = 100
): AsyncGenerator<User[], void, unknown> {
  let offset = 0;
  let hasMore = true;

  while (hasMore) {
    const users = await fetchUsersBatch(offset, batchSize);

    if (users.length === 0) {
      hasMore = false;
    } else {
      yield users;
      offset += batchSize;
      hasMore = users.length === batchSize;
    }
  }
}

// Usage
async function processAllUsers(): Promise<void> {
  for await (const userBatch of fetchUsersInBatches(50)) {
    await Promise.all(userBatch.map(user => processUser(user)));
  }
}
```

## Comments and Documentation

### Comment Guidelines

**No inline documentation** - Use section comments and minimal explanatory comments only:

```typescript
// ------------ HELPER FUNCTIONS

// Calculate compound interest using the standard formula
function calculateCompoundInterest(
  principal: number,
  rate: number,
  time: number,
  frequency: number
): number {
  return principal * Math.pow(1 + rate / frequency, frequency * time);
}

// Validate email using RFC 5322 compliant regex
function isValidEmail(email: string): boolean {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email) && email.length <= 254;
}

// ------------ PAYMENT PROCESSOR CLASS IMPLEMENTATION

class PaymentProcessor {
  private readonly config: PaymentConfig;
  private readonly rateLimiter: RateLimiter;

  constructor(config: PaymentConfig) {
    this.config = config;
    this.rateLimiter = new RateLimiter(100, 60000); // 100 requests per minute
  }

  async processPayment(payment: PaymentRequest): Promise<PaymentResult> {
    // Validate payment data before processing
    const validation = this.validatePayment(payment);
    if (!validation.isValid) {
      throw new ValidationError(validation.error, validation.field);
    }

    return this.rateLimiter.execute(() => this.executePayment(payment));
  }
}
```

### Type Documentation

Use TypeScript's type system for documentation:

```typescript
// ------------ TYPES AND INTERFACES

/**
 * User entity representing a system user
 */
interface User {
  /** Unique identifier for the user */
  readonly id: string;

  /** User's full name */
  name: string;

  /** User's email address (must be unique) */
  email: string;

  /** User's age in years */
  age: number;

  /** List of roles assigned to the user */
  roles: UserRole[];

  /** User's personal preferences */
  preferences: UserPreferences;

  /** Timestamp when the user was created */
  readonly createdAt: Date;

  /** Timestamp when the user was last updated */
  readonly updatedAt: Date;
}

/**
 * Configuration options for UserService
 */
interface UserServiceConfig {
  /** API client for making HTTP requests */
  apiClient: ApiClient;

  /** Whether to enable caching (default: true) */
  cacheEnabled?: boolean;

  /** Default page size for pagination (default: 20) */
  defaultPageSize?: number;

  /** Cache TTL in milliseconds (default: 300000) */
  cacheTtl?: number;
}
```

## Code Quality and Linting

### TypeScript Configuration

Follow the organization's TypeScript configuration as defined in `docs/development/code-quality/tsconfig.md`:

```typescript
// Key TypeScript compiler options to follow:
// - strict: true (enable all strict type checking)
// - noImplicitAny: true (error on implicit any types)
// - strictNullChecks: true (strict null and undefined checks)
// - noImplicitReturns: true (error on missing return statements)
// - noUnusedLocals: true (error on unused local variables)
// - noUnusedParameters: true (error on unused parameters)
```

### ESLint TypeScript Rules

Follow the organization's ESLint TypeScript configuration:

```typescript
// Key TypeScript ESLint rules to follow:
// - @typescript-eslint/no-explicit-any: warn
// - @typescript-eslint/no-unused-vars: warn
// - @typescript-eslint/prefer-as-const: error
// - @typescript-eslint/consistent-type-imports: warn
// - @typescript-eslint/no-non-null-assertion: warn
```

### Type Safety Best Practices

```typescript
// Good: Use type guards
function isUser(obj: unknown): obj is User {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'name' in obj &&
    'email' in obj
  );
}

// Good: Use assertion functions
function assertIsUser(obj: unknown): asserts obj is User {
  if (!isUser(obj)) {
    throw new ValidationError('Object is not a valid User');
  }
}

// Good: Use discriminated unions
type ApiResponse<T> =
  | { success: true; data: T }
  | { success: false; error: string };

function handleResponse<T>(response: ApiResponse<T>): T {
  if (response.success) {
    return response.data; // TypeScript knows this is T
  } else {
    throw new Error(response.error); // TypeScript knows this has error
  }
}

// Good: Use branded types for type safety
type UserId = string & { readonly brand: unique symbol };
type Email = string & { readonly brand: unique symbol };

function createUserId(id: string): UserId {
  if (!id || id.length === 0) {
    throw new ValidationError('Invalid user ID');
  }
  return id as UserId;
}

function createEmail(email: string): Email {
  if (!isValidEmail(email)) {
    throw new ValidationError('Invalid email address');
  }
  return email as Email;
}
```

## Testing Standards

### Test File Structure

```typescript
/**
 * @file USER-SERVICE.TEST.TS
 *
 * @version 1.0.0
 * <AUTHOR>
 * @contributors
 *
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * Unit tests for UserService class.
 * Tests user creation, validation, and data management functionality.
 */

// ------------ IMPORTS
import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';
import type { User, CreateUserRequest } from '../types/user';
import { UserService } from '../user-service';
import { ApiClient } from '../clients/api-client';
import { ValidationError, ConflictError } from '../errors/custom-errors';

// ------------ TEST SETUP
describe('UserService', () => {
  let userService: UserService;
  let mockApiClient: jest.Mocked<ApiClient>;

  beforeEach(() => {
    mockApiClient = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn()
    } as jest.Mocked<ApiClient>;

    userService = new UserService({
      apiClient: mockApiClient,
      cacheEnabled: true,
      defaultPageSize: 20
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  // ------------ CONSTRUCTOR TESTS
  describe('constructor', () => {
    test('should initialize with provided configuration', () => {
      expect(userService).toBeInstanceOf(UserService);
      expect(userService.cacheSize).toBe(0);
    });

    test('should use default configuration values', () => {
      const service = new UserService({ apiClient: mockApiClient });
      expect(service).toBeInstanceOf(UserService);
    });
  });

  // ------------ METHOD TESTS
  describe('createUser', () => {
    const validUserRequest: CreateUserRequest = {
      name: 'John Doe',
      email: '<EMAIL>',
      age: 30
    };

    test('should create user with valid data', async () => {
      const expectedUser: User = {
        id: '123',
        ...validUserRequest,
        roles: [],
        preferences: { theme: 'light', notifications: true, language: 'en' },
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockApiClient.post.mockResolvedValue(expectedUser);

      const result = await userService.createUser(validUserRequest);

      expect(mockApiClient.post).toHaveBeenCalledWith('/users', validUserRequest);
      expect(result).toEqual(expectedUser);
      expect(userService.cacheSize).toBe(1);
    });

    test('should throw ValidationError for invalid email', async () => {
      const invalidRequest = {
        ...validUserRequest,
        email: 'invalid-email'
      };

      await expect(userService.createUser(invalidRequest))
        .rejects
        .toThrow(ValidationError);

      expect(mockApiClient.post).not.toHaveBeenCalled();
    });

    test('should throw ValidationError for short name', async () => {
      const invalidRequest = {
        ...validUserRequest,
        name: 'A'
      };

      await expect(userService.createUser(invalidRequest))
        .rejects
        .toThrow(ValidationError);

      expect(mockApiClient.post).not.toHaveBeenCalled();
    });
  });

  describe('findById', () => {
    test('should return user from cache if available', async () => {
      const user: User = {
        id: '123',
        name: 'John Doe',
        email: '<EMAIL>',
        age: 30,
        roles: [],
        preferences: { theme: 'light', notifications: true, language: 'en' },
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // First call to populate cache
      mockApiClient.get.mockResolvedValue(user);
      await userService.findById('123');

      // Second call should use cache
      const result = await userService.findById('123');

      expect(result).toEqual(user);
      expect(mockApiClient.get).toHaveBeenCalledTimes(1);
    });

    test('should throw ValidationError for invalid ID', async () => {
      await expect(userService.findById(''))
        .rejects
        .toThrow(ValidationError);

      expect(mockApiClient.get).not.toHaveBeenCalled();
    });
  });
});
```

### Type Testing

```typescript
// Type-only tests for compile-time validation
type TestUser = {
  id: string;
  name: string;
  email: string;
};

// Test that types are assignable
const testTypeAssignability = () => {
  // This should compile without errors
  const user: User = {} as TestUser; // This will fail if types don't match

  // Test generic constraints
  const cache = new Cache<User>();
  cache.set('test', user); // Should work

  // Test function overloads
  const stringResult = processData('test'); // Should be string
  const numberResult = processData(42); // Should be number
};
```

## Performance Guidelines

### Type-Level Performance

```typescript
// Good: Use const assertions for better performance
const STATUSES = ['active', 'inactive', 'pending'] as const;
type Status = typeof STATUSES[number];

// Good: Use template literal types efficiently
type EventName<T extends string> = `on${Capitalize<T>}`;

// Good: Use mapped types for transformations
type Optional<T> = {
  [K in keyof T]?: T[K];
};

// Avoid: Complex recursive types that can slow compilation
// type DeepReadonly<T> = {
//   readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
// };
```

### Runtime Performance

```typescript
// Good: Use type guards for efficient runtime checks
function isString(value: unknown): value is string {
  return typeof value === 'string';
}

// Good: Use object freezing for immutable data
const createImmutableConfig = <T extends Record<string, unknown>>(config: T): Readonly<T> => {
  return Object.freeze({ ...config });
};

// Good: Use efficient data structures
class TypedMap<K extends string | number | symbol, V> {
  private readonly data = {} as Record<K, V>;

  set(key: K, value: V): void {
    this.data[key] = value;
  }

  get(key: K): V | undefined {
    return this.data[key];
  }

  has(key: K): boolean {
    return key in this.data;
  }
}
```

## Security Considerations

### Type-Safe Input Validation

```typescript
// Use schema validation with TypeScript
import { z } from 'zod';

const UserSchema = z.object({
  name: z.string().min(2).max(100),
  email: z.string().email().max(254),
  age: z.number().int().min(0).max(150),
  roles: z.array(z.string()).optional().default([])
});

type ValidatedUser = z.infer<typeof UserSchema>;

function validateUserInput(input: unknown): ValidatedUser {
  try {
    return UserSchema.parse(input);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ValidationError(`Validation failed: ${error.message}`);
    }
    throw error;
  }
}

// Type-safe environment variable handling
const EnvSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']),
  DATABASE_URL: z.string().url(),
  API_KEY: z.string().min(1),
  PORT: z.string().transform(Number).pipe(z.number().int().positive())
});

type Environment = z.infer<typeof EnvSchema>;

function getEnvironment(): Environment {
  return EnvSchema.parse(process.env);
}
```

### Secure Type Patterns

```typescript
// Opaque types for sensitive data
type SensitiveData = string & { readonly __brand: 'SensitiveData' };
type HashedPassword = string & { readonly __brand: 'HashedPassword' };

function createSensitiveData(data: string): SensitiveData {
  // Sanitize and validate
  return data as SensitiveData;
}

function hashPassword(password: string): HashedPassword {
  // Use proper hashing algorithm
  return bcrypt.hashSync(password, 12) as HashedPassword;
}

// Prevent prototype pollution with strict typing
function safeAssign<T extends Record<string, unknown>>(
  target: T,
  source: Partial<T>
): T {
  const safeKeys = Object.keys(source).filter(
    (key): key is keyof T =>
      key !== '__proto__' &&
      key !== 'constructor' &&
      key !== 'prototype' &&
      key in target
  );

  const result = { ...target };
  safeKeys.forEach(key => {
    if (source[key] !== undefined) {
      result[key] = source[key] as T[keyof T];
    }
  });

  return result;
}
```

## References

- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [TypeScript ESLint Rules](https://typescript-eslint.io/rules/)
- [TypeScript Deep Dive](https://basarat.gitbook.io/typescript/)
- [Effective TypeScript](https://effectivetypescript.com/)
- [TypeScript Style Guide](https://ts.dev/style/)

> TypeScript code standards are enforced through TypeScript compiler settings, ESLint configuration, and code reviews. All developers must follow these standards to maintain code quality, type safety, and consistency across the Dynamic Innovative Studio organization.
