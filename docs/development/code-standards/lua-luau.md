# Luau Code Standards

This document outlines the Luau (Roblox Lua) coding standards and conventions that should be followed across all projects within the Dynamic Innovative Studio organization. These standards are designed to ensure consistency, readability, quality, and maintainability of the Luau codebase while following modern Luau best practices and Roblox development patterns.

## Table of Contents

- [Luau Code Standards](#luau-code-standards)
  - [Table of Contents](#table-of-contents)
  - [Overview](#overview)
    - [Core Principles](#core-principles)
  - [File Structure and Headers](#file-structure-and-headers)
    - [File Header Format](#file-header-format)
    - [Header Guidelines](#header-guidelines)
    - [File Organization Structure](#file-organization-structure)
  - [Code Organization](#code-organization)
    - [Section Comments](#section-comments)
    - [Service and Module Organization](#service-and-module-organization)
    - [Constants Declaration](#constants-declaration)
  - [Naming Conventions](#naming-conventions)
    - [Variables and Functions](#variables-and-functions)
    - [Constants](#constants)
    - [Classes and Types](#classes-and-types)
    - [Private Members](#private-members)
  - [Variable Declarations](#variable-declarations)
    - [Modern lua \& luau Variable Declarations](#modern-lua--luau-variable-declarations)
    - [Type Safety and Assertions](#type-safety-and-assertions)
  - [Functions and Methods](#functions-and-methods)
    - [Function Signatures and Type Annotations](#function-signatures-and-type-annotations)
    - [Method Organization](#method-organization)
  - [Classes and Objects](#classes-and-objects)
    - [Class Design Patterns](#class-design-patterns)
    - [Inheritance Patterns](#inheritance-patterns)
  - [Type Annotations](#type-annotations)
    - [Advanced Type Definitions](#advanced-type-definitions)
    - [Type Guards and Narrowing](#type-guards-and-narrowing)
  - [Modules and Requires](#modules-and-requires)
    - [Module Structure and Exports](#module-structure-and-exports)
  - [Error Handling](#error-handling)
    - [Error Handling Patterns](#error-handling-patterns)
  - [Roblox-Specific Patterns](#roblox-specific-patterns)
    - [Service Management](#service-management)
    - [RemoteEvent and RemoteFunction Patterns](#remoteevent-and-remotefunction-patterns)
  - [Comments and Documentation](#comments-and-documentation)
    - [Documentation Standards](#documentation-standards)
    - [Comment Guidelines](#comment-guidelines)
  - [Code Quality and Linting](#code-quality-and-linting)
    - [Lua \& luau Code Quality Tools](#lua--luau-code-quality-tools)
    - [Code Quality Practices](#code-quality-practices)
  - [Testing Standards](#testing-standards)
    - [Test File Structure](#test-file-structure)
  - [Performance Guidelines](#performance-guidelines)
    - [Optimization Techniques](#optimization-techniques)
  - [Security Considerations](#security-considerations)
    - [Secure Coding Practices](#secure-coding-practices)
  - [References](#references)

## Overview

Luau is a fast, small, safe, gradually typed embeddable scripting language derived from Lua. It is the primary scripting language for Roblox development. These standards ensure that Luau code written within the DIS organization maintains high quality, consistency, and follows modern Luau and Roblox best practices.

### Core Principles

- **Type Safety**: Use Luau's type system for better code reliability and performance
- **Readability**: Write code that is easy to read and understand
- **Consistency**: Maintain consistent coding style across all Luau files
- **Performance**: Follow Roblox performance best practices
- **Security**: Implement secure coding practices for Roblox environments
- **Modern Luau**: Use modern Luau features and syntax
- **Roblox Integration**: Follow Roblox-specific patterns and conventions

## File Structure and Headers

### File Header Format

All Luau files must include a standardized header comment block at the top of the file:

```lua
--[[
 - file: FILENAME.LUAU

 - version: 1.0.0
 - author: BleckWolf25
 - contributors:

 - copyright: Dynamic Innovative Studio

 - description:
   - Brief description of the file's purpose and functionality.
   - Additional details about the module or component.
]]
```

### Header Guidelines

- **file**: Use UPPERCASE filename with .LUAU extension
- **version**: Follow semantic versioning (MAJOR.MINOR.PATCH)
- **author**: Primary author of the file
- **contributors**: List additional contributors (one per line)
- **copyright**: Always "Dynamic Innovative Studio"
- **description**: Clear, concise description of file purpose

### File Organization Structure

```lua
--[[
 - file: USER_SERVICE.SERVER.luau

 - version: 1.0.0
 - author: BleckWolf25
 - contributors:

 - copyright: Dynamic Innovative Studio

 - description:
   - User service for managing user data and operations.
   - Provides CRUD operations and user validation functionality.
   - Utilizes caching and database connection pooling for performance optimization.
   - Implements rate limiting to prevent abuse and ensure fair usage.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local HttpService = game:GetService("HttpService")

-- ============================================================================
-- MODULES
-- ============================================================================
local Types = require(ReplicatedStorage.Shared.Types)
local Utilities = require(ReplicatedStorage.Shared.Utilities)
local ValidationModule = require(ReplicatedStorage.Shared.Validation)

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local DEFAULT_PAGE_SIZE = 20
local MAX_PAGE_SIZE = 100
local CACHE_TTL_SECONDS = 300

-- ============================================================================
-- TYPES
-- ============================================================================
export type User = Types.User
export type UserData = Types.UserData
export type UserService = {
 __index: UserService,
 new: () -> UserService,
 createUser: (self: UserService, userData: UserData) -> User,
 getUserById: (self: UserService, userId: string) -> User?,
}

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================
local function generateUserId(): string
    return HttpService:GenerateGUID(false)
end

local function formatUserName(firstName: string, lastName: string): string
    return string.format("%s %s", string.gsub(firstName, "^%s*(.-)%s*$", "%1"), string.gsub(lastName, "^%s*(.-)%s*$", "%1"))
end

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local UserService = {} :: UserService
UserService.__index = UserService

function UserService.new(): UserService
 local self = setmetatable({}, UserService)
 self._cache = {}
 self._connections = {}
 return self
end

function UserService:createUser(userData: UserData): User
 -- Implementation
 local userId = generateUserId()
 local user: User = {
  id = userId,
  name = formatUserName(userData.firstName, userData.lastName),
  email = userData.email,
  createdAt = DateTime.now().UnixTimestamp,
 }

 self._cache[userId] = user
 return user
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return UserService
```

## Code Organization

### Section Comments

Use standardized section comments to organize code within files:

```lua
-- ============================================================================
-- SERVICES
-- ============================================================================

-- ============================================================================
-- MODULES
-- ============================================================================

-- ============================================================================
-- CONSTANTS
-- ============================================================================

-- ============================================================================
-- TYPES
-- ============================================================================

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================

-- ============================================================================
-- EXPORTS
-- ============================================================================
```

### Service and Module Organization

Organize services and module requires in the following order:

1. **Roblox Services**
2. **Third-party modules**
3. **Project-specific modules**

```lua
-- ============================================================================
-- SERVICES
-- ============================================================================
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local HttpService = game:GetService("HttpService")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")
local Lighting = game:GetService("Lighting")

-- ============================================================================
-- MODULES
-- ============================================================================
-- Third-party modules
local Roact = require(ReplicatedStorage.Packages.Roact)
local Rodux = require(ReplicatedStorage.Packages.Rodux)
local Promise = require(ReplicatedStorage.Packages.Promise)

-- Project modules
local Types = require(ReplicatedStorage.Shared.Types)
local Config = require(ReplicatedStorage.Shared.Config)
local Utilities = require(ReplicatedStorage.Shared.Utilities)
local ValidationModule = require(ReplicatedStorage.Shared.Validation)
local NetworkModule = require(ReplicatedStorage.Shared.Network)
```

### Constants Declaration

Define constants at the module level after requires:

```lua
-- ============================================================================
-- CONSTANTS
-- ============================================================================
local API_VERSION = "v1"
local MAX_FILE_SIZE_MB = 10
local SUPPORTED_IMAGE_FORMATS = {"jpg", "jpeg", "png", "gif", "webp"}
local DEFAULT_TIMEOUT_SECONDS = 30

-- Error messages
local ERROR_MESSAGES = {
 INVALID_EMAIL = "Invalid email address format",
 PASSWORD_TOO_SHORT = "Password must be at least 8 characters",
 USER_NOT_FOUND = "User not found",
 UNAUTHORIZED = "Unauthorized access",
}

-- UI Constants
local UI_CONSTANTS = {
 BUTTON_SIZE = UDim2.new(0, 200, 0, 50),
 PADDING = UDim.new(0, 10),
 ANIMATION_DURATION = 0.3,
 COLORS = {
  PRIMARY = Color3.fromRGB(52, 152, 219),
  SECONDARY = Color3.fromRGB(155, 89, 182),
  SUCCESS = Color3.fromRGB(46, 204, 113),
  ERROR = Color3.fromRGB(231, 76, 60),
 },
}
```

## Naming Conventions

### Variables and Functions

Use **camelCase** for variables and functions:

```lua
-- Variables
local userName = "john_doe"
local isAuthenticated = true
local userPreferences = {}
local totalCount = 0

-- Functions
local function calculateTotalPrice(items: {Item}): number
 local total = 0
 for _, item in items do
  total += item.price
 end
 return total
end

local function validateUserInput(userData: UserData): boolean
 -- Implementation
 return true
end

local function fetchUserDataAsync(userId: string): Promise.Promise<User?>
 -- Implementation
 return Promise.resolve(nil)
end
```

### Constants

Use **SCREAMING_SNAKE_CASE** for constants:

```lua
local MAX_RETRY_ATTEMPTS = 3
local API_BASE_URL = "https://api.example.com"
local DEFAULT_PAGE_SIZE = 20
local CACHE_EXPIRY_SECONDS = 3600
```

### Classes and Types

Use **PascalCase** for classes, types, and modules:

```lua
-- Types
export type UserManager = {
 __index: UserManager,
 new: (databaseUrl: string) -> UserManager,
 createUser: (self: UserManager, userData: UserData) -> User,
}

export type ApiClient = {
 __index: ApiClient,
 new: (baseUrl: string, timeout: number?) -> ApiClient,
 get: (self: ApiClient, endpoint: string) -> Promise.Promise<any>,
}

export type DatabaseConnection = {
 connectionString: string,
 maxConnections: number,
 timeout: number,
}

-- Enums
export type UserStatus = "Active" | "Inactive" | "Pending" | "Suspended"
```

### Private Members

Use underscore prefix for private members:

```lua
local DataProcessor = {}
DataProcessor.__index = DataProcessor

function DataProcessor.new(cacheSize: number): DataProcessor
 local self = setmetatable({}, DataProcessor)

 -- Private members with underscore prefix
 self._cache = {}
 self._secretKey = "secret"
 self._connections = {}

 -- Public members
 self.publicData = {}

 return self
end

-- Private methods
function DataProcessor:_validateData(data: any): boolean
 return type(data) == "table"
end

function DataProcessor:_encryptData(data: string): string
 -- Implementation
 return data
end

-- Public methods
function DataProcessor:processData(data: any): any
 if not self:_validateData(data) then
  error("Invalid data format")
 end
    return self:_encryptData(tostring(data))
end
```

## Variable Declarations

### Modern lua & luau Variable Declarations

Use proper type annotations and modern lua & luau features:

```lua
-- Basic type annotations
local userName: string = "john_doe"
local isAuthenticated: boolean = true
local userCount: number = 0
local userData: UserData? = nil

-- Table types
local userIds: {string} = {}
local userPreferences: {[string]: any} = {}
local coordinates: {x: number, y: number} = {x = 0, y = 0}

-- Function types
local validator: (UserData) -> boolean = function(data)
    return data.name ~= nil and data.email ~= nil
end

-- Optional and union types
local user: User? = nil
local result: string | number = "success"
local data: UserData? = nil

-- Array and dictionary types
local items: {Item} = {}
local settings: {[string]: string} = {
    theme = "dark",
    language = "en"
}

-- Proper nil checking
local function getUserDisplayName(user: User?): string
    if user == nil then
        return "Anonymous"
    end

    if user.name then
        return user.name
    end

    return user.email or "Unknown User"
end
```

### Type Safety and Assertions

Use type assertions and guards for better type safety:

```lua
-- Type assertions
local function processUserData(data: any): User
    assert(type(data) == "table", "User data must be a table")
    assert(type(data.name) == "string", "User name must be a string")
    assert(type(data.email) == "string", "User email must be a string")

    return data :: User
end

-- Type guards
local function isUser(value: any): boolean
    return type(value) == "table"
        and type(value.id) == "string"
        and type(value.name) == "string"
        and type(value.email) == "string"
end

local function assertIsUser(value: any): User
    if not isUser(value) then
        error("Value is not a valid User")
    end
    return value :: User
end

-- Safe property access
local function getUserAge(user: User): number?
    if user.profile and user.profile.age then
        return user.profile.age
    end
    return nil
end
```

## Functions and Methods

### Function Signatures and Type Annotations

Use clear function signatures with proper type annotations:

```lua
-- Basic function with types
local function calculateDiscount(price: number, discountPercent: number): number
    if price < 0 or discountPercent < 0 or discountPercent > 100 then
        error("Invalid input parameters")
    end
    return price * (discountPercent / 100)
end

-- Function with optional parameters
local function createUser(name: string, email: string, age: number?): User
    return {
        id = HttpService:GenerateGUID(false),
        name = name,
        email = email,
        age = age or 18,
        createdAt = DateTime.now().UnixTimestamp
    }
end

-- Higher-order functions
local function mapArray<T, U>(array: {T}, mapper: (T) -> U): {U}
    local result: {U} = {}
    for i, item in array do
        result[i] = mapper(item)
    end
    return result
end

-- Async functions with Promise
local function fetchUserProfileAsync(userId: string): Promise.Promise<UserProfile?>
    return Promise.new(function(resolve, reject)
        local success, result = pcall(function()
            -- Simulate API call
            wait(1)
            return {
                userId = userId,
                displayName = "User " .. userId,
                avatar = "rbxasset://textures/face.png"
            }
        end)

        if success then
            resolve(result)
        else
            reject(result)
        end
    end)
end

-- Variadic functions
local function combineStrings(separator: string, ...: string): string
    local args = {...}
    return table.concat(args, separator)
end
```

### Method Organization

Organize class methods in a consistent order:

```lua
local UserRepository = {}
UserRepository.__index = UserRepository

export type UserRepository = typeof(setmetatable({} :: {
    _users: {[string]: User},
    _connections: {RBXScriptConnection},
}, UserRepository))

-- Constructor
function UserRepository.new(): UserRepository
    local self = setmetatable({}, UserRepository)
    self._users = {}
    self._connections = {}
    return self
end

-- Public methods
function UserRepository:createUser(userData: UserData): User
    local validatedData = self:_validateUserData(userData)
    local user: User = {
        id = HttpService:GenerateGUID(false),
        name = validatedData.name,
        email = validatedData.email,
        createdAt = DateTime.now().UnixTimestamp
    }

    self._users[user.id] = user
    return user
end

function UserRepository:getUserById(userId: string): User?
    return self._users[userId]
end

function UserRepository:updateUser(userId: string, updates: Partial<UserData>): User?
    local user = self._users[userId]
    if not user then
        return nil
    end

    local validatedUpdates = self:_validateUserData(updates, true)
    for key, value in validatedUpdates do
        (user :: any)[key] = value
    end

    return user
end

function UserRepository:deleteUser(userId: string): boolean
    if self._users[userId] then
        self._users[userId] = nil
        return true
    end
    return false
end

-- Cleanup method
function UserRepository:destroy(): ()
    for _, connection in self._connections do
        connection:Disconnect()
    end
    table.clear(self._connections)
    table.clear(self._users)
end

-- Private methods
function UserRepository:_validateUserData(data: UserData, partial: boolean?): UserData
    partial = partial or false

    if not partial then
        assert(data.name, "Name is required")
        assert(data.email, "Email is required")
    end

    if data.email and not self:_isValidEmail(data.email) then
        error("Invalid email format")
    end

    return data
end

function UserRepository:_isValidEmail(email: string): boolean
    return string.match(email, "^[%w%._%+%-]+@[%w%.%-]+%.%w+$") ~= nil
end
```

## Classes and Objects

### Class Design Patterns

Follow modern lua & luau class design patterns:

```lua
-- Basic class structure
local Player = {}
Player.__index = Player

export type Player = typeof(setmetatable({} :: {
    name: string,
    health: number,
    maxHealth: number,
    position: Vector3,
    _connections: {RBXScriptConnection},
}, Player))

function Player.new(name: string, maxHealth: number?): Player
    local self = setmetatable({}, Player)

    self.name = name
    self.maxHealth = maxHealth or 100
    self.health = self.maxHealth
    self.position = Vector3.new(0, 0, 0)
    self._connections = {}

    return self
end

-- Property getters and setters
function Player:getName(): string
    return self.name
end

function Player:getHealth(): number
    return self.health
end

function Player:setHealth(newHealth: number): ()
    self.health = math.clamp(newHealth, 0, self.maxHealth)

    if self.health <= 0 then
        self:_onDeath()
    end
end

function Player:getHealthPercentage(): number
    return self.health / self.maxHealth
end

-- Public methods
function Player:takeDamage(damage: number): ()
    self:setHealth(self.health - damage)
end

function Player:heal(amount: number): ()
    self:setHealth(self.health + amount)
end

function Player:moveTo(position: Vector3): ()
    self.position = position
    self:_onPositionChanged()
end

-- Event handling
function Player:onHealthChanged(callback: (newHealth: number, oldHealth: number) -> ()): RBXScriptConnection
    -- Implementation would connect to a BindableEvent
    local connection = {} :: any
    table.insert(self._connections, connection)
    return connection
end

-- Cleanup
function Player:destroy(): ()
    for _, connection in self._connections do
        connection:Disconnect()
    end
    table.clear(self._connections)
end

-- Private methods
function Player:_onDeath(): ()
    print(self.name .. " has died!")
end

function Player:_onPositionChanged(): ()
    print(self.name .. " moved to " .. tostring(self.position))
end
```

### Inheritance Patterns

Use composition over inheritance when possible:

```lua
-- Component-based design
local HealthComponent = {}
HealthComponent.__index = HealthComponent

export type HealthComponent = typeof(setmetatable({} :: {
    current: number,
    maximum: number,
    onChanged: BindableEvent,
}, HealthComponent))

function HealthComponent.new(maxHealth: number): HealthComponent
    local self = setmetatable({}, HealthComponent)

    self.maximum = maxHealth
    self.current = maxHealth
    self.onChanged = Instance.new("BindableEvent")

    return self
end

function HealthComponent:setHealth(newHealth: number): ()
    local oldHealth = self.current
    self.current = math.clamp(newHealth, 0, self.maximum)

    if self.current ~= oldHealth then
        self.onChanged:Fire(self.current, oldHealth)
    end
end

function HealthComponent:getPercentage(): number
    return self.current / self.maximum
end

function HealthComponent:destroy(): ()
    self.onChanged:Destroy()
end

-- Entity using components
local Entity = {}
Entity.__index = Entity

export type Entity = typeof(setmetatable({} :: {
    id: string,
    name: string,
    health: HealthComponent,
    position: Vector3,
}, Entity))

function Entity.new(name: string, maxHealth: number?): Entity
    local self = setmetatable({}, Entity)

    self.id = HttpService:GenerateGUID(false)
    self.name = name
    self.health = HealthComponent.new(maxHealth or 100)
    self.position = Vector3.new(0, 0, 0)

    -- Connect to health changes
    self.health.onChanged:Connect(function(newHealth, oldHealth)
        self:_onHealthChanged(newHealth, oldHealth)
    end)

    return self
end

function Entity:takeDamage(damage: number): ()
    self.health:setHealth(self.health.current - damage)
end

function Entity:heal(amount: number): ()
    self.health:setHealth(self.health.current + amount)
end

function Entity:destroy(): ()
    self.health:destroy()
end

function Entity:_onHealthChanged(newHealth: number, oldHealth: number): ()
    if newHealth <= 0 then
        print(self.name .. " has been destroyed!")
    end
end
```

## Type Annotations

### Advanced Type Definitions

Use lua & luau's advanced type system features:

```lua
-- Generic types
export type Result<T, E> = {
    success: true,
    value: T
} | {
    success: false,
    error: E
}

export type Optional<T> = T | nil

export type Array<T> = {T}
export type Dictionary<T> = {[string]: T}

-- Function types
export type EventCallback<T> = (data: T) -> ()
export type Validator<T> = (value: any) -> T?
export type Transformer<T, U> = (input: T) -> U

-- Complex type definitions
export type UserPermissions = {
    canRead: boolean,
    canWrite: boolean,
    canDelete: boolean,
    canAdmin: boolean,
}

export type User = {
    id: string,
    name: string,
    email: string,
    permissions: UserPermissions,
    profile: {
        avatar: string?,
        bio: string?,
        joinDate: number,
    },
    settings: {
        theme: "light" | "dark",
        notifications: boolean,
        language: string,
    },
}

-- Utility types (simplified for Luau)
export type Partial<T> = {[string]: any}  -- Simplified partial type for Luau

-- Usage examples
local function createUserResult(userData: UserData): Result<User, string>
    local success, user = pcall(function()
        return createUser(userData)
    end)

    if success then
        return {
            success = true,
            value = user
        }
    else
        return {
            success = false,
            error = tostring(user)
        }
    end
end

local function updateUserPartial(userId: string, updates: Partial<User>): User?
    local user = getUserById(userId)
    if not user then
        return nil
    end

    for key, value in updates do
        (user :: any)[key] = value
    end

    return user
end
```

### Type Guards and Narrowing

Use type guards for runtime type checking:

```lua
-- Type guard functions
local function isString(value: any): boolean
    return type(value) == "string"
end

local function isNumber(value: any): boolean
    return type(value) == "number"
end

local function isUser(value: any): boolean
    return type(value) == "table"
        and type(value.id) == "string"
        and type(value.name) == "string"
        and type(value.email) == "string"
end

local function isValidUserData(value: any): boolean
    return type(value) == "table"
        and isString(value.name)
        and isString(value.email)
        and (value.age == nil or isNumber(value.age))
end

-- Type narrowing with assertions
local function processUserSafely(data: any): User
    if not isValidUserData(data) then
        error("Invalid user data provided")
    end

    -- Type is now narrowed to UserData
    return createUser(data.name, data.email, data.age)
end

-- Pattern matching with type guards
local function handleApiResponse(response: any): string
    if type(response) == "table" then
        if response.success and response.data then
            return "Success: " .. tostring(response.data)
        elseif response.error then
            return "Error: " .. tostring(response.error)
        end
    end

    return "Unknown response format"
end
```

## Modules and Requires

### Module Structure and Exports

Follow consistent module organization patterns:

```lua
--[[
 - file: VALIDATION_MODULE.lua & luau

 - version: 1.0.0
 - author: BleckWolf25
 - contributors:

 - copyright: Dynamic Innovative Studio

 - description:
   - Validation utilities for user input and data processing.
   - Provides type-safe validation functions and error handling.
   - Utilizes advanced type system features for better code quality.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local HttpService = game:GetService("HttpService")

-- ============================================================================
-- MODULES
-- ============================================================================
local Types = require(script.Parent.Types)

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local EMAIL_PATTERN = "^[%w%._%+%-]+@[%w%.%-]+%.%w+$"
local MIN_PASSWORD_LENGTH = 8
local MAX_NAME_LENGTH = 50

-- ============================================================================
-- TYPES
-- ============================================================================
export type ValidationResult<T> = {
    success: true,
    value: T
} | {
    success: false,
    errors: {string}
}

export type EmailValidator = {
    validate: (email: string) -> ValidationResult<string>
}

export type PasswordValidator = {
    validate: (password: string) -> ValidationResult<string>
}

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================
local function isValidEmail(email: string): boolean
    return string.match(email, EMAIL_PATTERN) ~= nil
end

local function isValidPassword(password: string): boolean
    return string.len(password) >= MIN_PASSWORD_LENGTH
end

-- ============================================================================
-- VALIDATORS
-- ============================================================================
local EmailValidator = {}
EmailValidator.__index = EmailValidator

function EmailValidator.new(): EmailValidator
    return setmetatable({}, EmailValidator)
end

function EmailValidator:validate(email: string): ValidationResult<string>
    local errors = {}

    if not email or email == "" then
        table.insert(errors, "Email is required")
    elseif not isValidEmail(email) then
        table.insert(errors, "Invalid email format")
    end

    if #errors > 0 then
        return {
            success = false,
            errors = errors
        }
    end

    return {
        success = true,
        value = string.lower(email)
    }
end

local PasswordValidator = {}
PasswordValidator.__index = PasswordValidator

function PasswordValidator.new(): PasswordValidator
    return setmetatable({}, PasswordValidator)
end

function PasswordValidator:validate(password: string): ValidationResult<string>
    local errors = {}

    if not password or password == "" then
        table.insert(errors, "Password is required")
    elseif not isValidPassword(password) then
        table.insert(errors, "Password must be at least " .. MIN_PASSWORD_LENGTH .. " characters")
    end

    if #errors > 0 then
        return {
            success = false,
            errors = errors
        }
    end

    return {
        success = true,
        value = password
    }
end

-- ============================================================================
-- PUBLIC API
-- ============================================================================
local ValidationModule = {
    EmailValidator = EmailValidator,
    PasswordValidator = PasswordValidator,
}

function ValidationModule.validateUserData(userData: any): ValidationResult<Types.UserData>
    local errors = {}

    -- Validate name
    if not userData.name or userData.name == "" then
        table.insert(errors, "Name is required")
    elseif string.len(userData.name) > MAX_NAME_LENGTH then
        table.insert(errors, "Name is too long")
    end

    -- Validate email
    local emailResult = EmailValidator.new():validate(userData.email)
    if not emailResult.success then
        for _, error in emailResult.errors do
            table.insert(errors, error)
        end
    end

    -- Validate password if provided
    if userData.password then
        local passwordResult = PasswordValidator.new():validate(userData.password)
        if not passwordResult.success then
            for _, error in passwordResult.errors do
                table.insert(errors, error)
            end
        end
    end

    if #errors > 0 then
        return {
            success = false,
            errors = errors
        }
    end

    return {
        success = true,
        value = {
            name = userData.name,
            email = emailResult.value,
            password = userData.password,
        }
    }
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return ValidationModule
```

## Error Handling

### Error Handling Patterns

Use consistent error handling patterns in lua & luau:

```lua
-- Result pattern for error handling
export type Result<T, E> = {
    success: true,
    value: T
} | {
    success: false,
    error: E
}

local function safeParseJson(jsonString: string): Result<any, string>
    local success, result = pcall(function()
        return HttpService:JSONDecode(jsonString)
    end)

    if success then
        return {
            success = true,
            value = result
        }
    else
        return {
            success = false,
            error = "Failed to parse JSON: " .. tostring(result)
        }
    end
end

-- Error handling with custom error types
export type ValidationError = {
    type: "ValidationError",
    field: string,
    message: string,
}

export type NetworkError = {
    type: "NetworkError",
    statusCode: number?,
    message: string,
}

export type AppError = ValidationError | NetworkError

local function createValidationError(field: string, message: string): ValidationError
    return {
        type = "ValidationError",
        field = field,
        message = message,
    }
end

local function createNetworkError(message: string, statusCode: number?): NetworkError
    return {
        type = "NetworkError",
        statusCode = statusCode,
        message = message,
    }
end

-- Safe function execution with error recovery
local function executeWithRetry<T>(
    operation: () -> T,
    maxRetries: number,
    delaySeconds: number?
): Result<T, string>
    delaySeconds = delaySeconds or 1

    for attempt = 1, maxRetries do
        local success, result = pcall(operation)

        if success then
            return {
                success = true,
                value = result
            }
        end

        if attempt < maxRetries then
            wait(delaySeconds)
        end
    end

    return {
        success = false,
        error = "Operation failed after " .. maxRetries .. " attempts"
    }
end

-- Promise-based error handling
local function fetchDataAsync(url: string): Promise.Promise<any>
    return Promise.new(function(resolve, reject)
        local success, result = pcall(function()
            -- Simulate HTTP request
            return HttpService:GetAsync(url)
        end)

        if success then
            local parseResult = safeParseJson(result)
            if parseResult.success then
                resolve(parseResult.value)
            else
                reject(parseResult.error)
            end
        else
            reject("Network request failed: " .. tostring(result))
        end
    end)
end
```

## Roblox-Specific Patterns

### Service Management

Use proper service management patterns:

```lua
-- Service locator pattern
local ServiceLocator = {}
ServiceLocator.__index = ServiceLocator

local services: {[string]: any} = {}

function ServiceLocator.getService(serviceName: string): any
    if not services[serviceName] then
        services[serviceName] = game:GetService(serviceName)
    end
    return services[serviceName]
end

-- Commonly used services
local Players = ServiceLocator.getService("Players")
local ReplicatedStorage = ServiceLocator.getService("ReplicatedStorage")
local RunService = ServiceLocator.getService("RunService")

-- Connection management
local ConnectionManager = {}
ConnectionManager.__index = ConnectionManager

export type ConnectionManager = typeof(setmetatable({} :: {
    _connections: {RBXScriptConnection},
}, ConnectionManager))

function ConnectionManager.new(): ConnectionManager
    local self = setmetatable({}, ConnectionManager)
    self._connections = {}
    return self
end

function ConnectionManager:connect(signal: RBXScriptSignal, callback: (...any) -> ()): RBXScriptConnection
    local connection = signal:Connect(callback)
    table.insert(self._connections, connection)
    return connection
end

function ConnectionManager:disconnectAll(): ()
    for _, connection in self._connections do
        connection:Disconnect()
    end
    table.clear(self._connections)
end

function ConnectionManager:destroy(): ()
    self:disconnectAll()
end

-- Player data management
local PlayerDataManager = {}
PlayerDataManager.__index = PlayerDataManager

export type PlayerData = {
    userId: number,
    displayName: string,
    level: number,
    experience: number,
    coins: number,
    inventory: {string},
}

export type PlayerDataManager = typeof(setmetatable({} :: {
    _playerData: {[Player]: PlayerData},
    _connections: ConnectionManager,
}, PlayerDataManager))

function PlayerDataManager.new(): PlayerDataManager
    local self = setmetatable({}, PlayerDataManager)
    self._playerData = {}
    self._connections = ConnectionManager.new()

    -- Connect to player events
    self._connections:connect(Players.PlayerAdded, function(player)
        self:_onPlayerAdded(player)
    end)

    self._connections:connect(Players.PlayerRemoving, function(player)
        self:_onPlayerRemoving(player)
    end)

    return self
end

function PlayerDataManager:getPlayerData(player: Player): PlayerData?
    return self._playerData[player]
end

function PlayerDataManager:updatePlayerData(player: Player, updates: Partial<PlayerData>): ()
    local data = self._playerData[player]
    if not data then
        return
    end

    for key, value in updates do
        (data :: any)[key] = value
    end
end

function PlayerDataManager:_onPlayerAdded(player: Player): ()
    local playerData: PlayerData = {
        userId = player.UserId,
        displayName = player.DisplayName,
        level = 1,
        experience = 0,
        coins = 100,
        inventory = {},
    }

    self._playerData[player] = playerData
end

function PlayerDataManager:_onPlayerRemoving(player: Player): ()
    -- Save player data before removing
    local data = self._playerData[player]
    if data then
        self:_savePlayerData(player, data)
        self._playerData[player] = nil
    end
end

function PlayerDataManager:_savePlayerData(player: Player, data: PlayerData): ()
    -- Implementation for saving player data
    print("Saving data for player:", player.Name)
end

function PlayerDataManager:destroy(): ()
    self._connections:destroy()
    table.clear(self._playerData)
end
```

### RemoteEvent and RemoteFunction Patterns

Use type-safe remote communication:

```lua
-- Remote event wrapper with type safety
local RemoteEventWrapper = {}
RemoteEventWrapper.__index = RemoteEventWrapper

export type RemoteEventWrapper<T> = typeof(setmetatable({} :: {
    _remoteEvent: RemoteEvent,
    _connections: {RBXScriptConnection},
}, RemoteEventWrapper))

function RemoteEventWrapper.new<T>(remoteEvent: RemoteEvent): RemoteEventWrapper<T>
    local self = setmetatable({}, RemoteEventWrapper)
    self._remoteEvent = remoteEvent
    self._connections = {}
    return self
end

function RemoteEventWrapper:fireServer<T>(data: T): ()
    self._remoteEvent:FireServer(data)
end

function RemoteEventWrapper:fireClient<T>(player: Player, data: T): ()
    self._remoteEvent:FireClient(player, data)
end

function RemoteEventWrapper:fireAllClients<T>(data: T): ()
    self._remoteEvent:FireAllClients(data)
end

function RemoteEventWrapper:onServerEvent<T>(callback: (player: Player, data: T) -> ()): RBXScriptConnection
    local connection = self._remoteEvent.OnServerEvent:Connect(callback)
    table.insert(self._connections, connection)
    return connection
end

function RemoteEventWrapper:onClientEvent<T>(callback: (data: T) -> ()): RBXScriptConnection
    local connection = self._remoteEvent.OnClientEvent:Connect(callback)
    table.insert(self._connections, connection)
    return connection
end

function RemoteEventWrapper:destroy(): ()
    for _, connection in self._connections do
        connection:Disconnect()
    end
    table.clear(self._connections)
end

-- Usage example
export type ChatMessage = {
    playerId: number,
    message: string,
    timestamp: number,
}

local chatRemoteEvent = RemoteEventWrapper.new<ChatMessage>(
    ReplicatedStorage.Remotes.ChatMessage
)

-- Server-side usage
chatRemoteEvent:onServerEvent(function(player: Player, data: ChatMessage)
    -- Validate and broadcast message
    if data.message and string.len(data.message) > 0 then
        local validatedMessage: ChatMessage = {
            playerId = player.UserId,
            message = data.message,
            timestamp = DateTime.now().UnixTimestamp,
        }

        chatRemoteEvent:fireAllClients(validatedMessage)
    end
end)
```

## Comments and Documentation

### Documentation Standards

Use clear and concise documentation following the "no inline documentation" principle:

```lua
-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================

-- Calculate compound interest using the standard formula
local function calculateCompoundInterest(
    principal: number,
    rate: number,
    time: number,
    frequency: number
): number
    return principal * math.pow(1 + rate / frequency, frequency * time)
end

-- Validate email address using basic format checking
local function isValidEmail(email: string): boolean
    return string.match(email, "^[%w%._%+%-]+@[%w%.%-]+%.%w+$") ~= nil
end

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================

--[[
    Service class for managing payment operations.

    Provides secure payment processing with rate limiting,
    validation, and comprehensive error handling.
]]
local PaymentProcessor = {}
PaymentProcessor.__index = PaymentProcessor

export type PaymentProcessor = typeof(setmetatable({} :: {
    _config: PaymentConfig,
    _rateLimiter: RateLimiter,
    _connections: {RBXScriptConnection},
}, PaymentProcessor))

--[[
    Initialize payment processor with configuration.

    @param config Payment processor configuration
    @throws error if configuration is invalid
]]
function PaymentProcessor.new(config: PaymentConfig): PaymentProcessor
    local self = setmetatable({}, PaymentProcessor)
    self._config = config
    self._rateLimiter = RateLimiter.new(config.maxRequestsPerMinute)
    self._connections = {}
    return self
end

--[[
    Process a payment request.

    @param paymentRequest Payment details and amount
    @return Promise containing payment result
    @throws ValidationError if payment data is invalid
    @throws RateLimitError if rate limit exceeded
]]
function PaymentProcessor:processPayment(paymentRequest: PaymentRequest): Promise.Promise<PaymentResult>
    return Promise.new(function(resolve, reject)
        -- Validate payment request data
        local validationResult = self:_validatePaymentRequest(paymentRequest)
        if not validationResult.success then
            reject(validationResult.error)
            return
        end

        -- Check rate limiting
        if not self._rateLimiter:canProceed() then
            reject("Rate limit exceeded")
            return
        end

        -- Execute payment processing
        local result = self:_executePayment(paymentRequest)
        resolve(result)
    end)
end

-- Validate payment request data
function PaymentProcessor:_validatePaymentRequest(request: PaymentRequest): Result<PaymentRequest, string>
    if not request.amount or request.amount <= 0 then
        return {
            success = false,
            error = "Invalid payment amount"
        }
    end

    if not request.currency or string.len(request.currency) ~= 3 then
        return {
            success = false,
            error = "Invalid currency code"
        }
    end

    return {
        success = true,
        value = request
    }
end

-- Execute the actual payment processing
function PaymentProcessor:_executePayment(request: PaymentRequest): PaymentResult
    -- Implementation
    return {
        transactionId = HttpService:GenerateGUID(false),
        status = "completed",
        amount = request.amount,
        currency = request.currency,
    }
end
```

### Comment Guidelines

Use minimal but meaningful comments:

```lua
-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================

local function generateSessionToken(): string
    -- Use cryptographically secure random number generator
    local chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
    local token = ""

    -- Generate 32-character token
    for i = 1, 32 do
        local randomIndex = math.random(1, string.len(chars))
        token = token .. string.sub(chars, randomIndex, randomIndex)
    end

    return token
end

local function validateUserPermissions(
    user: User,
    requiredPermissions: {Permission}
): boolean
    local userPermissions = user.permissions

    -- Check if user has all required permissions
    for _, permission in requiredPermissions do
        local hasPermission = false
        for _, userPermission in userPermissions do
            if userPermission == permission then
                hasPermission = true
                break
            end
        end

        if not hasPermission then
            return false
        end
    end

    return true
end

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================

local UserService = {}
UserService.__index = UserService

function UserService.new(repository: UserRepository): UserService
    local self = setmetatable({}, UserService)
    self._repository = repository
    self._connections = {}
    return self
end

function UserService:createUser(userData: UserData): Promise.Promise<User>
    return Promise.new(function(resolve, reject)
        -- Validate required fields
        local validationResult = self:_validateUserData(userData)
        if not validationResult.success then
            reject(validationResult.error)
            return
        end

        -- Check for existing user with same email
        local existingUser = self._repository:findByEmail(userData.email)
        if existingUser then
            reject("User with this email already exists")
            return
        end

        -- Create and save user
        local user = User.new(userData)
        local savedUser = self._repository:save(user)

        print("Created user:", savedUser.id)
        resolve(savedUser)
    end)
end

function UserService:_validateUserData(data: UserData): Result<UserData, string>
    if not data.name or string.len(data.name) == 0 then
        return {
            success = false,
            error = "Name cannot be empty"
        }
    end

    if not isValidEmail(data.email) then
        return {
            success = false,
            error = "Invalid email format"
        }
    end

    if data.age and (data.age < 0 or data.age > 150) then
        return {
            success = false,
            error = "Age must be between 0 and 150"
        }
    end

    return {
        success = true,
        value = data
    }
end
```

## Code Quality and Linting

### Lua & luau Code Quality Tools

Use modern lua & luau code quality tools for consistent code standards:

```lua
-- Key tools for lua & luau code quality:
-- - lua & luau Language Server: Type checking and IntelliSense
-- - StyLua: Code formatting
-- - Selene: Linting and static analysis
-- - Roblox Studio: Built-in script analysis
-- - TestEZ: Unit testing framework
```

### Code Quality Practices

```lua
-- Good: Use proper type annotations
local function processUserData(data: UserData): User
    assert(type(data) == "table", "User data must be a table")
    assert(type(data.name) == "string", "User name must be a string")
    assert(type(data.email) == "string", "User email must be a string")

    return User.new(data)
end

-- Good: Use proper error handling
local function safeOperation<T>(operation: () -> T): Result<T, string>
    local success, result = pcall(operation)

    if success then
        return {
            success = true,
            value = result
        }
    else
        return {
            success = false,
            error = tostring(result)
        }
    end
end

-- Good: Use modern lua & luau features
local function filterActiveUsers(users: {User}): {User}
    local activeUsers: {User} = {}

    for _, user in users do
        if user.isActive then
            table.insert(activeUsers, user)
        end
    end

    return activeUsers
end
```

## Testing Standards

### Test File Structure

```lua
--[[
 - file: TEST_USER_SERVICE.lua & luau

 - version: 1.0.0
 - author: BleckWolf25
 - contributors:

 - copyright: Dynamic Innovative Studio

 - description:
   - Unit tests for UserService class.
   - Tests user creation, validation, and data management functionality.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local TestEZ = require(ReplicatedStorage.DevPackages.TestEZ)
local UserService = require(ReplicatedStorage.Shared.UserService)
local MockUserRepository = require(script.Parent.Mocks.MockUserRepository)

-- ============================================================================
-- TEST SUITES
-- ============================================================================
return TestEZ.describe("UserService", function()
    local userService: UserService
    local mockRepository: MockUserRepository

    TestEZ.beforeEach(function()
        mockRepository = MockUserRepository.new()
        userService = UserService.new(mockRepository)
    end)

    TestEZ.afterEach(function()
        userService:destroy()
        mockRepository:destroy()
    end)

    TestEZ.describe("createUser", function()
        TestEZ.it("should create user with valid data", function()
            -- Arrange
            local userData = {
                name = "John Doe",
                email = "<EMAIL>",
                age = 30
            }

            mockRepository:mockFindByEmail(userData.email, nil)
            mockRepository:mockSave(function(user)
                return user
            end)

            -- Act
            local promise = userService:createUser(userData)
            local success, result = promise:await()

            -- Assert
            TestEZ.expect(success).to.equal(true)
            TestEZ.expect(result.name).to.equal(userData.name)
            TestEZ.expect(result.email).to.equal(userData.email)
            TestEZ.expect(result.age).to.equal(userData.age)
        end)

        TestEZ.it("should reject with empty name", function()
            -- Arrange
            local userData = {
                name = "",
                email = "<EMAIL>",
                age = 30
            }

            -- Act
            local promise = userService:createUser(userData)
            local success, error = promise:await()

            -- Assert
            TestEZ.expect(success).to.equal(false)
            TestEZ.expect(error).to.equal("Name cannot be empty")
        end)

        TestEZ.it("should reject with invalid email", function()
            -- Arrange
            local userData = {
                name = "John Doe",
                email = "invalid-email",
                age = 30
            }

            -- Act
            local promise = userService:createUser(userData)
            local success, error = promise:await()

            -- Assert
            TestEZ.expect(success).to.equal(false)
            TestEZ.expect(error).to.equal("Invalid email format")
        end)

        TestEZ.it("should reject when user already exists", function()
            -- Arrange
            local userData = {
                name = "John Doe",
                email = "<EMAIL>",
                age = 30
            }

            local existingUser = User.new(userData)
            mockRepository:mockFindByEmail(userData.email, existingUser)

            -- Act
            local promise = userService:createUser(userData)
            local success, error = promise:await()

            -- Assert
            TestEZ.expect(success).to.equal(false)
            TestEZ.expect(error).to.equal("User with this email already exists")
        end)
    end)
end)
```

## Performance Guidelines

### Optimization Techniques

```lua
-- Good: Use table.clear() instead of creating new tables
local cache: {[string]: any} = {}

local function clearCache(): ()
    table.clear(cache)  -- More efficient than cache = {}
end

-- Good: Pre-allocate tables when size is known
local function createUserList(userCount: number): {User}
    local users: {User} = table.create(userCount)  -- Pre-allocate

    for i = 1, userCount do
        users[i] = User.new({
            name = "User " .. i,
            email = "user" .. i .. "@example.com",
            age = 25
        })
    end

    return users
end

-- Good: Use string.buffer for string concatenation
local function buildLargeString(parts: {string}): string
    local buffer = buffer.create(1024)  -- Pre-allocate buffer

    for _, part in parts do
        buffer.writestring(buffer, part)
    end

    return buffer.tostring(buffer)
end

-- Good: Cache expensive computations
local fibonacciCache: {[number]: number} = {}

local function fibonacci(n: number): number
    if fibonacciCache[n] then
        return fibonacciCache[n]
    end

    local result: number
    if n <= 1 then
        result = n
    else
        result = fibonacci(n - 1) + fibonacci(n - 2)
    end

    fibonacciCache[n] = result
    return result
end

-- Good: Use RunService for frame-based operations
local function createFrameBasedUpdater(): () -> ()
    local connections: {RBXScriptConnection} = {}

    local function startUpdating(): ()
        local connection = RunService.Heartbeat:Connect(function(deltaTime)
            -- Update logic here
            updateGameState(deltaTime)
        end)

        table.insert(connections, connection)
    end

    local function stopUpdating(): ()
        for _, connection in connections do
            connection:Disconnect()
        end
        table.clear(connections)
    end

    return {
        start = startUpdating,
        stop = stopUpdating
    }
end
```

## Security Considerations

### Secure Coding Practices

```lua
-- Input validation and sanitization
local SecurityUtils = {}

function SecurityUtils.validateInput(input: any, expectedType: string): boolean
    return type(input) == expectedType
end

function SecurityUtils.sanitizeString(input: string, maxLength: number?): string
    maxLength = maxLength or 100

    -- Remove dangerous characters
    local sanitized = string.gsub(input, "[<>\"'&]", "")

    -- Limit length
    if string.len(sanitized) > maxLength then
        sanitized = string.sub(sanitized, 1, maxLength)
    end

    return sanitized
end

function SecurityUtils.validateUserId(userId: any): boolean
    return type(userId) == "number" and userId > 0
end

-- Rate limiting
local RateLimiter = {}
RateLimiter.__index = RateLimiter

export type RateLimiter = typeof(setmetatable({} :: {
    _requests: {[any]: {number}},
    _maxRequests: number,
    _windowSeconds: number,
}, RateLimiter))

function RateLimiter.new(maxRequests: number, windowSeconds: number?): RateLimiter
    local self = setmetatable({}, RateLimiter)
    self._requests = {}
    self._maxRequests = maxRequests
    self._windowSeconds = windowSeconds or 60
    return self
end

function RateLimiter:canProceed(identifier: any): boolean
    local currentTime = DateTime.now().UnixTimestamp
    local requests = self._requests[identifier] or {}

    -- Remove old requests outside the window
    local validRequests: {number} = {}
    for _, requestTime in requests do
        if currentTime - requestTime < self._windowSeconds then
            table.insert(validRequests, requestTime)
        end
    end

    -- Check if under limit
    if #validRequests < self._maxRequests then
        table.insert(validRequests, currentTime)
        self._requests[identifier] = validRequests
        return true
    end

    return false
end

-- Secure remote validation
local function validateRemoteRequest(player: Player, data: any): boolean
    -- Validate player
    if not player or not player.Parent then
        return false
    end

    -- Validate data structure
    if type(data) ~= "table" then
        return false
    end

    -- Check rate limiting
    local rateLimiter = RateLimiter.new(10, 60)  -- 10 requests per minute
    if not rateLimiter:canProceed(player.UserId) then
        return false
    end

    return true
end

-- Example secure remote event handler
local function handleSecureRemoteEvent(player: Player, data: any): ()
    if not validateRemoteRequest(player, data) then
        warn("Invalid remote request from player:", player.Name)
        return
    end

    -- Process validated request
    local sanitizedData = {
        message = SecurityUtils.sanitizeString(data.message, 200),
        action = SecurityUtils.sanitizeString(data.action, 50),
    }

    -- Continue with business logic
    processPlayerAction(player, sanitizedData)
end
```

## References

- [lua & luau Language Reference](<https://lua> & luau-lang.org/)
- [Roblox Developer Hub](https://developer.roblox.com/)
- [lua & luau Type System](<https://lua> & luau-lang.org/typecheck)
- [StyLua Formatter](https://github.com/JohnnyMorganz/StyLua)
- [Selene Linter](https://kampfkarren.github.io/selene/)
- [TestEZ Testing Framework](https://roblox.github.io/testez/)
- [Roblox Performance Guidelines](https://developer.roblox.com/en-us/articles/improving-performance)

> lua & luau code standards are enforced through StyLua, Selene, and the lua & luau Language Server. All developers must follow these standards to maintain code quality, performance, and consistency across the Dynamic Innovative Studio organization.
