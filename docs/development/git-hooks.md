# Git Hooks Guide

This project uses Git hooks via <PERSON><PERSON> to enforce code quality and consistency. This document explains the configured hooks and how to work with them.

## Overview

Git hooks are scripts that run automatically when certain Git events occur. We use the following hooks:

- **pre-commit**: Runs before a commit is created
- **commit-msg**: Validates commit messages
- **pre-push**: Runs before pushing to a remote

## Setup

The hooks are automatically installed when you run `npm install` thanks to the `prepare` script in `package.json`. If you need to manually install them:

```bash
npm run prepare
```

## Pre-commit Hook

The pre-commit hook runs before each commit and:

1. Runs lint-staged to:
   - Lint JavaScript/TypeScript files with ESLint
   - Format code with Prettier
2. Checks for large files (>5MB)
3. Checks for merge conflict markers
4. Scans for potential sensitive information

### Bypassing Pre-commit Hooks

In rare cases, you may need to bypass the pre-commit hook:

```bash
git commit --no-verify -m "Your message"
```

**Note**: This should be used sparingly and only in exceptional circumstances.

## Commit Message Hook

The commit-msg hook enforces the [Conventional Commits](https://www.conventionalcommits.org/) format:

```git
type(scope): subject
```

### Valid Types

- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code changes that neither fix bugs nor add features
- `test`: Adding or modifying tests
- `chore`: Changes to the build process or auxiliary tools

### Valid Scopes

Scopes are defined in `commitlint.config.js` and include:

- `auth`, `api`, `ui`, `config`, `docs`, `deps`, `ci`, etc.

### Examples

✅ Valid commit messages:

```git
feat(auth): implement JWT authentication
fix(ui): resolve button alignment issue
docs(readme): update installation instructions
```

❌ Invalid commit messages:

```git
added new feature
Fixed bug
Update code
```

## Pre-push Hook

The pre-push hook runs before pushing to a remote and:

1. Runs tests to ensure all tests pass
2. Runs linting to catch any code quality issues
3. Validates branch naming conventions

### Branch Naming Convention

All branches must follow the pattern:

- `feature/description`
- `bugfix/description`
- `hotfix/description`

## Troubleshooting

If you encounter issues with Git hooks:

1. Ensure Husky is properly installed: `npm run prepare`
2. Check if the hook scripts are executable: `chmod +x .husky/*`
3. Verify that the hooks are in the correct location: `.husky/` directory
4. Check for error messages in the terminal when Git hooks run

## Additional Resources

- [Husky Documentation](https://typicode.github.io/husky/)
- [Conventional Commits](https://www.conventionalcommits.org/)
- [lint-staged Documentation](https://github.com/okonet/lint-staged)
