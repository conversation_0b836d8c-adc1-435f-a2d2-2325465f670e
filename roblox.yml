# This file was @generated by generate-roblox-std at 2025-04-14 19:18:36.605571 +01:00
base: luau
name: roblox
globals:
  Axes.new:
    args:
    - type: '...'
    must_use: true
  BrickColor.Black:
    args: []
    must_use: true
  BrickColor.Blue:
    args: []
    must_use: true
  BrickColor.DarkGray:
    args: []
    must_use: true
  BrickColor.Gray:
    args: []
    must_use: true
  BrickColor.Green:
    args: []
    must_use: true
  BrickColor.Red:
    args: []
    must_use: true
  BrickColor.White:
    args: []
    must_use: true
  BrickColor.Yellow:
    args: []
    must_use: true
  BrickColor.new:
    args:
    - type: any
    - required: false
      type: number
    - required: false
      type: number
    must_use: true
  BrickColor.palette:
    args:
    - type: number
    must_use: true
  BrickColor.random:
    args: []
    must_use: true
  CFrame.Angles:
    args:
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    must_use: true
  CFrame.fromAxisAngle:
    args:
    - type:
        display: Vector3
    - type: number
    must_use: true
  CFrame.fromEulerAngles:
    args:
    - type: number
    - type: number
    - type: number
    - required: false
      type:
        display: RotationOrder
    must_use: true
  CFrame.fromEulerAnglesXYZ:
    args:
    - type: number
    - type: number
    - type: number
    must_use: true
  CFrame.fromEulerAnglesYXZ:
    args:
    - type: number
    - type: number
    - type: number
    must_use: true
  CFrame.fromMatrix:
    args:
    - type:
        display: Vector3
    - type:
        display: Vector3
    - type:
        display: Vector3
    - required: false
      type:
        display: Vector3
    must_use: true
  CFrame.fromOrientation:
    args:
    - type: number
    - type: number
    - type: number
    must_use: true
  CFrame.identity:
    property: read-only
  CFrame.lookAlong:
    args:
    - type:
        display: Vector3
    - type:
        display: Vector3
    - required: false
      type:
        display: Vector3
    must_use: true
  CFrame.lookAt:
    args:
    - type:
        display: Vector3
    - type:
        display: Vector3
    - required: false
      type:
        display: Vector3
    must_use: true
  CFrame.new:
    args:
    - required: false
      type: any
    - required: false
      type: any
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    must_use: true
  CatalogSearchParams.new:
    args: []
    must_use: true
  Color3.fromHSV:
    args:
    - type: number
    - type: number
    - type: number
    must_use: true
  Color3.fromHex:
    args:
    - type: string
    must_use: true
  Color3.fromRGB:
    args:
    - type: number
    - type: number
    - type: number
    must_use: true
  Color3.new:
    args:
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    must_use: true
  Color3.toHSV:
    args:
    - type:
        display: Color3
    must_use: true
  ColorSequence.new:
    args:
    - type: any
    - required: false
      type:
        display: Color3
    must_use: true
  ColorSequenceKeypoint.new:
    args:
    - type: number
    - type:
        display: Color3
    must_use: true
  Content.fromAssetId:
    args:
    - type: number
  Content.fromObject:
    args:
    - type:
        display: Object
    must_use: true
  Content.fromUri:
    args:
    - type: string
    must_use: true
  DateTime.fromIsoDate:
    args:
    - type: string
    must_use: true
  DateTime.fromLocalTime:
    args:
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    must_use: true
  DateTime.fromUniversalTime:
    args:
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    must_use: true
  DateTime.fromUnixTimestamp:
    args:
    - type: number
    must_use: true
  DateTime.fromUnixTimestampMillis:
    args:
    - type: number
    must_use: true
  DateTime.now:
    args: []
    must_use: true
  DebuggerManager:
    args: []
    must_use: true
  DockWidgetPluginGuiInfo.new:
    args:
    - required: false
      type:
        display: InitialDockState
    - required: false
      type: bool
    - required: false
      type: bool
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    must_use: true
  Enum.AccessModifierType.Allow:
    struct: EnumItem
  Enum.AccessModifierType.Deny:
    struct: EnumItem
  Enum.AccessModifierType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AccessoryType.Back:
    struct: EnumItem
  Enum.AccessoryType.DressSkirt:
    struct: EnumItem
  Enum.AccessoryType.Eyebrow:
    struct: EnumItem
  Enum.AccessoryType.Eyelash:
    struct: EnumItem
  Enum.AccessoryType.Face:
    struct: EnumItem
  Enum.AccessoryType.Front:
    struct: EnumItem
  Enum.AccessoryType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AccessoryType.Hair:
    struct: EnumItem
  Enum.AccessoryType.Hat:
    struct: EnumItem
  Enum.AccessoryType.Jacket:
    struct: EnumItem
  Enum.AccessoryType.LeftShoe:
    struct: EnumItem
  Enum.AccessoryType.Neck:
    struct: EnumItem
  Enum.AccessoryType.Pants:
    struct: EnumItem
  Enum.AccessoryType.RightShoe:
    struct: EnumItem
  Enum.AccessoryType.Shirt:
    struct: EnumItem
  Enum.AccessoryType.Shorts:
    struct: EnumItem
  Enum.AccessoryType.Shoulder:
    struct: EnumItem
  Enum.AccessoryType.Sweater:
    struct: EnumItem
  Enum.AccessoryType.TShirt:
    struct: EnumItem
  Enum.AccessoryType.TeeShirt:
    struct: EnumItem
    deprecated:
      message: Enum.AccessoryType.TeeShirt was replaced with Enum.AccessoryType.TShirt
      replace:
      - Enum.AccessoryType.TShirt
  Enum.AccessoryType.Unknown:
    struct: EnumItem
  Enum.AccessoryType.Waist:
    struct: EnumItem
  Enum.ActionOnAutoResumeSync.DontResume:
    struct: EnumItem
  Enum.ActionOnAutoResumeSync.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ActionOnAutoResumeSync.KeepLocal:
    struct: EnumItem
  Enum.ActionOnAutoResumeSync.KeepStudio:
    struct: EnumItem
  Enum.ActionOnStopSync.AlwaysAsk:
    struct: EnumItem
  Enum.ActionOnStopSync.DeleteLocalFiles:
    struct: EnumItem
  Enum.ActionOnStopSync.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ActionOnStopSync.KeepLocalFiles:
    struct: EnumItem
  Enum.ActionType.Draw:
    struct: EnumItem
  Enum.ActionType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ActionType.Lose:
    struct: EnumItem
  Enum.ActionType.Nothing:
    struct: EnumItem
  Enum.ActionType.Pause:
    struct: EnumItem
  Enum.ActionType.Win:
    struct: EnumItem
  Enum.ActuatorRelativeTo.Attachment0:
    struct: EnumItem
  Enum.ActuatorRelativeTo.Attachment1:
    struct: EnumItem
  Enum.ActuatorRelativeTo.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ActuatorRelativeTo.World:
    struct: EnumItem
  Enum.ActuatorType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ActuatorType.Motor:
    struct: EnumItem
  Enum.ActuatorType.None:
    struct: EnumItem
  Enum.ActuatorType.Servo:
    struct: EnumItem
  Enum.AdAvailabilityResult.DeviceIneligible:
    struct: EnumItem
  Enum.AdAvailabilityResult.ExperienceIneligible:
    struct: EnumItem
  Enum.AdAvailabilityResult.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AdAvailabilityResult.InternalError:
    struct: EnumItem
  Enum.AdAvailabilityResult.IsAvailable:
    struct: EnumItem
  Enum.AdAvailabilityResult.NoFill:
    struct: EnumItem
  Enum.AdAvailabilityResult.PlayerIneligible:
    struct: EnumItem
  Enum.AdAvailabilityResult.PublisherIneligible:
    struct: EnumItem
  Enum.AdEventType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AdEventType.RewardedAdGrant:
    struct: EnumItem
  Enum.AdEventType.RewardedAdLoaded:
    struct: EnumItem
  Enum.AdEventType.RewardedAdUnloaded:
    struct: EnumItem
  Enum.AdEventType.UserCompletedVideo:
    struct: EnumItem
  Enum.AdEventType.VideoLoaded:
    struct: EnumItem
  Enum.AdEventType.VideoRemoved:
    struct: EnumItem
  Enum.AdFormat.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AdFormat.RewardedVideo:
    struct: EnumItem
  Enum.AdShape.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AdShape.HorizontalRectangle:
    struct: EnumItem
  Enum.AdTeleportMethod.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AdTeleportMethod.InGameMenuBackButton:
    struct: EnumItem
  Enum.AdTeleportMethod.PortalForward:
    struct: EnumItem
  Enum.AdTeleportMethod.UIBackButton:
    struct: EnumItem
  Enum.AdTeleportMethod.Undefined:
    struct: EnumItem
  Enum.AdUIEventType.AdLabelClicked:
    struct: EnumItem
  Enum.AdUIEventType.CloseButtonClicked:
    struct: EnumItem
  Enum.AdUIEventType.FullscreenButtonClicked:
    struct: EnumItem
  Enum.AdUIEventType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AdUIEventType.PauseButtonClicked:
    struct: EnumItem
  Enum.AdUIEventType.PauseEventTriggered:
    struct: EnumItem
  Enum.AdUIEventType.PlayButtonClicked:
    struct: EnumItem
  Enum.AdUIEventType.PlayEventTriggered:
    struct: EnumItem
  Enum.AdUIEventType.VolumeButtonClicked:
    struct: EnumItem
  Enum.AdUIEventType.WhyThisAdClicked:
    struct: EnumItem
  Enum.AdUIType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AdUIType.Image:
    struct: EnumItem
  Enum.AdUIType.None:
    struct: EnumItem
  Enum.AdUIType.Video:
    struct: EnumItem
  Enum.AdUnitStatus.Active:
    struct: EnumItem
  Enum.AdUnitStatus.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AdUnitStatus.Inactive:
    struct: EnumItem
  Enum.AdornCullingMode.Automatic:
    struct: EnumItem
  Enum.AdornCullingMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AdornCullingMode.Never:
    struct: EnumItem
  Enum.AlignType.AllAxes:
    struct: EnumItem
  Enum.AlignType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AlignType.Parallel:
    struct: EnumItem
  Enum.AlignType.Perpendicular:
    struct: EnumItem
  Enum.AlignType.PrimaryAxisLookAt:
    struct: EnumItem
  Enum.AlignType.PrimaryAxisParallel:
    struct: EnumItem
  Enum.AlignType.PrimaryAxisPerpendicular:
    struct: EnumItem
  Enum.AlphaMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AlphaMode.Overlay:
    struct: EnumItem
  Enum.AlphaMode.TintMask:
    struct: EnumItem
  Enum.AlphaMode.Transparency:
    struct: EnumItem
  Enum.AnalyticsCustomFieldKeys.CustomField01:
    struct: EnumItem
  Enum.AnalyticsCustomFieldKeys.CustomField02:
    struct: EnumItem
  Enum.AnalyticsCustomFieldKeys.CustomField03:
    struct: EnumItem
  Enum.AnalyticsCustomFieldKeys.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AnalyticsEconomyAction.Acquire:
    struct: EnumItem
  Enum.AnalyticsEconomyAction.Default:
    struct: EnumItem
  Enum.AnalyticsEconomyAction.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AnalyticsEconomyAction.Spend:
    struct: EnumItem
  Enum.AnalyticsEconomyFlowType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AnalyticsEconomyFlowType.Sink:
    struct: EnumItem
  Enum.AnalyticsEconomyFlowType.Source:
    struct: EnumItem
  Enum.AnalyticsEconomyTransactionType.ContextualPurchase:
    struct: EnumItem
  Enum.AnalyticsEconomyTransactionType.Gameplay:
    struct: EnumItem
  Enum.AnalyticsEconomyTransactionType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AnalyticsEconomyTransactionType.IAP:
    struct: EnumItem
  Enum.AnalyticsEconomyTransactionType.Onboarding:
    struct: EnumItem
  Enum.AnalyticsEconomyTransactionType.Shop:
    struct: EnumItem
  Enum.AnalyticsEconomyTransactionType.TimedReward:
    struct: EnumItem
  Enum.AnalyticsLogLevel.Debug:
    struct: EnumItem
  Enum.AnalyticsLogLevel.Error:
    struct: EnumItem
  Enum.AnalyticsLogLevel.Fatal:
    struct: EnumItem
  Enum.AnalyticsLogLevel.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AnalyticsLogLevel.Information:
    struct: EnumItem
  Enum.AnalyticsLogLevel.Trace:
    struct: EnumItem
  Enum.AnalyticsLogLevel.Warning:
    struct: EnumItem
  Enum.AnalyticsProgressionStatus.Abandon:
    struct: EnumItem
  Enum.AnalyticsProgressionStatus.Begin:
    struct: EnumItem
  Enum.AnalyticsProgressionStatus.Complete:
    struct: EnumItem
  Enum.AnalyticsProgressionStatus.Default:
    struct: EnumItem
  Enum.AnalyticsProgressionStatus.Fail:
    struct: EnumItem
  Enum.AnalyticsProgressionStatus.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AnalyticsProgressionType.Complete:
    struct: EnumItem
  Enum.AnalyticsProgressionType.Custom:
    struct: EnumItem
  Enum.AnalyticsProgressionType.Fail:
    struct: EnumItem
  Enum.AnalyticsProgressionType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AnalyticsProgressionType.Start:
    struct: EnumItem
  Enum.AnimationClipFromVideoStatus.Cancelled:
    struct: EnumItem
  Enum.AnimationClipFromVideoStatus.ErrorGeneric:
    struct: EnumItem
  Enum.AnimationClipFromVideoStatus.ErrorMultiplePeople:
    struct: EnumItem
  Enum.AnimationClipFromVideoStatus.ErrorNoPersonDetected:
    struct: EnumItem
  Enum.AnimationClipFromVideoStatus.ErrorUploadingVideo:
    struct: EnumItem
  Enum.AnimationClipFromVideoStatus.ErrorVideoTooLong:
    struct: EnumItem
  Enum.AnimationClipFromVideoStatus.ErrorVideoUnstable:
    struct: EnumItem
  Enum.AnimationClipFromVideoStatus.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AnimationClipFromVideoStatus.Initializing:
    struct: EnumItem
  Enum.AnimationClipFromVideoStatus.Pending:
    struct: EnumItem
  Enum.AnimationClipFromVideoStatus.Processing:
    struct: EnumItem
  Enum.AnimationClipFromVideoStatus.Success:
    struct: EnumItem
  Enum.AnimationClipFromVideoStatus.Timeout:
    struct: EnumItem
  Enum.AnimationPriority.Action:
    struct: EnumItem
  Enum.AnimationPriority.Action2:
    struct: EnumItem
  Enum.AnimationPriority.Action3:
    struct: EnumItem
  Enum.AnimationPriority.Action4:
    struct: EnumItem
  Enum.AnimationPriority.Core:
    struct: EnumItem
  Enum.AnimationPriority.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AnimationPriority.Idle:
    struct: EnumItem
  Enum.AnimationPriority.Movement:
    struct: EnumItem
  Enum.AnimatorRetargetingMode.Default:
    struct: EnumItem
  Enum.AnimatorRetargetingMode.Disabled:
    struct: EnumItem
  Enum.AnimatorRetargetingMode.Enabled:
    struct: EnumItem
  Enum.AnimatorRetargetingMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AnnotationEditingMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AnnotationEditingMode.None:
    struct: EnumItem
  Enum.AnnotationEditingMode.PlacingNew:
    struct: EnumItem
  Enum.AnnotationEditingMode.WritingNew:
    struct: EnumItem
  Enum.AnnotationRequestResult.ErrorInternalFailure:
    struct: EnumItem
  Enum.AnnotationRequestResult.ErrorModerated:
    struct: EnumItem
  Enum.AnnotationRequestResult.ErrorNotFound:
    struct: EnumItem
  Enum.AnnotationRequestResult.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AnnotationRequestResult.Success:
    struct: EnumItem
  Enum.AnnotationRequestType.Create:
    struct: EnumItem
  Enum.AnnotationRequestType.Delete:
    struct: EnumItem
  Enum.AnnotationRequestType.Edit:
    struct: EnumItem
  Enum.AnnotationRequestType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AnnotationRequestType.Resolve:
    struct: EnumItem
  Enum.AnnotationRequestType.Unknown:
    struct: EnumItem
  Enum.AppLifecycleManagerState.Active:
    struct: EnumItem
  Enum.AppLifecycleManagerState.Detached:
    struct: EnumItem
  Enum.AppLifecycleManagerState.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AppLifecycleManagerState.Hidden:
    struct: EnumItem
  Enum.AppLifecycleManagerState.Inactive:
    struct: EnumItem
  Enum.AppShellActionType.AvatarEditorPageLoaded:
    struct: EnumItem
  Enum.AppShellActionType.GamePageLoaded:
    struct: EnumItem
  Enum.AppShellActionType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AppShellActionType.HomePageLoaded:
    struct: EnumItem
  Enum.AppShellActionType.None:
    struct: EnumItem
  Enum.AppShellActionType.OpenApp:
    struct: EnumItem
  Enum.AppShellActionType.ReadConversation:
    struct: EnumItem
  Enum.AppShellActionType.TapAvatarTab:
    struct: EnumItem
  Enum.AppShellActionType.TapChatTab:
    struct: EnumItem
  Enum.AppShellActionType.TapConversationEntry:
    struct: EnumItem
  Enum.AppShellActionType.TapGamePageTab:
    struct: EnumItem
  Enum.AppShellActionType.TapHomePageTab:
    struct: EnumItem
  Enum.AppShellFeature.AvatarEditor:
    struct: EnumItem
  Enum.AppShellFeature.Chat:
    struct: EnumItem
  Enum.AppShellFeature.GamePage:
    struct: EnumItem
  Enum.AppShellFeature.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AppShellFeature.HomePage:
    struct: EnumItem
  Enum.AppShellFeature.Landing:
    struct: EnumItem
  Enum.AppShellFeature.More:
    struct: EnumItem
  Enum.AppShellFeature.None:
    struct: EnumItem
  Enum.AppUpdateStatus.Available:
    struct: EnumItem
  Enum.AppUpdateStatus.AvailableBoundChannel:
    struct: EnumItem
  Enum.AppUpdateStatus.Failed:
    struct: EnumItem
  Enum.AppUpdateStatus.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AppUpdateStatus.NotAvailable:
    struct: EnumItem
  Enum.AppUpdateStatus.NotSupported:
    struct: EnumItem
  Enum.AppUpdateStatus.Unknown:
    struct: EnumItem
  Enum.ApplyStrokeMode.Border:
    struct: EnumItem
  Enum.ApplyStrokeMode.Contextual:
    struct: EnumItem
  Enum.ApplyStrokeMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AspectType.FitWithinMaxSize:
    struct: EnumItem
  Enum.AspectType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AspectType.ScaleWithParentSize:
    struct: EnumItem
  Enum.AssetCreatorType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AssetCreatorType.Group:
    struct: EnumItem
  Enum.AssetCreatorType.User:
    struct: EnumItem
  Enum.AssetFetchStatus.Failure:
    struct: EnumItem
  Enum.AssetFetchStatus.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AssetFetchStatus.Loading:
    struct: EnumItem
  Enum.AssetFetchStatus.None:
    struct: EnumItem
  Enum.AssetFetchStatus.Success:
    struct: EnumItem
  Enum.AssetFetchStatus.TimedOut:
    struct: EnumItem
  Enum.AssetType.Animation:
    struct: EnumItem
  Enum.AssetType.Audio:
    struct: EnumItem
  Enum.AssetType.BackAccessory:
    struct: EnumItem
  Enum.AssetType.Badge:
    struct: EnumItem
  Enum.AssetType.ClimbAnimation:
    struct: EnumItem
  Enum.AssetType.DeathAnimation:
    struct: EnumItem
  Enum.AssetType.Decal:
    struct: EnumItem
  Enum.AssetType.DressSkirtAccessory:
    struct: EnumItem
  Enum.AssetType.DynamicHead:
    struct: EnumItem
  Enum.AssetType.EarAccessory:
    struct: EnumItem
  Enum.AssetType.EmoteAnimation:
    struct: EnumItem
  Enum.AssetType.EyeAccessory:
    struct: EnumItem
  Enum.AssetType.EyebrowAccessory:
    struct: EnumItem
  Enum.AssetType.EyelashAccessory:
    struct: EnumItem
  Enum.AssetType.Face:
    struct: EnumItem
  Enum.AssetType.FaceAccessory:
    struct: EnumItem
  Enum.AssetType.FallAnimation:
    struct: EnumItem
  Enum.AssetType.FontFamily:
    struct: EnumItem
  Enum.AssetType.FrontAccessory:
    struct: EnumItem
  Enum.AssetType.GamePass:
    struct: EnumItem
  Enum.AssetType.Gear:
    struct: EnumItem
  Enum.AssetType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AssetType.HairAccessory:
    struct: EnumItem
  Enum.AssetType.Hat:
    struct: EnumItem
  Enum.AssetType.Head:
    struct: EnumItem
  Enum.AssetType.IdleAnimation:
    struct: EnumItem
  Enum.AssetType.Image:
    struct: EnumItem
  Enum.AssetType.JacketAccessory:
    struct: EnumItem
  Enum.AssetType.JumpAnimation:
    struct: EnumItem
  Enum.AssetType.LeftArm:
    struct: EnumItem
  Enum.AssetType.LeftLeg:
    struct: EnumItem
  Enum.AssetType.LeftShoeAccessory:
    struct: EnumItem
  Enum.AssetType.Lua:
    struct: EnumItem
  Enum.AssetType.Mesh:
    struct: EnumItem
  Enum.AssetType.MeshPart:
    struct: EnumItem
  Enum.AssetType.Model:
    struct: EnumItem
  Enum.AssetType.MoodAnimation:
    struct: EnumItem
  Enum.AssetType.NeckAccessory:
    struct: EnumItem
  Enum.AssetType.Package:
    struct: EnumItem
  Enum.AssetType.Pants:
    struct: EnumItem
  Enum.AssetType.PantsAccessory:
    struct: EnumItem
  Enum.AssetType.Place:
    struct: EnumItem
  Enum.AssetType.Plugin:
    struct: EnumItem
  Enum.AssetType.PoseAnimation:
    struct: EnumItem
  Enum.AssetType.RightArm:
    struct: EnumItem
  Enum.AssetType.RightLeg:
    struct: EnumItem
  Enum.AssetType.RightShoeAccessory:
    struct: EnumItem
  Enum.AssetType.RunAnimation:
    struct: EnumItem
  Enum.AssetType.Shirt:
    struct: EnumItem
  Enum.AssetType.ShirtAccessory:
    struct: EnumItem
  Enum.AssetType.ShortsAccessory:
    struct: EnumItem
  Enum.AssetType.ShoulderAccessory:
    struct: EnumItem
  Enum.AssetType.SweaterAccessory:
    struct: EnumItem
  Enum.AssetType.SwimAnimation:
    struct: EnumItem
  Enum.AssetType.TShirt:
    struct: EnumItem
  Enum.AssetType.TShirtAccessory:
    struct: EnumItem
  Enum.AssetType.TeeShirt:
    struct: EnumItem
    deprecated:
      message: Enum.AssetType.TeeShirt was replaced with Enum.AssetType.TShirt
      replace:
      - Enum.AssetType.TShirt
  Enum.AssetType.TeeShirtAccessory:
    struct: EnumItem
    deprecated:
      message: Enum.AssetType.TeeShirtAccessory was replaced with Enum.AssetType.TShirtAccessory
      replace:
      - Enum.AssetType.TShirtAccessory
  Enum.AssetType.Torso:
    struct: EnumItem
  Enum.AssetType.Video:
    struct: EnumItem
  Enum.AssetType.WaistAccessory:
    struct: EnumItem
  Enum.AssetType.WalkAnimation:
    struct: EnumItem
  Enum.AssetTypeVerification.Always:
    struct: EnumItem
  Enum.AssetTypeVerification.ClientOnly:
    struct: EnumItem
  Enum.AssetTypeVerification.Default:
    struct: EnumItem
  Enum.AssetTypeVerification.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AudioApiRollout.Automatic:
    struct: EnumItem
  Enum.AudioApiRollout.Disabled:
    struct: EnumItem
  Enum.AudioApiRollout.Enabled:
    struct: EnumItem
  Enum.AudioApiRollout.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AudioChannelLayout.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AudioChannelLayout.Mono:
    struct: EnumItem
  Enum.AudioChannelLayout.Quad:
    struct: EnumItem
  Enum.AudioChannelLayout.Stereo:
    struct: EnumItem
  Enum.AudioChannelLayout.Surround_5:
    struct: EnumItem
  Enum.AudioChannelLayout.Surround_5_1:
    struct: EnumItem
  Enum.AudioChannelLayout.Surround_7_1:
    struct: EnumItem
  Enum.AudioChannelLayout.Surround_7_1_4:
    struct: EnumItem
  Enum.AudioFilterType.Bandpass:
    struct: EnumItem
  Enum.AudioFilterType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AudioFilterType.HighShelf:
    struct: EnumItem
  Enum.AudioFilterType.Highpass12dB:
    struct: EnumItem
  Enum.AudioFilterType.Highpass24dB:
    struct: EnumItem
  Enum.AudioFilterType.Highpass48dB:
    struct: EnumItem
  Enum.AudioFilterType.LowShelf:
    struct: EnumItem
  Enum.AudioFilterType.Lowpass12dB:
    struct: EnumItem
  Enum.AudioFilterType.Lowpass24dB:
    struct: EnumItem
  Enum.AudioFilterType.Lowpass48dB:
    struct: EnumItem
  Enum.AudioFilterType.Notch:
    struct: EnumItem
  Enum.AudioFilterType.Peak:
    struct: EnumItem
  Enum.AudioSimulationFidelity.Automatic:
    struct: EnumItem
  Enum.AudioSimulationFidelity.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AudioSimulationFidelity.None:
    struct: EnumItem
  Enum.AudioSubType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AudioSubType.Music:
    struct: EnumItem
  Enum.AudioSubType.SoundEffect:
    struct: EnumItem
  Enum.AudioWindowSize.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AudioWindowSize.Large:
    struct: EnumItem
  Enum.AudioWindowSize.Medium:
    struct: EnumItem
  Enum.AudioWindowSize.Small:
    struct: EnumItem
  Enum.AutoIndentRule.Absolute:
    struct: EnumItem
  Enum.AutoIndentRule.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AutoIndentRule.Off:
    struct: EnumItem
  Enum.AutoIndentRule.Relative:
    struct: EnumItem
  Enum.AutomaticSize.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AutomaticSize.None:
    struct: EnumItem
  Enum.AutomaticSize.X:
    struct: EnumItem
  Enum.AutomaticSize.XY:
    struct: EnumItem
  Enum.AutomaticSize.Y:
    struct: EnumItem
  Enum.AvatarAssetType.BackAccessory:
    struct: EnumItem
  Enum.AvatarAssetType.ClimbAnimation:
    struct: EnumItem
  Enum.AvatarAssetType.DressSkirtAccessory:
    struct: EnumItem
  Enum.AvatarAssetType.DynamicHead:
    struct: EnumItem
  Enum.AvatarAssetType.EmoteAnimation:
    struct: EnumItem
  Enum.AvatarAssetType.EyebrowAccessory:
    struct: EnumItem
  Enum.AvatarAssetType.EyelashAccessory:
    struct: EnumItem
  Enum.AvatarAssetType.Face:
    struct: EnumItem
  Enum.AvatarAssetType.FaceAccessory:
    struct: EnumItem
  Enum.AvatarAssetType.FallAnimation:
    struct: EnumItem
  Enum.AvatarAssetType.FrontAccessory:
    struct: EnumItem
  Enum.AvatarAssetType.Gear:
    struct: EnumItem
  Enum.AvatarAssetType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AvatarAssetType.HairAccessory:
    struct: EnumItem
  Enum.AvatarAssetType.Hat:
    struct: EnumItem
  Enum.AvatarAssetType.Head:
    struct: EnumItem
  Enum.AvatarAssetType.IdleAnimation:
    struct: EnumItem
  Enum.AvatarAssetType.JacketAccessory:
    struct: EnumItem
  Enum.AvatarAssetType.JumpAnimation:
    struct: EnumItem
  Enum.AvatarAssetType.LeftArm:
    struct: EnumItem
  Enum.AvatarAssetType.LeftLeg:
    struct: EnumItem
  Enum.AvatarAssetType.LeftShoeAccessory:
    struct: EnumItem
  Enum.AvatarAssetType.MoodAnimation:
    struct: EnumItem
  Enum.AvatarAssetType.NeckAccessory:
    struct: EnumItem
  Enum.AvatarAssetType.Pants:
    struct: EnumItem
  Enum.AvatarAssetType.PantsAccessory:
    struct: EnumItem
  Enum.AvatarAssetType.RightArm:
    struct: EnumItem
  Enum.AvatarAssetType.RightLeg:
    struct: EnumItem
  Enum.AvatarAssetType.RightShoeAccessory:
    struct: EnumItem
  Enum.AvatarAssetType.RunAnimation:
    struct: EnumItem
  Enum.AvatarAssetType.Shirt:
    struct: EnumItem
  Enum.AvatarAssetType.ShirtAccessory:
    struct: EnumItem
  Enum.AvatarAssetType.ShortsAccessory:
    struct: EnumItem
  Enum.AvatarAssetType.ShoulderAccessory:
    struct: EnumItem
  Enum.AvatarAssetType.SweaterAccessory:
    struct: EnumItem
  Enum.AvatarAssetType.SwimAnimation:
    struct: EnumItem
  Enum.AvatarAssetType.TShirt:
    struct: EnumItem
  Enum.AvatarAssetType.TShirtAccessory:
    struct: EnumItem
  Enum.AvatarAssetType.TeeShirtAccessory:
    struct: EnumItem
    deprecated:
      message: Enum.AvatarAssetType.TeeShirtAccessory was replaced with Enum.AvatarAssetType.TShirtAccessory
      replace:
      - Enum.AvatarAssetType.TShirtAccessory
  Enum.AvatarAssetType.Torso:
    struct: EnumItem
  Enum.AvatarAssetType.WaistAccessory:
    struct: EnumItem
  Enum.AvatarAssetType.WalkAnimation:
    struct: EnumItem
  Enum.AvatarChatServiceFeature.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AvatarChatServiceFeature.None:
    struct: EnumItem
  Enum.AvatarChatServiceFeature.PlaceAudio:
    struct: EnumItem
  Enum.AvatarChatServiceFeature.PlaceVideo:
    struct: EnumItem
  Enum.AvatarChatServiceFeature.UniverseAudio:
    struct: EnumItem
  Enum.AvatarChatServiceFeature.UniverseVideo:
    struct: EnumItem
  Enum.AvatarChatServiceFeature.UserAudio:
    struct: EnumItem
  Enum.AvatarChatServiceFeature.UserAudioEligible:
    struct: EnumItem
  Enum.AvatarChatServiceFeature.UserBanned:
    struct: EnumItem
  Enum.AvatarChatServiceFeature.UserVerifiedForVoice:
    struct: EnumItem
  Enum.AvatarChatServiceFeature.UserVideo:
    struct: EnumItem
  Enum.AvatarChatServiceFeature.UserVideoEligible:
    struct: EnumItem
  Enum.AvatarContextMenuOption.Chat:
    struct: EnumItem
  Enum.AvatarContextMenuOption.Emote:
    struct: EnumItem
  Enum.AvatarContextMenuOption.Friend:
    struct: EnumItem
  Enum.AvatarContextMenuOption.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AvatarContextMenuOption.InspectMenu:
    struct: EnumItem
  Enum.AvatarGenerationError.Canceled:
    struct: EnumItem
  Enum.AvatarGenerationError.DownloadFailed:
    struct: EnumItem
  Enum.AvatarGenerationError.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AvatarGenerationError.JobNotFound:
    struct: EnumItem
  Enum.AvatarGenerationError.None:
    struct: EnumItem
  Enum.AvatarGenerationError.Offensive:
    struct: EnumItem
  Enum.AvatarGenerationError.Timeout:
    struct: EnumItem
  Enum.AvatarGenerationError.Unknown:
    struct: EnumItem
  Enum.AvatarItemType.Asset:
    struct: EnumItem
  Enum.AvatarItemType.Bundle:
    struct: EnumItem
  Enum.AvatarItemType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AvatarPromptResult.Failed:
    struct: EnumItem
  Enum.AvatarPromptResult.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AvatarPromptResult.PermissionDenied:
    struct: EnumItem
  Enum.AvatarPromptResult.Success:
    struct: EnumItem
  Enum.AvatarThumbnailCustomizationType.Closeup:
    struct: EnumItem
  Enum.AvatarThumbnailCustomizationType.FullBody:
    struct: EnumItem
  Enum.AvatarThumbnailCustomizationType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.AvatarUnificationMode.Default:
    struct: EnumItem
  Enum.AvatarUnificationMode.Disabled:
    struct: EnumItem
  Enum.AvatarUnificationMode.Enabled:
    struct: EnumItem
  Enum.AvatarUnificationMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.Axis.Back:
    struct: EnumItem
    deprecated:
      message: Enum.Axis.Back was replaced with Enum.Axis.Z
      replace:
      - Enum.Axis.Z
  Enum.Axis.Bottom:
    struct: EnumItem
    deprecated:
      message: Enum.Axis.Bottom was replaced with Enum.Axis.Y
      replace:
      - Enum.Axis.Y
  Enum.Axis.Front:
    struct: EnumItem
    deprecated:
      message: Enum.Axis.Front was replaced with Enum.Axis.Z
      replace:
      - Enum.Axis.Z
  Enum.Axis.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.Axis.Left:
    struct: EnumItem
    deprecated:
      message: Enum.Axis.Left was replaced with Enum.Axis.X
      replace:
      - Enum.Axis.X
  Enum.Axis.Right:
    struct: EnumItem
    deprecated:
      message: Enum.Axis.Right was replaced with Enum.Axis.X
      replace:
      - Enum.Axis.X
  Enum.Axis.Top:
    struct: EnumItem
    deprecated:
      message: Enum.Axis.Top was replaced with Enum.Axis.Y
      replace:
      - Enum.Axis.Y
  Enum.Axis.X:
    struct: EnumItem
  Enum.Axis.Y:
    struct: EnumItem
  Enum.Axis.Z:
    struct: EnumItem
  Enum.BenefitType.AvatarAsset:
    struct: EnumItem
  Enum.BenefitType.AvatarBundle:
    struct: EnumItem
  Enum.BenefitType.DeveloperProduct:
    struct: EnumItem
  Enum.BenefitType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.BinType.Clone:
    struct: EnumItem
  Enum.BinType.GameTool:
    struct: EnumItem
  Enum.BinType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.BinType.Grab:
    struct: EnumItem
  Enum.BinType.Hammer:
    struct: EnumItem
  Enum.BinType.Laser:
    struct: EnumItem
    deprecated:
      message: Enum.BinType.Laser was replaced with Enum.BinType.Script
      replace:
      - Enum.BinType.Script
  Enum.BinType.Rocket:
    struct: EnumItem
    deprecated:
      message: Enum.BinType.Rocket was replaced with Enum.BinType.Script
      replace:
      - Enum.BinType.Script
  Enum.BinType.Script:
    struct: EnumItem
  Enum.BinType.Slingshot:
    struct: EnumItem
    deprecated:
      message: Enum.BinType.Slingshot was replaced with Enum.BinType.Script
      replace:
      - Enum.BinType.Script
  Enum.BodyPart.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.BodyPart.Head:
    struct: EnumItem
  Enum.BodyPart.LeftArm:
    struct: EnumItem
  Enum.BodyPart.LeftLeg:
    struct: EnumItem
  Enum.BodyPart.RightArm:
    struct: EnumItem
  Enum.BodyPart.RightLeg:
    struct: EnumItem
  Enum.BodyPart.Torso:
    struct: EnumItem
  Enum.BodyPartR15.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.BodyPartR15.Head:
    struct: EnumItem
  Enum.BodyPartR15.LeftFoot:
    struct: EnumItem
  Enum.BodyPartR15.LeftHand:
    struct: EnumItem
  Enum.BodyPartR15.LeftLowerArm:
    struct: EnumItem
  Enum.BodyPartR15.LeftLowerLeg:
    struct: EnumItem
  Enum.BodyPartR15.LeftUpperArm:
    struct: EnumItem
  Enum.BodyPartR15.LeftUpperLeg:
    struct: EnumItem
  Enum.BodyPartR15.LowerTorso:
    struct: EnumItem
  Enum.BodyPartR15.RightFoot:
    struct: EnumItem
  Enum.BodyPartR15.RightHand:
    struct: EnumItem
  Enum.BodyPartR15.RightLowerArm:
    struct: EnumItem
  Enum.BodyPartR15.RightLowerLeg:
    struct: EnumItem
  Enum.BodyPartR15.RightUpperArm:
    struct: EnumItem
  Enum.BodyPartR15.RightUpperLeg:
    struct: EnumItem
  Enum.BodyPartR15.RootPart:
    struct: EnumItem
  Enum.BodyPartR15.Unknown:
    struct: EnumItem
  Enum.BodyPartR15.UpperTorso:
    struct: EnumItem
  Enum.BorderMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.BorderMode.Inset:
    struct: EnumItem
  Enum.BorderMode.Middle:
    struct: EnumItem
  Enum.BorderMode.Outline:
    struct: EnumItem
  Enum.BreakReason.Error:
    struct: EnumItem
  Enum.BreakReason.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.BreakReason.Other:
    struct: EnumItem
  Enum.BreakReason.SpecialBreakpoint:
    struct: EnumItem
  Enum.BreakReason.UserBreakpoint:
    struct: EnumItem
  Enum.BreakpointRemoveReason.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.BreakpointRemoveReason.Requested:
    struct: EnumItem
  Enum.BreakpointRemoveReason.ScriptChanged:
    struct: EnumItem
  Enum.BreakpointRemoveReason.ScriptRemoved:
    struct: EnumItem
  Enum.BulkMoveMode.FireAllEvents:
    struct: EnumItem
  Enum.BulkMoveMode.FireCFrameChanged:
    struct: EnumItem
  Enum.BulkMoveMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.BundleType.Animations:
    struct: EnumItem
  Enum.BundleType.BodyParts:
    struct: EnumItem
  Enum.BundleType.DynamicHead:
    struct: EnumItem
  Enum.BundleType.DynamicHeadAvatar:
    struct: EnumItem
  Enum.BundleType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.BundleType.Shoes:
    struct: EnumItem
  Enum.Button.Dismount:
    struct: EnumItem
  Enum.Button.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.Button.Jump:
    struct: EnumItem
  Enum.ButtonStyle.Custom:
    struct: EnumItem
  Enum.ButtonStyle.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ButtonStyle.RobloxButton:
    struct: EnumItem
  Enum.ButtonStyle.RobloxButtonDefault:
    struct: EnumItem
  Enum.ButtonStyle.RobloxRoundButton:
    struct: EnumItem
  Enum.ButtonStyle.RobloxRoundDefaultButton:
    struct: EnumItem
  Enum.ButtonStyle.RobloxRoundDropdownButton:
    struct: EnumItem
  Enum.CageType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CageType.Inner:
    struct: EnumItem
  Enum.CageType.Outer:
    struct: EnumItem
  Enum.CameraMode.Classic:
    struct: EnumItem
  Enum.CameraMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CameraMode.LockFirstPerson:
    struct: EnumItem
  Enum.CameraPanMode.Classic:
    struct: EnumItem
  Enum.CameraPanMode.EdgeBump:
    struct: EnumItem
  Enum.CameraPanMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CameraSpeedAdjustBinding.AltScroll:
    struct: EnumItem
  Enum.CameraSpeedAdjustBinding.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CameraSpeedAdjustBinding.None:
    struct: EnumItem
  Enum.CameraSpeedAdjustBinding.RmbScroll:
    struct: EnumItem
  Enum.CameraType.Attach:
    struct: EnumItem
  Enum.CameraType.Custom:
    struct: EnumItem
  Enum.CameraType.Fixed:
    struct: EnumItem
  Enum.CameraType.Follow:
    struct: EnumItem
  Enum.CameraType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CameraType.Orbital:
    struct: EnumItem
  Enum.CameraType.Scriptable:
    struct: EnumItem
  Enum.CameraType.Track:
    struct: EnumItem
  Enum.CameraType.Watch:
    struct: EnumItem
  Enum.CatalogCategoryFilter.Collectibles:
    struct: EnumItem
  Enum.CatalogCategoryFilter.CommunityCreations:
    struct: EnumItem
  Enum.CatalogCategoryFilter.Featured:
    struct: EnumItem
  Enum.CatalogCategoryFilter.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CatalogCategoryFilter.None:
    struct: EnumItem
  Enum.CatalogCategoryFilter.Premium:
    struct: EnumItem
  Enum.CatalogCategoryFilter.Recommended:
    struct: EnumItem
  Enum.CatalogSortAggregation.AllTime:
    struct: EnumItem
  Enum.CatalogSortAggregation.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CatalogSortAggregation.Past12Hours:
    struct: EnumItem
  Enum.CatalogSortAggregation.Past3Days:
    struct: EnumItem
  Enum.CatalogSortAggregation.PastDay:
    struct: EnumItem
  Enum.CatalogSortAggregation.PastMonth:
    struct: EnumItem
  Enum.CatalogSortAggregation.PastWeek:
    struct: EnumItem
  Enum.CatalogSortType.Bestselling:
    struct: EnumItem
  Enum.CatalogSortType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CatalogSortType.MostFavorited:
    struct: EnumItem
  Enum.CatalogSortType.PriceHighToLow:
    struct: EnumItem
  Enum.CatalogSortType.PriceLowToHigh:
    struct: EnumItem
  Enum.CatalogSortType.RecentlyCreated:
    struct: EnumItem
  Enum.CatalogSortType.RecentlyUpdated:
    struct: EnumItem
    deprecated:
      message: Enum.CatalogSortType.RecentlyUpdated was replaced with Enum.CatalogSortType.RecentlyCreated
      replace:
      - Enum.CatalogSortType.RecentlyCreated
  Enum.CatalogSortType.Relevance:
    struct: EnumItem
  Enum.CellBlock.CornerWedge:
    struct: EnumItem
  Enum.CellBlock.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CellBlock.HorizontalWedge:
    struct: EnumItem
  Enum.CellBlock.InverseCornerWedge:
    struct: EnumItem
  Enum.CellBlock.Solid:
    struct: EnumItem
  Enum.CellBlock.VerticalWedge:
    struct: EnumItem
  Enum.CellMaterial.Aluminum:
    struct: EnumItem
  Enum.CellMaterial.Asphalt:
    struct: EnumItem
  Enum.CellMaterial.BluePlastic:
    struct: EnumItem
  Enum.CellMaterial.Brick:
    struct: EnumItem
  Enum.CellMaterial.Cement:
    struct: EnumItem
  Enum.CellMaterial.CinderBlock:
    struct: EnumItem
  Enum.CellMaterial.Empty:
    struct: EnumItem
  Enum.CellMaterial.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CellMaterial.Gold:
    struct: EnumItem
  Enum.CellMaterial.Granite:
    struct: EnumItem
  Enum.CellMaterial.Grass:
    struct: EnumItem
  Enum.CellMaterial.Gravel:
    struct: EnumItem
  Enum.CellMaterial.Iron:
    struct: EnumItem
  Enum.CellMaterial.MossyStone:
    struct: EnumItem
  Enum.CellMaterial.RedPlastic:
    struct: EnumItem
  Enum.CellMaterial.Sand:
    struct: EnumItem
  Enum.CellMaterial.Water:
    struct: EnumItem
  Enum.CellMaterial.WoodLog:
    struct: EnumItem
  Enum.CellMaterial.WoodPlank:
    struct: EnumItem
  Enum.CellOrientation.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CellOrientation.NegX:
    struct: EnumItem
  Enum.CellOrientation.NegZ:
    struct: EnumItem
  Enum.CellOrientation.X:
    struct: EnumItem
  Enum.CellOrientation.Z:
    struct: EnumItem
  Enum.CenterDialogType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CenterDialogType.ModalDialog:
    struct: EnumItem
  Enum.CenterDialogType.PlayerInitiatedDialog:
    struct: EnumItem
  Enum.CenterDialogType.QuitDialog:
    struct: EnumItem
  Enum.CenterDialogType.UnsolicitedDialog:
    struct: EnumItem
  Enum.CharacterControlMode.Default:
    struct: EnumItem
  Enum.CharacterControlMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CharacterControlMode.Legacy:
    struct: EnumItem
  Enum.CharacterControlMode.LuaCharacterController:
    struct: EnumItem
  Enum.CharacterControlMode.NoCharacterController:
    struct: EnumItem
  Enum.ChatCallbackType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ChatCallbackType.OnClientFormattingMessage:
    struct: EnumItem
  Enum.ChatCallbackType.OnClientSendingMessage:
    struct: EnumItem
  Enum.ChatCallbackType.OnCreatingChatWindow:
    struct: EnumItem
  Enum.ChatCallbackType.OnServerReceivingMessage:
    struct: EnumItem
  Enum.ChatColor.Blue:
    struct: EnumItem
  Enum.ChatColor.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ChatColor.Green:
    struct: EnumItem
  Enum.ChatColor.Red:
    struct: EnumItem
  Enum.ChatColor.White:
    struct: EnumItem
  Enum.ChatMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ChatMode.Menu:
    struct: EnumItem
  Enum.ChatMode.TextAndMenu:
    struct: EnumItem
  Enum.ChatPrivacyMode.AllUsers:
    struct: EnumItem
  Enum.ChatPrivacyMode.Friends:
    struct: EnumItem
  Enum.ChatPrivacyMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ChatPrivacyMode.NoOne:
    struct: EnumItem
  Enum.ChatRestrictionStatus.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ChatRestrictionStatus.NotRestricted:
    struct: EnumItem
  Enum.ChatRestrictionStatus.Restricted:
    struct: EnumItem
  Enum.ChatRestrictionStatus.Unknown:
    struct: EnumItem
  Enum.ChatStyle.Bubble:
    struct: EnumItem
  Enum.ChatStyle.Classic:
    struct: EnumItem
  Enum.ChatStyle.ClassicAndBubble:
    struct: EnumItem
  Enum.ChatStyle.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ChatVersion.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ChatVersion.LegacyChatService:
    struct: EnumItem
  Enum.ChatVersion.TextChatService:
    struct: EnumItem
  Enum.ClientAnimatorThrottlingMode.Default:
    struct: EnumItem
  Enum.ClientAnimatorThrottlingMode.Disabled:
    struct: EnumItem
  Enum.ClientAnimatorThrottlingMode.Enabled:
    struct: EnumItem
  Enum.ClientAnimatorThrottlingMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CloseReason.DeveloperShutdown:
    struct: EnumItem
  Enum.CloseReason.DeveloperUpdate:
    struct: EnumItem
  Enum.CloseReason.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CloseReason.OutOfMemory:
    struct: EnumItem
  Enum.CloseReason.RobloxMaintenance:
    struct: EnumItem
  Enum.CloseReason.ServerEmpty:
    struct: EnumItem
  Enum.CloseReason.Unknown:
    struct: EnumItem
  Enum.CollaboratorStatus.Editing3D:
    struct: EnumItem
  Enum.CollaboratorStatus.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CollaboratorStatus.None:
    struct: EnumItem
  Enum.CollaboratorStatus.PrivateScripting:
    struct: EnumItem
  Enum.CollaboratorStatus.Scripting:
    struct: EnumItem
  Enum.CollisionFidelity.Box:
    struct: EnumItem
  Enum.CollisionFidelity.Default:
    struct: EnumItem
  Enum.CollisionFidelity.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CollisionFidelity.Hull:
    struct: EnumItem
  Enum.CollisionFidelity.PreciseConvexDecomposition:
    struct: EnumItem
  Enum.CommandPermission.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CommandPermission.LocalUser:
    struct: EnumItem
  Enum.CommandPermission.Plugin:
    struct: EnumItem
  Enum.CompileTarget.Client:
    struct: EnumItem
  Enum.CompileTarget.CoreScript:
    struct: EnumItem
  Enum.CompileTarget.CoreScriptRaw:
    struct: EnumItem
  Enum.CompileTarget.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CompileTarget.Studio:
    struct: EnumItem
  Enum.CompletionAcceptanceBehavior.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CompletionAcceptanceBehavior.Insert:
    struct: EnumItem
  Enum.CompletionAcceptanceBehavior.InsertOnEnterReplaceOnTab:
    struct: EnumItem
  Enum.CompletionAcceptanceBehavior.Replace:
    struct: EnumItem
  Enum.CompletionAcceptanceBehavior.ReplaceOnEnterInsertOnTab:
    struct: EnumItem
  Enum.CompletionItemKind.Class:
    struct: EnumItem
  Enum.CompletionItemKind.Color:
    struct: EnumItem
  Enum.CompletionItemKind.Constant:
    struct: EnumItem
  Enum.CompletionItemKind.Constructor:
    struct: EnumItem
  Enum.CompletionItemKind.Enum:
    struct: EnumItem
  Enum.CompletionItemKind.EnumMember:
    struct: EnumItem
  Enum.CompletionItemKind.Event:
    struct: EnumItem
  Enum.CompletionItemKind.Field:
    struct: EnumItem
  Enum.CompletionItemKind.File:
    struct: EnumItem
  Enum.CompletionItemKind.Folder:
    struct: EnumItem
  Enum.CompletionItemKind.Function:
    struct: EnumItem
  Enum.CompletionItemKind.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CompletionItemKind.Interface:
    struct: EnumItem
  Enum.CompletionItemKind.Keyword:
    struct: EnumItem
  Enum.CompletionItemKind.Method:
    struct: EnumItem
  Enum.CompletionItemKind.Module:
    struct: EnumItem
  Enum.CompletionItemKind.Operator:
    struct: EnumItem
  Enum.CompletionItemKind.Property:
    struct: EnumItem
  Enum.CompletionItemKind.Reference:
    struct: EnumItem
  Enum.CompletionItemKind.Snippet:
    struct: EnumItem
  Enum.CompletionItemKind.Struct:
    struct: EnumItem
  Enum.CompletionItemKind.Text:
    struct: EnumItem
  Enum.CompletionItemKind.TypeParameter:
    struct: EnumItem
  Enum.CompletionItemKind.Unit:
    struct: EnumItem
  Enum.CompletionItemKind.Value:
    struct: EnumItem
  Enum.CompletionItemKind.Variable:
    struct: EnumItem
  Enum.CompletionItemTag.AddParens:
    struct: EnumItem
  Enum.CompletionItemTag.ClientServerBoundaryViolation:
    struct: EnumItem
  Enum.CompletionItemTag.CommandLinePermissions:
    struct: EnumItem
  Enum.CompletionItemTag.Deprecated:
    struct: EnumItem
  Enum.CompletionItemTag.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CompletionItemTag.IncorrectIndexType:
    struct: EnumItem
  Enum.CompletionItemTag.Invalidated:
    struct: EnumItem
  Enum.CompletionItemTag.PluginPermissions:
    struct: EnumItem
  Enum.CompletionItemTag.PutCursorBeforeEnd:
    struct: EnumItem
  Enum.CompletionItemTag.PutCursorInParens:
    struct: EnumItem
  Enum.CompletionItemTag.RobloxPermissions:
    struct: EnumItem
  Enum.CompletionItemTag.TypeCorrect:
    struct: EnumItem
  Enum.CompletionTriggerKind.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CompletionTriggerKind.Invoked:
    struct: EnumItem
  Enum.CompletionTriggerKind.TriggerCharacter:
    struct: EnumItem
  Enum.CompletionTriggerKind.TriggerForIncompleteCompletions:
    struct: EnumItem
  Enum.ComputerCameraMovementMode.CameraToggle:
    struct: EnumItem
  Enum.ComputerCameraMovementMode.Classic:
    struct: EnumItem
  Enum.ComputerCameraMovementMode.Default:
    struct: EnumItem
  Enum.ComputerCameraMovementMode.Follow:
    struct: EnumItem
  Enum.ComputerCameraMovementMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ComputerCameraMovementMode.Orbital:
    struct: EnumItem
  Enum.ComputerMovementMode.ClickToMove:
    struct: EnumItem
  Enum.ComputerMovementMode.Default:
    struct: EnumItem
  Enum.ComputerMovementMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ComputerMovementMode.KeyboardMouse:
    struct: EnumItem
  Enum.ConfigSnapshotErrorState.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ConfigSnapshotErrorState.LoadFailed:
    struct: EnumItem
  Enum.ConfigSnapshotErrorState.None:
    struct: EnumItem
  Enum.ConnectionError.AndroidAnticheatKick:
    struct: EnumItem
  Enum.ConnectionError.AndroidEmulatorKick:
    struct: EnumItem
  Enum.ConnectionError.DisconnectBadhash:
    struct: EnumItem
  Enum.ConnectionError.DisconnectBlockedIP:
    struct: EnumItem
  Enum.ConnectionError.DisconnectBySecurityPolicy:
    struct: EnumItem
  Enum.ConnectionError.DisconnectClientFailure:
    struct: EnumItem
  Enum.ConnectionError.DisconnectClientRequest:
    struct: EnumItem
  Enum.ConnectionError.DisconnectCloudEditKick:
    struct: EnumItem
  Enum.ConnectionError.DisconnectCollaboratorPermissionRevoked:
    struct: EnumItem
  Enum.ConnectionError.DisconnectCollaboratorUnderage:
    struct: EnumItem
  Enum.ConnectionError.DisconnectConnectionLost:
    struct: EnumItem
  Enum.ConnectionError.DisconnectDevMaintenance:
    struct: EnumItem
  Enum.ConnectionError.DisconnectDuplicatePlayer:
    struct: EnumItem
  Enum.ConnectionError.DisconnectDuplicateTicket:
    struct: EnumItem
  Enum.ConnectionError.DisconnectErrors:
    struct: EnumItem
  Enum.ConnectionError.DisconnectEvicted:
    struct: EnumItem
  Enum.ConnectionError.DisconnectHashTimeout:
    struct: EnumItem
  Enum.ConnectionError.DisconnectIdle:
    struct: EnumItem
  Enum.ConnectionError.DisconnectIllegalTeleport:
    struct: EnumItem
  Enum.ConnectionError.DisconnectLuaKick:
    struct: EnumItem
  Enum.ConnectionError.DisconnectModeratedGame:
    struct: EnumItem
  Enum.ConnectionError.DisconnectNewSecurityKeyMismatch:
    struct: EnumItem
  Enum.ConnectionError.DisconnectOnRemoteSysStats:
    struct: EnumItem
  Enum.ConnectionError.DisconnectOutOfMemoryKeepPlayingLeave:
    struct: EnumItem
  Enum.ConnectionError.DisconnectPlayerless:
    struct: EnumItem
  Enum.ConnectionError.DisconnectPrivateServerKickout:
    struct: EnumItem
  Enum.ConnectionError.DisconnectProtocolMismatch:
    struct: EnumItem
  Enum.ConnectionError.DisconnectRaknetErrors:
    struct: EnumItem
  Enum.ConnectionError.DisconnectReceivePacketError:
    struct: EnumItem
  Enum.ConnectionError.DisconnectReceivePacketStreamError:
    struct: EnumItem
  Enum.ConnectionError.DisconnectRejoin:
    struct: EnumItem
  Enum.ConnectionError.DisconnectRobloxMaintenance:
    struct: EnumItem
  Enum.ConnectionError.DisconnectRomarkEndOfTest:
    struct: EnumItem
  Enum.ConnectionError.DisconnectSecurityKeyMismatch:
    struct: EnumItem
  Enum.ConnectionError.DisconnectSendPacketError:
    struct: EnumItem
  Enum.ConnectionError.DisconnectTimeout:
    struct: EnumItem
  Enum.ConnectionError.DisconnectWrongVersion:
    struct: EnumItem
  Enum.ConnectionError.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ConnectionError.NetworkInternal:
    struct: EnumItem
  Enum.ConnectionError.NetworkMisbehavior:
    struct: EnumItem
  Enum.ConnectionError.NetworkSecurity:
    struct: EnumItem
  Enum.ConnectionError.NetworkSend:
    struct: EnumItem
  Enum.ConnectionError.NetworkTimeout:
    struct: EnumItem
  Enum.ConnectionError.OK:
    struct: EnumItem
  Enum.ConnectionError.PhantomFreeze:
    struct: EnumItem
  Enum.ConnectionError.PlacelaunchCreatorBan:
    struct: EnumItem
  Enum.ConnectionError.PlacelaunchCustomMessage:
    struct: EnumItem
  Enum.ConnectionError.PlacelaunchDisabled:
    struct: EnumItem
  Enum.ConnectionError.PlacelaunchError:
    struct: EnumItem
  Enum.ConnectionError.PlacelaunchErrors:
    struct: EnumItem
  Enum.ConnectionError.PlacelaunchFlooded:
    struct: EnumItem
  Enum.ConnectionError.PlacelaunchGameEnded:
    struct: EnumItem
  Enum.ConnectionError.PlacelaunchGameFull:
    struct: EnumItem
  Enum.ConnectionError.PlacelaunchHashException:
    struct: EnumItem
  Enum.ConnectionError.PlacelaunchHashExpired:
    struct: EnumItem
  Enum.ConnectionError.PlacelaunchHttpError:
    struct: EnumItem
  Enum.ConnectionError.PlacelaunchOtherError:
    struct: EnumItem
  Enum.ConnectionError.PlacelaunchPartyCannotFit:
    struct: EnumItem
  Enum.ConnectionError.PlacelaunchRestricted:
    struct: EnumItem
  Enum.ConnectionError.PlacelaunchUnauthorized:
    struct: EnumItem
  Enum.ConnectionError.PlacelaunchUserLeft:
    struct: EnumItem
  Enum.ConnectionError.PlacelaunchUserPrivacyUnauthorized:
    struct: EnumItem
  Enum.ConnectionError.PlayerRemoved:
    struct: EnumItem
  Enum.ConnectionError.ReplacementReady:
    struct: EnumItem
  Enum.ConnectionError.ReplicatorTimeout:
    struct: EnumItem
  Enum.ConnectionError.ServerEmpty:
    struct: EnumItem
  Enum.ConnectionError.ServerShutdown:
    struct: EnumItem
  Enum.ConnectionError.TeleportErrors:
    struct: EnumItem
  Enum.ConnectionError.TeleportFailure:
    struct: EnumItem
  Enum.ConnectionError.TeleportFlooded:
    struct: EnumItem
  Enum.ConnectionError.TeleportGameEnded:
    struct: EnumItem
  Enum.ConnectionError.TeleportGameFull:
    struct: EnumItem
  Enum.ConnectionError.TeleportGameNotFound:
    struct: EnumItem
  Enum.ConnectionError.TeleportIsTeleporting:
    struct: EnumItem
  Enum.ConnectionError.TeleportUnauthorized:
    struct: EnumItem
  Enum.ConnectionError.Unknown:
    struct: EnumItem
  Enum.ConnectionState.Connected:
    struct: EnumItem
  Enum.ConnectionState.Disconnected:
    struct: EnumItem
  Enum.ConnectionState.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ContentSourceType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ContentSourceType.None:
    struct: EnumItem
  Enum.ContentSourceType.Object:
    struct: EnumItem
  Enum.ContentSourceType.Uri:
    struct: EnumItem
  Enum.ContextActionPriority.Default:
    struct: EnumItem
    deprecated:
      message: Enum.ContextActionPriority.Default was replaced with Enum.ContextActionPriority.Medium
      replace:
      - Enum.ContextActionPriority.Medium
  Enum.ContextActionPriority.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ContextActionPriority.High:
    struct: EnumItem
  Enum.ContextActionPriority.Low:
    struct: EnumItem
  Enum.ContextActionPriority.Medium:
    struct: EnumItem
  Enum.ContextActionResult.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ContextActionResult.Pass:
    struct: EnumItem
  Enum.ContextActionResult.Sink:
    struct: EnumItem
  Enum.ControlMode.Classic:
    struct: EnumItem
  Enum.ControlMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ControlMode.Mouse Lock Switch:
    struct: EnumItem
    deprecated:
      message: Enum.ControlMode.Mouse Lock Switch was replaced with Enum.ControlMode.MouseLockSwitch
      replace:
      - Enum.ControlMode.MouseLockSwitch
  Enum.ControlMode.MouseLockSwitch:
    struct: EnumItem
  Enum.CoreGuiType.All:
    struct: EnumItem
  Enum.CoreGuiType.Backpack:
    struct: EnumItem
  Enum.CoreGuiType.Captures:
    struct: EnumItem
  Enum.CoreGuiType.Chat:
    struct: EnumItem
  Enum.CoreGuiType.EmotesMenu:
    struct: EnumItem
  Enum.CoreGuiType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CoreGuiType.Health:
    struct: EnumItem
  Enum.CoreGuiType.PlayerList:
    struct: EnumItem
  Enum.CoreGuiType.SelfView:
    struct: EnumItem
  Enum.CreateAssetResult.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CreateAssetResult.PermissionDenied:
    struct: EnumItem
  Enum.CreateAssetResult.Success:
    struct: EnumItem
  Enum.CreateAssetResult.Unknown:
    struct: EnumItem
  Enum.CreateAssetResult.UploadFailed:
    struct: EnumItem
  Enum.CreateOutfitFailure.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CreateOutfitFailure.InvalidName:
    struct: EnumItem
  Enum.CreateOutfitFailure.Other:
    struct: EnumItem
  Enum.CreateOutfitFailure.OutfitLimitReached:
    struct: EnumItem
  Enum.CreatorType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CreatorType.Group:
    struct: EnumItem
  Enum.CreatorType.User:
    struct: EnumItem
  Enum.CreatorTypeFilter.All:
    struct: EnumItem
  Enum.CreatorTypeFilter.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CreatorTypeFilter.Group:
    struct: EnumItem
  Enum.CreatorTypeFilter.User:
    struct: EnumItem
  Enum.CurrencyType.Default:
    struct: EnumItem
  Enum.CurrencyType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.CurrencyType.Robux:
    struct: EnumItem
  Enum.CurrencyType.Tix:
    struct: EnumItem
  Enum.CustomCameraMode.Classic:
    struct: EnumItem
  Enum.CustomCameraMode.Default:
    struct: EnumItem
  Enum.CustomCameraMode.Follow:
    struct: EnumItem
  Enum.CustomCameraMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DataStoreRequestType.GetAsync:
    struct: EnumItem
  Enum.DataStoreRequestType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DataStoreRequestType.GetSortedAsync:
    struct: EnumItem
  Enum.DataStoreRequestType.GetVersionAsync:
    struct: EnumItem
  Enum.DataStoreRequestType.ListAsync:
    struct: EnumItem
  Enum.DataStoreRequestType.OnUpdate:
    struct: EnumItem
  Enum.DataStoreRequestType.RemoveVersionAsync:
    struct: EnumItem
  Enum.DataStoreRequestType.SetIncrementAsync:
    struct: EnumItem
  Enum.DataStoreRequestType.SetIncrementSortedAsync:
    struct: EnumItem
  Enum.DataStoreRequestType.UpdateAsync:
    struct: EnumItem
  Enum.DebuggerEndReason.ClientRequest:
    struct: EnumItem
  Enum.DebuggerEndReason.ConfigurationFailed:
    struct: EnumItem
  Enum.DebuggerEndReason.Disconnected:
    struct: EnumItem
  Enum.DebuggerEndReason.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DebuggerEndReason.InvalidHost:
    struct: EnumItem
  Enum.DebuggerEndReason.RpcError:
    struct: EnumItem
  Enum.DebuggerEndReason.ServerProtocolMismatch:
    struct: EnumItem
  Enum.DebuggerEndReason.ServerShutdown:
    struct: EnumItem
  Enum.DebuggerEndReason.Timeout:
    struct: EnumItem
  Enum.DebuggerExceptionBreakMode.Always:
    struct: EnumItem
  Enum.DebuggerExceptionBreakMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DebuggerExceptionBreakMode.Never:
    struct: EnumItem
  Enum.DebuggerExceptionBreakMode.Unhandled:
    struct: EnumItem
  Enum.DebuggerFrameType.C:
    struct: EnumItem
  Enum.DebuggerFrameType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DebuggerFrameType.Lua:
    struct: EnumItem
  Enum.DebuggerPauseReason.Breakpoint:
    struct: EnumItem
  Enum.DebuggerPauseReason.Entrypoint:
    struct: EnumItem
  Enum.DebuggerPauseReason.Exception:
    struct: EnumItem
  Enum.DebuggerPauseReason.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DebuggerPauseReason.Requested:
    struct: EnumItem
  Enum.DebuggerPauseReason.SingleStep:
    struct: EnumItem
  Enum.DebuggerPauseReason.Unknown:
    struct: EnumItem
  Enum.DebuggerStatus.ConnectionClosed:
    struct: EnumItem
  Enum.DebuggerStatus.ConnectionLost:
    struct: EnumItem
  Enum.DebuggerStatus.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DebuggerStatus.InternalError:
    struct: EnumItem
  Enum.DebuggerStatus.InvalidArgument:
    struct: EnumItem
  Enum.DebuggerStatus.InvalidResponse:
    struct: EnumItem
  Enum.DebuggerStatus.InvalidState:
    struct: EnumItem
  Enum.DebuggerStatus.RpcError:
    struct: EnumItem
  Enum.DebuggerStatus.Success:
    struct: EnumItem
  Enum.DebuggerStatus.Timeout:
    struct: EnumItem
  Enum.DevCameraOcclusionMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DevCameraOcclusionMode.Invisicam:
    struct: EnumItem
  Enum.DevCameraOcclusionMode.Zoom:
    struct: EnumItem
  Enum.DevComputerCameraMovementMode.CameraToggle:
    struct: EnumItem
  Enum.DevComputerCameraMovementMode.Classic:
    struct: EnumItem
  Enum.DevComputerCameraMovementMode.Follow:
    struct: EnumItem
  Enum.DevComputerCameraMovementMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DevComputerCameraMovementMode.Orbital:
    struct: EnumItem
  Enum.DevComputerCameraMovementMode.UserChoice:
    struct: EnumItem
  Enum.DevComputerMovementMode.ClickToMove:
    struct: EnumItem
  Enum.DevComputerMovementMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DevComputerMovementMode.KeyboardMouse:
    struct: EnumItem
  Enum.DevComputerMovementMode.Scriptable:
    struct: EnumItem
  Enum.DevComputerMovementMode.UserChoice:
    struct: EnumItem
  Enum.DevTouchCameraMovementMode.Classic:
    struct: EnumItem
  Enum.DevTouchCameraMovementMode.Follow:
    struct: EnumItem
  Enum.DevTouchCameraMovementMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DevTouchCameraMovementMode.Orbital:
    struct: EnumItem
  Enum.DevTouchCameraMovementMode.UserChoice:
    struct: EnumItem
  Enum.DevTouchMovementMode.ClickToMove:
    struct: EnumItem
  Enum.DevTouchMovementMode.DPad:
    struct: EnumItem
  Enum.DevTouchMovementMode.DynamicThumbstick:
    struct: EnumItem
  Enum.DevTouchMovementMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DevTouchMovementMode.Scriptable:
    struct: EnumItem
  Enum.DevTouchMovementMode.Thumbpad:
    struct: EnumItem
  Enum.DevTouchMovementMode.Thumbstick:
    struct: EnumItem
  Enum.DevTouchMovementMode.UserChoice:
    struct: EnumItem
  Enum.DeveloperMemoryTag.Animation:
    struct: EnumItem
  Enum.DeveloperMemoryTag.GeometryCSG:
    struct: EnumItem
  Enum.DeveloperMemoryTag.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DeveloperMemoryTag.GraphicsMeshParts:
    struct: EnumItem
  Enum.DeveloperMemoryTag.GraphicsParticles:
    struct: EnumItem
  Enum.DeveloperMemoryTag.GraphicsParts:
    struct: EnumItem
  Enum.DeveloperMemoryTag.GraphicsSolidModels:
    struct: EnumItem
  Enum.DeveloperMemoryTag.GraphicsSpatialHash:
    struct: EnumItem
  Enum.DeveloperMemoryTag.GraphicsTerrain:
    struct: EnumItem
  Enum.DeveloperMemoryTag.GraphicsTexture:
    struct: EnumItem
  Enum.DeveloperMemoryTag.GraphicsTextureCharacter:
    struct: EnumItem
  Enum.DeveloperMemoryTag.Gui:
    struct: EnumItem
  Enum.DeveloperMemoryTag.HttpCache:
    struct: EnumItem
  Enum.DeveloperMemoryTag.Instances:
    struct: EnumItem
  Enum.DeveloperMemoryTag.Internal:
    struct: EnumItem
  Enum.DeveloperMemoryTag.LuaHeap:
    struct: EnumItem
  Enum.DeveloperMemoryTag.Navigation:
    struct: EnumItem
  Enum.DeveloperMemoryTag.PhysicsCollision:
    struct: EnumItem
  Enum.DeveloperMemoryTag.PhysicsParts:
    struct: EnumItem
  Enum.DeveloperMemoryTag.Script:
    struct: EnumItem
  Enum.DeveloperMemoryTag.Signals:
    struct: EnumItem
  Enum.DeveloperMemoryTag.Sounds:
    struct: EnumItem
  Enum.DeveloperMemoryTag.StreamingSounds:
    struct: EnumItem
  Enum.DeveloperMemoryTag.TerrainVoxels:
    struct: EnumItem
  Enum.DeviceFeatureType.DeviceCapture:
    struct: EnumItem
  Enum.DeviceFeatureType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DeviceForm.Console:
    struct: EnumItem
  Enum.DeviceForm.Desktop:
    struct: EnumItem
  Enum.DeviceForm.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DeviceForm.Phone:
    struct: EnumItem
  Enum.DeviceForm.Tablet:
    struct: EnumItem
  Enum.DeviceForm.VR:
    struct: EnumItem
  Enum.DeviceLevel.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DeviceLevel.High:
    struct: EnumItem
  Enum.DeviceLevel.Low:
    struct: EnumItem
  Enum.DeviceLevel.Medium:
    struct: EnumItem
  Enum.DeviceType.Desktop:
    struct: EnumItem
  Enum.DeviceType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DeviceType.Phone:
    struct: EnumItem
  Enum.DeviceType.Tablet:
    struct: EnumItem
  Enum.DeviceType.Unknown:
    struct: EnumItem
  Enum.DialogBehaviorType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DialogBehaviorType.MultiplePlayers:
    struct: EnumItem
  Enum.DialogBehaviorType.SinglePlayer:
    struct: EnumItem
  Enum.DialogPurpose.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DialogPurpose.Help:
    struct: EnumItem
  Enum.DialogPurpose.Quest:
    struct: EnumItem
  Enum.DialogPurpose.Shop:
    struct: EnumItem
  Enum.DialogTone.Enemy:
    struct: EnumItem
  Enum.DialogTone.Friendly:
    struct: EnumItem
  Enum.DialogTone.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DialogTone.Neutral:
    struct: EnumItem
  Enum.DominantAxis.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DominantAxis.Height:
    struct: EnumItem
  Enum.DominantAxis.Width:
    struct: EnumItem
  Enum.DraftStatusCode.DraftCommitted:
    struct: EnumItem
  Enum.DraftStatusCode.DraftOutdated:
    struct: EnumItem
  Enum.DraftStatusCode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DraftStatusCode.OK:
    struct: EnumItem
  Enum.DraftStatusCode.ScriptRemoved:
    struct: EnumItem
  Enum.DragDetectorDragStyle.BestForDevice:
    struct: EnumItem
  Enum.DragDetectorDragStyle.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DragDetectorDragStyle.RotateAxis:
    struct: EnumItem
  Enum.DragDetectorDragStyle.RotateTrackball:
    struct: EnumItem
  Enum.DragDetectorDragStyle.Scriptable:
    struct: EnumItem
  Enum.DragDetectorDragStyle.TranslateLine:
    struct: EnumItem
  Enum.DragDetectorDragStyle.TranslateLineOrPlane:
    struct: EnumItem
  Enum.DragDetectorDragStyle.TranslatePlane:
    struct: EnumItem
  Enum.DragDetectorDragStyle.TranslatePlaneOrLine:
    struct: EnumItem
  Enum.DragDetectorDragStyle.TranslateViewPlane:
    struct: EnumItem
  Enum.DragDetectorPermissionPolicy.Everybody:
    struct: EnumItem
  Enum.DragDetectorPermissionPolicy.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DragDetectorPermissionPolicy.Nobody:
    struct: EnumItem
  Enum.DragDetectorPermissionPolicy.Scriptable:
    struct: EnumItem
  Enum.DragDetectorResponseStyle.Custom:
    struct: EnumItem
  Enum.DragDetectorResponseStyle.Geometric:
    struct: EnumItem
  Enum.DragDetectorResponseStyle.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DragDetectorResponseStyle.Physical:
    struct: EnumItem
  Enum.DraggerCoordinateSpace.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DraggerCoordinateSpace.Object:
    struct: EnumItem
  Enum.DraggerCoordinateSpace.World:
    struct: EnumItem
  Enum.DraggerMovementMode.Geometric:
    struct: EnumItem
  Enum.DraggerMovementMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DraggerMovementMode.Physical:
    struct: EnumItem
  Enum.DraggingScrollBar.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.DraggingScrollBar.Horizontal:
    struct: EnumItem
  Enum.DraggingScrollBar.None:
    struct: EnumItem
  Enum.DraggingScrollBar.Vertical:
    struct: EnumItem
  Enum.EasingDirection.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.EasingDirection.In:
    struct: EnumItem
  Enum.EasingDirection.InOut:
    struct: EnumItem
  Enum.EasingDirection.Out:
    struct: EnumItem
  Enum.EasingStyle.Back:
    struct: EnumItem
  Enum.EasingStyle.Bounce:
    struct: EnumItem
  Enum.EasingStyle.Circular:
    struct: EnumItem
  Enum.EasingStyle.Cubic:
    struct: EnumItem
  Enum.EasingStyle.Elastic:
    struct: EnumItem
  Enum.EasingStyle.Exponential:
    struct: EnumItem
  Enum.EasingStyle.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.EasingStyle.Linear:
    struct: EnumItem
  Enum.EasingStyle.Quad:
    struct: EnumItem
  Enum.EasingStyle.Quart:
    struct: EnumItem
  Enum.EasingStyle.Quint:
    struct: EnumItem
  Enum.EasingStyle.Sine:
    struct: EnumItem
  Enum.EditableStatus.Allowed:
    struct: EnumItem
  Enum.EditableStatus.Disallowed:
    struct: EnumItem
  Enum.EditableStatus.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.EditableStatus.Unknown:
    struct: EnumItem
  Enum.ElasticBehavior.Always:
    struct: EnumItem
  Enum.ElasticBehavior.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ElasticBehavior.Never:
    struct: EnumItem
  Enum.ElasticBehavior.WhenScrollable:
    struct: EnumItem
  Enum.EnviromentalPhysicsThrottle.Always:
    struct: EnumItem
  Enum.EnviromentalPhysicsThrottle.DefaultAuto:
    struct: EnumItem
  Enum.EnviromentalPhysicsThrottle.Disabled:
    struct: EnumItem
  Enum.EnviromentalPhysicsThrottle.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.EnviromentalPhysicsThrottle.Skip16:
    struct: EnumItem
  Enum.EnviromentalPhysicsThrottle.Skip2:
    struct: EnumItem
  Enum.EnviromentalPhysicsThrottle.Skip4:
    struct: EnumItem
  Enum.EnviromentalPhysicsThrottle.Skip8:
    struct: EnumItem
  Enum.ExperienceAuthScope.CreatorAssetsCreate:
    struct: EnumItem
  Enum.ExperienceAuthScope.DefaultScope:
    struct: EnumItem
  Enum.ExperienceAuthScope.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ExplosionType.Craters:
    struct: EnumItem
  Enum.ExplosionType.CratersAndDebris:
    struct: EnumItem
    deprecated:
      message: Enum.ExplosionType.CratersAndDebris was replaced with Enum.ExplosionType.Craters
      replace:
      - Enum.ExplosionType.Craters
  Enum.ExplosionType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ExplosionType.NoCraters:
    struct: EnumItem
  Enum.FACSDataLod.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.FACSDataLod.LOD0:
    struct: EnumItem
  Enum.FACSDataLod.LOD1:
    struct: EnumItem
  Enum.FACSDataLod.LODCount:
    struct: EnumItem
  Enum.FacialAnimationStreamingState.Audio:
    struct: EnumItem
  Enum.FacialAnimationStreamingState.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.FacialAnimationStreamingState.None:
    struct: EnumItem
  Enum.FacialAnimationStreamingState.Place:
    struct: EnumItem
  Enum.FacialAnimationStreamingState.Server:
    struct: EnumItem
  Enum.FacialAnimationStreamingState.Video:
    struct: EnumItem
  Enum.FieldOfViewMode.Diagonal:
    struct: EnumItem
  Enum.FieldOfViewMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.FieldOfViewMode.MaxAxis:
    struct: EnumItem
  Enum.FieldOfViewMode.Vertical:
    struct: EnumItem
  Enum.FillDirection.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.FillDirection.Horizontal:
    struct: EnumItem
  Enum.FillDirection.Vertical:
    struct: EnumItem
  Enum.FilterErrorType.BackslashNotEscapingAnything:
    struct: EnumItem
  Enum.FilterErrorType.BadBespokeFilter:
    struct: EnumItem
  Enum.FilterErrorType.BadName:
    struct: EnumItem
  Enum.FilterErrorType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.FilterErrorType.IncompleteOr:
    struct: EnumItem
  Enum.FilterErrorType.IncompleteParenthesis:
    struct: EnumItem
  Enum.FilterErrorType.InvalidDoubleStar:
    struct: EnumItem
  Enum.FilterErrorType.InvalidTilde:
    struct: EnumItem
  Enum.FilterErrorType.PropertyBadOperator:
    struct: EnumItem
  Enum.FilterErrorType.PropertyDoesNotExist:
    struct: EnumItem
  Enum.FilterErrorType.PropertyInvalidField:
    struct: EnumItem
  Enum.FilterErrorType.PropertyInvalidValue:
    struct: EnumItem
  Enum.FilterErrorType.PropertyUnsupportedFields:
    struct: EnumItem
  Enum.FilterErrorType.PropertyUnsupportedProperty:
    struct: EnumItem
  Enum.FilterErrorType.UnexpectedNameIndex:
    struct: EnumItem
  Enum.FilterErrorType.UnexpectedToken:
    struct: EnumItem
  Enum.FilterErrorType.UnfinishedBinaryOperator:
    struct: EnumItem
  Enum.FilterErrorType.UnfinishedQuote:
    struct: EnumItem
  Enum.FilterErrorType.UnknownBespokeFilter:
    struct: EnumItem
  Enum.FilterErrorType.WildcardInProperty:
    struct: EnumItem
  Enum.FilterResult.Accepted:
    struct: EnumItem
  Enum.FilterResult.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.FilterResult.Rejected:
    struct: EnumItem
  Enum.FinishRecordingOperation.Append:
    struct: EnumItem
  Enum.FinishRecordingOperation.Cancel:
    struct: EnumItem
  Enum.FinishRecordingOperation.Commit:
    struct: EnumItem
  Enum.FinishRecordingOperation.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.FluidFidelity.Automatic:
    struct: EnumItem
  Enum.FluidFidelity.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.FluidFidelity.UseCollisionGeometry:
    struct: EnumItem
  Enum.FluidFidelity.UsePreciseGeometry:
    struct: EnumItem
  Enum.FluidForces.Default:
    struct: EnumItem
  Enum.FluidForces.Experimental:
    struct: EnumItem
  Enum.FluidForces.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.Font.AmaticSC:
    struct: EnumItem
  Enum.Font.Antique:
    struct: EnumItem
  Enum.Font.Arcade:
    struct: EnumItem
  Enum.Font.Arial:
    struct: EnumItem
  Enum.Font.ArialBold:
    struct: EnumItem
  Enum.Font.Arimo:
    struct: EnumItem
  Enum.Font.ArimoBold:
    struct: EnumItem
  Enum.Font.Bangers:
    struct: EnumItem
  Enum.Font.Bodoni:
    struct: EnumItem
  Enum.Font.BuilderSans:
    struct: EnumItem
  Enum.Font.BuilderSansBold:
    struct: EnumItem
  Enum.Font.BuilderSansExtraBold:
    struct: EnumItem
  Enum.Font.BuilderSansMedium:
    struct: EnumItem
  Enum.Font.Cartoon:
    struct: EnumItem
  Enum.Font.Code:
    struct: EnumItem
  Enum.Font.Creepster:
    struct: EnumItem
  Enum.Font.DenkOne:
    struct: EnumItem
  Enum.Font.Fantasy:
    struct: EnumItem
  Enum.Font.Fondamento:
    struct: EnumItem
  Enum.Font.FredokaOne:
    struct: EnumItem
  Enum.Font.Garamond:
    struct: EnumItem
  Enum.Font.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.Font.Gotham:
    struct: EnumItem
  Enum.Font.GothamBlack:
    struct: EnumItem
  Enum.Font.GothamBold:
    struct: EnumItem
  Enum.Font.GothamMedium:
    struct: EnumItem
  Enum.Font.GothamSemibold:
    struct: EnumItem
    deprecated:
      message: Enum.Font.GothamSemibold was replaced with Enum.Font.GothamMedium
      replace:
      - Enum.Font.GothamMedium
  Enum.Font.GrenzeGotisch:
    struct: EnumItem
  Enum.Font.Highway:
    struct: EnumItem
  Enum.Font.IndieFlower:
    struct: EnumItem
  Enum.Font.JosefinSans:
    struct: EnumItem
  Enum.Font.Jura:
    struct: EnumItem
  Enum.Font.Kalam:
    struct: EnumItem
  Enum.Font.Legacy:
    struct: EnumItem
  Enum.Font.LuckiestGuy:
    struct: EnumItem
  Enum.Font.Merriweather:
    struct: EnumItem
  Enum.Font.Michroma:
    struct: EnumItem
  Enum.Font.Montserrat:
    struct: EnumItem
    deprecated:
      message: Enum.Font.Montserrat was replaced with Enum.Font.Gotham
      replace:
      - Enum.Font.Gotham
  Enum.Font.MontserratBlack:
    struct: EnumItem
    deprecated:
      message: Enum.Font.MontserratBlack was replaced with Enum.Font.GothamBlack
      replace:
      - Enum.Font.GothamBlack
  Enum.Font.MontserratBold:
    struct: EnumItem
    deprecated:
      message: Enum.Font.MontserratBold was replaced with Enum.Font.GothamBold
      replace:
      - Enum.Font.GothamBold
  Enum.Font.MontserratMedium:
    struct: EnumItem
    deprecated:
      message: Enum.Font.MontserratMedium was replaced with Enum.Font.GothamMedium
      replace:
      - Enum.Font.GothamMedium
  Enum.Font.Nunito:
    struct: EnumItem
  Enum.Font.Oswald:
    struct: EnumItem
  Enum.Font.PatrickHand:
    struct: EnumItem
  Enum.Font.PermanentMarker:
    struct: EnumItem
  Enum.Font.Roboto:
    struct: EnumItem
  Enum.Font.RobotoCondensed:
    struct: EnumItem
  Enum.Font.RobotoMono:
    struct: EnumItem
  Enum.Font.Sarpanch:
    struct: EnumItem
  Enum.Font.SciFi:
    struct: EnumItem
  Enum.Font.SourceSans:
    struct: EnumItem
  Enum.Font.SourceSansBold:
    struct: EnumItem
  Enum.Font.SourceSansItalic:
    struct: EnumItem
  Enum.Font.SourceSansLight:
    struct: EnumItem
  Enum.Font.SourceSansSemibold:
    struct: EnumItem
  Enum.Font.SpecialElite:
    struct: EnumItem
  Enum.Font.TitilliumWeb:
    struct: EnumItem
  Enum.Font.Ubuntu:
    struct: EnumItem
  Enum.Font.Unknown:
    struct: EnumItem
  Enum.FontSize.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.FontSize.Size10:
    struct: EnumItem
  Enum.FontSize.Size11:
    struct: EnumItem
  Enum.FontSize.Size12:
    struct: EnumItem
  Enum.FontSize.Size14:
    struct: EnumItem
  Enum.FontSize.Size18:
    struct: EnumItem
  Enum.FontSize.Size24:
    struct: EnumItem
  Enum.FontSize.Size28:
    struct: EnumItem
  Enum.FontSize.Size32:
    struct: EnumItem
  Enum.FontSize.Size36:
    struct: EnumItem
  Enum.FontSize.Size42:
    struct: EnumItem
  Enum.FontSize.Size48:
    struct: EnumItem
  Enum.FontSize.Size60:
    struct: EnumItem
  Enum.FontSize.Size8:
    struct: EnumItem
  Enum.FontSize.Size9:
    struct: EnumItem
  Enum.FontSize.Size96:
    struct: EnumItem
  Enum.FontStyle.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.FontStyle.Italic:
    struct: EnumItem
  Enum.FontStyle.Normal:
    struct: EnumItem
  Enum.FontWeight.Bold:
    struct: EnumItem
  Enum.FontWeight.ExtraBold:
    struct: EnumItem
  Enum.FontWeight.ExtraLight:
    struct: EnumItem
  Enum.FontWeight.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.FontWeight.Heavy:
    struct: EnumItem
  Enum.FontWeight.Light:
    struct: EnumItem
  Enum.FontWeight.Medium:
    struct: EnumItem
  Enum.FontWeight.Regular:
    struct: EnumItem
  Enum.FontWeight.SemiBold:
    struct: EnumItem
  Enum.FontWeight.Thin:
    struct: EnumItem
  Enum.ForceLimitMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ForceLimitMode.Magnitude:
    struct: EnumItem
  Enum.ForceLimitMode.PerAxis:
    struct: EnumItem
  Enum.FormFactor.Block:
    struct: EnumItem
    deprecated:
      message: Enum.FormFactor.Block was replaced with Enum.FormFactor.Brick
      replace:
      - Enum.FormFactor.Brick
  Enum.FormFactor.Brick:
    struct: EnumItem
  Enum.FormFactor.Custom:
    struct: EnumItem
  Enum.FormFactor.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.FormFactor.Plate:
    struct: EnumItem
  Enum.FormFactor.Symmetric:
    struct: EnumItem
  Enum.FrameStyle.ChatBlue:
    struct: EnumItem
  Enum.FrameStyle.ChatGreen:
    struct: EnumItem
  Enum.FrameStyle.ChatRed:
    struct: EnumItem
  Enum.FrameStyle.Custom:
    struct: EnumItem
  Enum.FrameStyle.DropShadow:
    struct: EnumItem
  Enum.FrameStyle.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.FrameStyle.RobloxRound:
    struct: EnumItem
  Enum.FrameStyle.RobloxSquare:
    struct: EnumItem
  Enum.FramerateManagerMode.Automatic:
    struct: EnumItem
  Enum.FramerateManagerMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.FramerateManagerMode.Off:
    struct: EnumItem
  Enum.FramerateManagerMode.On:
    struct: EnumItem
  Enum.FriendRequestEvent.Accept:
    struct: EnumItem
  Enum.FriendRequestEvent.Deny:
    struct: EnumItem
  Enum.FriendRequestEvent.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.FriendRequestEvent.Issue:
    struct: EnumItem
  Enum.FriendRequestEvent.Revoke:
    struct: EnumItem
  Enum.FriendStatus.Friend:
    struct: EnumItem
  Enum.FriendStatus.FriendRequestReceived:
    struct: EnumItem
  Enum.FriendStatus.FriendRequestSent:
    struct: EnumItem
  Enum.FriendStatus.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.FriendStatus.NotFriend:
    struct: EnumItem
  Enum.FriendStatus.Unknown:
    struct: EnumItem
  Enum.FunctionalTestResult.Error:
    struct: EnumItem
  Enum.FunctionalTestResult.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.FunctionalTestResult.Passed:
    struct: EnumItem
  Enum.FunctionalTestResult.Warning:
    struct: EnumItem
  Enum.GameAvatarType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.GameAvatarType.PlayerChoice:
    struct: EnumItem
  Enum.GameAvatarType.R15:
    struct: EnumItem
  Enum.GameAvatarType.R6:
    struct: EnumItem
  Enum.GamepadType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.GamepadType.PS4:
    struct: EnumItem
  Enum.GamepadType.PS5:
    struct: EnumItem
  Enum.GamepadType.Unknown:
    struct: EnumItem
  Enum.GamepadType.XboxOne:
    struct: EnumItem
  Enum.GearGenreSetting.AllGenres:
    struct: EnumItem
  Enum.GearGenreSetting.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.GearGenreSetting.MatchingGenreOnly:
    struct: EnumItem
  Enum.GearType.BuildingTools:
    struct: EnumItem
  Enum.GearType.Explosives:
    struct: EnumItem
  Enum.GearType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.GearType.MeleeWeapons:
    struct: EnumItem
  Enum.GearType.MusicalInstruments:
    struct: EnumItem
  Enum.GearType.NavigationEnhancers:
    struct: EnumItem
  Enum.GearType.PowerUps:
    struct: EnumItem
  Enum.GearType.RangedWeapons:
    struct: EnumItem
  Enum.GearType.SocialItems:
    struct: EnumItem
  Enum.GearType.Transport:
    struct: EnumItem
  Enum.Genre.Adventure:
    struct: EnumItem
  Enum.Genre.All:
    struct: EnumItem
  Enum.Genre.Fantasy:
    struct: EnumItem
  Enum.Genre.Funny:
    struct: EnumItem
  Enum.Genre.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.Genre.Ninja:
    struct: EnumItem
  Enum.Genre.Pirate:
    struct: EnumItem
  Enum.Genre.Scary:
    struct: EnumItem
  Enum.Genre.SciFi:
    struct: EnumItem
  Enum.Genre.SkatePark:
    struct: EnumItem
  Enum.Genre.Sports:
    struct: EnumItem
  Enum.Genre.TownAndCity:
    struct: EnumItem
  Enum.Genre.Tutorial:
    struct: EnumItem
  Enum.Genre.War:
    struct: EnumItem
  Enum.Genre.WildWest:
    struct: EnumItem
  Enum.GetEnums:
    args: []
    method: true
    must_use: true
  Enum.GraphicsMode.Automatic:
    struct: EnumItem
  Enum.GraphicsMode.Direct3D11:
    struct: EnumItem
  Enum.GraphicsMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.GraphicsMode.Metal:
    struct: EnumItem
  Enum.GraphicsMode.NoGraphics:
    struct: EnumItem
  Enum.GraphicsMode.OpenGL:
    struct: EnumItem
  Enum.GraphicsMode.Vulkan:
    struct: EnumItem
  Enum.GraphicsOptimizationMode.Balanced:
    struct: EnumItem
  Enum.GraphicsOptimizationMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.GraphicsOptimizationMode.Performance:
    struct: EnumItem
  Enum.GraphicsOptimizationMode.Quality:
    struct: EnumItem
  Enum.GuiState.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.GuiState.Hover:
    struct: EnumItem
  Enum.GuiState.Idle:
    struct: EnumItem
  Enum.GuiState.NonInteractable:
    struct: EnumItem
  Enum.GuiState.Press:
    struct: EnumItem
  Enum.GuiType.Core:
    struct: EnumItem
  Enum.GuiType.CoreBillboards:
    struct: EnumItem
  Enum.GuiType.Custom:
    struct: EnumItem
  Enum.GuiType.CustomBillboards:
    struct: EnumItem
  Enum.GuiType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.GuiType.PlayerNameplates:
    struct: EnumItem
  Enum.HandlesStyle.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.HandlesStyle.Movement:
    struct: EnumItem
  Enum.HandlesStyle.Resize:
    struct: EnumItem
  Enum.HapticEffectType.Custom:
    struct: EnumItem
  Enum.HapticEffectType.GameplayCollision:
    struct: EnumItem
  Enum.HapticEffectType.GameplayExplosion:
    struct: EnumItem
  Enum.HapticEffectType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.HapticEffectType.UIClick:
    struct: EnumItem
  Enum.HapticEffectType.UIHover:
    struct: EnumItem
  Enum.HapticEffectType.UINotification:
    struct: EnumItem
  Enum.HighlightDepthMode.AlwaysOnTop:
    struct: EnumItem
  Enum.HighlightDepthMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.HighlightDepthMode.Occluded:
    struct: EnumItem
  Enum.HorizontalAlignment.Center:
    struct: EnumItem
  Enum.HorizontalAlignment.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.HorizontalAlignment.Left:
    struct: EnumItem
  Enum.HorizontalAlignment.Right:
    struct: EnumItem
  Enum.HoverAnimateSpeed.Fast:
    struct: EnumItem
  Enum.HoverAnimateSpeed.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.HoverAnimateSpeed.Medium:
    struct: EnumItem
  Enum.HoverAnimateSpeed.Slow:
    struct: EnumItem
  Enum.HoverAnimateSpeed.VeryFast:
    struct: EnumItem
  Enum.HoverAnimateSpeed.VerySlow:
    struct: EnumItem
  Enum.HttpCachePolicy.DataOnly:
    struct: EnumItem
  Enum.HttpCachePolicy.Default:
    struct: EnumItem
  Enum.HttpCachePolicy.Full:
    struct: EnumItem
  Enum.HttpCachePolicy.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.HttpCachePolicy.InternalRedirectRefresh:
    struct: EnumItem
  Enum.HttpCachePolicy.None:
    struct: EnumItem
  Enum.HttpCompression.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.HttpCompression.Gzip:
    struct: EnumItem
  Enum.HttpCompression.None:
    struct: EnumItem
  Enum.HttpContentType.ApplicationJson:
    struct: EnumItem
  Enum.HttpContentType.ApplicationUrlEncoded:
    struct: EnumItem
  Enum.HttpContentType.ApplicationXml:
    struct: EnumItem
  Enum.HttpContentType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.HttpContentType.TextPlain:
    struct: EnumItem
  Enum.HttpContentType.TextXml:
    struct: EnumItem
  Enum.HttpError.Aborted:
    struct: EnumItem
  Enum.HttpError.ConnectFail:
    struct: EnumItem
  Enum.HttpError.DnsResolve:
    struct: EnumItem
  Enum.HttpError.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.HttpError.InvalidRedirect:
    struct: EnumItem
  Enum.HttpError.InvalidUrl:
    struct: EnumItem
  Enum.HttpError.NetFail:
    struct: EnumItem
  Enum.HttpError.OK:
    struct: EnumItem
  Enum.HttpError.OutOfMemory:
    struct: EnumItem
  Enum.HttpError.SslConnectFail:
    struct: EnumItem
  Enum.HttpError.SslVerificationFail:
    struct: EnumItem
  Enum.HttpError.TimedOut:
    struct: EnumItem
  Enum.HttpError.TooManyRedirects:
    struct: EnumItem
  Enum.HttpError.Unknown:
    struct: EnumItem
  Enum.HttpRequestType.Analytics:
    struct: EnumItem
  Enum.HttpRequestType.Avatar:
    struct: EnumItem
  Enum.HttpRequestType.Chat:
    struct: EnumItem
  Enum.HttpRequestType.Default:
    struct: EnumItem
  Enum.HttpRequestType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.HttpRequestType.Localization:
    struct: EnumItem
  Enum.HttpRequestType.MarketplaceService:
    struct: EnumItem
  Enum.HttpRequestType.Players:
    struct: EnumItem
  Enum.HumanoidCollisionType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.HumanoidCollisionType.InnerBox:
    struct: EnumItem
  Enum.HumanoidCollisionType.OuterBox:
    struct: EnumItem
  Enum.HumanoidDisplayDistanceType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.HumanoidDisplayDistanceType.None:
    struct: EnumItem
  Enum.HumanoidDisplayDistanceType.Subject:
    struct: EnumItem
  Enum.HumanoidDisplayDistanceType.Viewer:
    struct: EnumItem
  Enum.HumanoidHealthDisplayType.AlwaysOff:
    struct: EnumItem
  Enum.HumanoidHealthDisplayType.AlwaysOn:
    struct: EnumItem
  Enum.HumanoidHealthDisplayType.DisplayWhenDamaged:
    struct: EnumItem
  Enum.HumanoidHealthDisplayType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.HumanoidRigType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.HumanoidRigType.R15:
    struct: EnumItem
  Enum.HumanoidRigType.R6:
    struct: EnumItem
  Enum.HumanoidStateType.Climbing:
    struct: EnumItem
  Enum.HumanoidStateType.Dead:
    struct: EnumItem
  Enum.HumanoidStateType.FallingDown:
    struct: EnumItem
  Enum.HumanoidStateType.Flying:
    struct: EnumItem
  Enum.HumanoidStateType.Freefall:
    struct: EnumItem
  Enum.HumanoidStateType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.HumanoidStateType.GettingUp:
    struct: EnumItem
  Enum.HumanoidStateType.Jumping:
    struct: EnumItem
  Enum.HumanoidStateType.Landed:
    struct: EnumItem
  Enum.HumanoidStateType.None:
    struct: EnumItem
  Enum.HumanoidStateType.Physics:
    struct: EnumItem
  Enum.HumanoidStateType.PlatformStanding:
    struct: EnumItem
  Enum.HumanoidStateType.Ragdoll:
    struct: EnumItem
  Enum.HumanoidStateType.Running:
    struct: EnumItem
  Enum.HumanoidStateType.RunningNoPhysics:
    struct: EnumItem
  Enum.HumanoidStateType.Seated:
    struct: EnumItem
  Enum.HumanoidStateType.StrafingNoPhysics:
    struct: EnumItem
  Enum.HumanoidStateType.Swimming:
    struct: EnumItem
  Enum.IKCollisionsMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.IKCollisionsMode.IncludeContactedMechanisms:
    struct: EnumItem
  Enum.IKCollisionsMode.NoCollisions:
    struct: EnumItem
  Enum.IKCollisionsMode.OtherMechanismsAnchored:
    struct: EnumItem
  Enum.IKControlConstraintSupport.Default:
    struct: EnumItem
  Enum.IKControlConstraintSupport.Disabled:
    struct: EnumItem
  Enum.IKControlConstraintSupport.Enabled:
    struct: EnumItem
  Enum.IKControlConstraintSupport.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.IKControlType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.IKControlType.LookAt:
    struct: EnumItem
  Enum.IKControlType.Position:
    struct: EnumItem
  Enum.IKControlType.Rotation:
    struct: EnumItem
  Enum.IKControlType.Transform:
    struct: EnumItem
  Enum.IXPLoadingStatus.ErrorConnection:
    struct: EnumItem
  Enum.IXPLoadingStatus.ErrorInvalidUser:
    struct: EnumItem
  Enum.IXPLoadingStatus.ErrorJsonParse:
    struct: EnumItem
  Enum.IXPLoadingStatus.ErrorTimedOut:
    struct: EnumItem
  Enum.IXPLoadingStatus.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.IXPLoadingStatus.Initialized:
    struct: EnumItem
  Enum.IXPLoadingStatus.None:
    struct: EnumItem
  Enum.IXPLoadingStatus.Pending:
    struct: EnumItem
  Enum.ImageAlphaType.Default:
    struct: EnumItem
  Enum.ImageAlphaType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ImageAlphaType.LockCanvasAlpha:
    struct: EnumItem
  Enum.ImageAlphaType.LockCanvasColor:
    struct: EnumItem
  Enum.ImageCombineType.Add:
    struct: EnumItem
  Enum.ImageCombineType.AlphaBlend:
    struct: EnumItem
  Enum.ImageCombineType.BlendSourceOver:
    struct: EnumItem
  Enum.ImageCombineType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ImageCombineType.Multiply:
    struct: EnumItem
  Enum.ImageCombineType.Overwrite:
    struct: EnumItem
  Enum.InOut.Center:
    struct: EnumItem
  Enum.InOut.Edge:
    struct: EnumItem
  Enum.InOut.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.InOut.Inset:
    struct: EnumItem
  Enum.InfoType.Asset:
    struct: EnumItem
  Enum.InfoType.Bundle:
    struct: EnumItem
  Enum.InfoType.GamePass:
    struct: EnumItem
  Enum.InfoType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.InfoType.Product:
    struct: EnumItem
  Enum.InfoType.Subscription:
    struct: EnumItem
  Enum.InitialDockState.Bottom:
    struct: EnumItem
  Enum.InitialDockState.Float:
    struct: EnumItem
  Enum.InitialDockState.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.InitialDockState.Left:
    struct: EnumItem
  Enum.InitialDockState.Right:
    struct: EnumItem
  Enum.InitialDockState.Top:
    struct: EnumItem
  Enum.InputActionType.Bool:
    struct: EnumItem
  Enum.InputActionType.Direction2D:
    struct: EnumItem
  Enum.InputActionType.Float:
    struct: EnumItem
  Enum.InputActionType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.InputType.Action1:
    struct: EnumItem
    deprecated:
      message: Enum.InputType.Action1 was replaced with Enum.InputType.NoInput
      replace:
      - Enum.InputType.NoInput
  Enum.InputType.Action2:
    struct: EnumItem
    deprecated:
      message: Enum.InputType.Action2 was replaced with Enum.InputType.NoInput
      replace:
      - Enum.InputType.NoInput
  Enum.InputType.Action3:
    struct: EnumItem
    deprecated:
      message: Enum.InputType.Action3 was replaced with Enum.InputType.NoInput
      replace:
      - Enum.InputType.NoInput
  Enum.InputType.Action4:
    struct: EnumItem
    deprecated:
      message: Enum.InputType.Action4 was replaced with Enum.InputType.NoInput
      replace:
      - Enum.InputType.NoInput
  Enum.InputType.Action5:
    struct: EnumItem
    deprecated:
      message: Enum.InputType.Action5 was replaced with Enum.InputType.NoInput
      replace:
      - Enum.InputType.NoInput
  Enum.InputType.Constant:
    struct: EnumItem
  Enum.InputType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.InputType.LeftTread:
    struct: EnumItem
    deprecated:
      message: Enum.InputType.LeftTread was replaced with Enum.InputType.NoInput
      replace:
      - Enum.InputType.NoInput
  Enum.InputType.NoInput:
    struct: EnumItem
  Enum.InputType.RightTread:
    struct: EnumItem
    deprecated:
      message: Enum.InputType.RightTread was replaced with Enum.InputType.NoInput
      replace:
      - Enum.InputType.NoInput
  Enum.InputType.Sin:
    struct: EnumItem
  Enum.InputType.Steer:
    struct: EnumItem
    deprecated:
      message: Enum.InputType.Steer was replaced with Enum.InputType.NoInput
      replace:
      - Enum.InputType.NoInput
  Enum.InputType.Throtle:
    struct: EnumItem
    deprecated:
      message: Enum.InputType.Throtle was replaced with Enum.InputType.NoInput
      replace:
      - Enum.InputType.NoInput
  Enum.InputType.Throttle:
    struct: EnumItem
    deprecated:
      message: Enum.InputType.Throttle was replaced with Enum.InputType.NoInput
      replace:
      - Enum.InputType.NoInput
  Enum.InputType.UpDown:
    struct: EnumItem
    deprecated:
      message: Enum.InputType.UpDown was replaced with Enum.InputType.NoInput
      replace:
      - Enum.InputType.NoInput
  Enum.IntermediateMeshGenerationResult.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.IntermediateMeshGenerationResult.HighQualityMesh:
    struct: EnumItem
  Enum.InterpolationThrottlingMode.Default:
    struct: EnumItem
  Enum.InterpolationThrottlingMode.Disabled:
    struct: EnumItem
  Enum.InterpolationThrottlingMode.Enabled:
    struct: EnumItem
  Enum.InterpolationThrottlingMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.InviteState.Accepted:
    struct: EnumItem
  Enum.InviteState.Declined:
    struct: EnumItem
  Enum.InviteState.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.InviteState.Missed:
    struct: EnumItem
  Enum.InviteState.Placed:
    struct: EnumItem
  Enum.ItemLineAlignment.Automatic:
    struct: EnumItem
  Enum.ItemLineAlignment.Center:
    struct: EnumItem
  Enum.ItemLineAlignment.End:
    struct: EnumItem
  Enum.ItemLineAlignment.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ItemLineAlignment.Start:
    struct: EnumItem
  Enum.ItemLineAlignment.Stretch:
    struct: EnumItem
  Enum.JoinSource.CreatedItemAttribution:
    struct: EnumItem
  Enum.JoinSource.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.JointCreationMode.All:
    struct: EnumItem
  Enum.JointCreationMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.JointCreationMode.None:
    struct: EnumItem
  Enum.JointCreationMode.Surface:
    struct: EnumItem
  Enum.KeyCode.A:
    struct: EnumItem
  Enum.KeyCode.Ampersand:
    struct: EnumItem
  Enum.KeyCode.Asterisk:
    struct: EnumItem
  Enum.KeyCode.At:
    struct: EnumItem
  Enum.KeyCode.B:
    struct: EnumItem
  Enum.KeyCode.BackSlash:
    struct: EnumItem
  Enum.KeyCode.Backquote:
    struct: EnumItem
  Enum.KeyCode.Backspace:
    struct: EnumItem
  Enum.KeyCode.Break:
    struct: EnumItem
  Enum.KeyCode.ButtonA:
    struct: EnumItem
  Enum.KeyCode.ButtonB:
    struct: EnumItem
  Enum.KeyCode.ButtonL1:
    struct: EnumItem
  Enum.KeyCode.ButtonL2:
    struct: EnumItem
  Enum.KeyCode.ButtonL3:
    struct: EnumItem
  Enum.KeyCode.ButtonR1:
    struct: EnumItem
  Enum.KeyCode.ButtonR2:
    struct: EnumItem
  Enum.KeyCode.ButtonR3:
    struct: EnumItem
  Enum.KeyCode.ButtonSelect:
    struct: EnumItem
  Enum.KeyCode.ButtonStart:
    struct: EnumItem
  Enum.KeyCode.ButtonX:
    struct: EnumItem
  Enum.KeyCode.ButtonY:
    struct: EnumItem
  Enum.KeyCode.C:
    struct: EnumItem
  Enum.KeyCode.CapsLock:
    struct: EnumItem
  Enum.KeyCode.Caret:
    struct: EnumItem
  Enum.KeyCode.Clear:
    struct: EnumItem
  Enum.KeyCode.Colon:
    struct: EnumItem
  Enum.KeyCode.Comma:
    struct: EnumItem
  Enum.KeyCode.Compose:
    struct: EnumItem
  Enum.KeyCode.D:
    struct: EnumItem
  Enum.KeyCode.DPadDown:
    struct: EnumItem
  Enum.KeyCode.DPadLeft:
    struct: EnumItem
  Enum.KeyCode.DPadRight:
    struct: EnumItem
  Enum.KeyCode.DPadUp:
    struct: EnumItem
  Enum.KeyCode.Delete:
    struct: EnumItem
  Enum.KeyCode.Dollar:
    struct: EnumItem
  Enum.KeyCode.Down:
    struct: EnumItem
  Enum.KeyCode.E:
    struct: EnumItem
  Enum.KeyCode.Eight:
    struct: EnumItem
  Enum.KeyCode.End:
    struct: EnumItem
  Enum.KeyCode.Equals:
    struct: EnumItem
  Enum.KeyCode.Escape:
    struct: EnumItem
  Enum.KeyCode.Euro:
    struct: EnumItem
  Enum.KeyCode.F:
    struct: EnumItem
  Enum.KeyCode.F1:
    struct: EnumItem
  Enum.KeyCode.F10:
    struct: EnumItem
  Enum.KeyCode.F11:
    struct: EnumItem
  Enum.KeyCode.F12:
    struct: EnumItem
  Enum.KeyCode.F13:
    struct: EnumItem
  Enum.KeyCode.F14:
    struct: EnumItem
  Enum.KeyCode.F15:
    struct: EnumItem
  Enum.KeyCode.F2:
    struct: EnumItem
  Enum.KeyCode.F3:
    struct: EnumItem
  Enum.KeyCode.F4:
    struct: EnumItem
  Enum.KeyCode.F5:
    struct: EnumItem
  Enum.KeyCode.F6:
    struct: EnumItem
  Enum.KeyCode.F7:
    struct: EnumItem
  Enum.KeyCode.F8:
    struct: EnumItem
  Enum.KeyCode.F9:
    struct: EnumItem
  Enum.KeyCode.Five:
    struct: EnumItem
  Enum.KeyCode.Four:
    struct: EnumItem
  Enum.KeyCode.G:
    struct: EnumItem
  Enum.KeyCode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.KeyCode.GreaterThan:
    struct: EnumItem
  Enum.KeyCode.H:
    struct: EnumItem
  Enum.KeyCode.Hash:
    struct: EnumItem
  Enum.KeyCode.Help:
    struct: EnumItem
  Enum.KeyCode.Home:
    struct: EnumItem
  Enum.KeyCode.I:
    struct: EnumItem
  Enum.KeyCode.Insert:
    struct: EnumItem
  Enum.KeyCode.J:
    struct: EnumItem
  Enum.KeyCode.K:
    struct: EnumItem
  Enum.KeyCode.KeypadDivide:
    struct: EnumItem
  Enum.KeyCode.KeypadEight:
    struct: EnumItem
  Enum.KeyCode.KeypadEnter:
    struct: EnumItem
  Enum.KeyCode.KeypadEquals:
    struct: EnumItem
  Enum.KeyCode.KeypadFive:
    struct: EnumItem
  Enum.KeyCode.KeypadFour:
    struct: EnumItem
  Enum.KeyCode.KeypadMinus:
    struct: EnumItem
  Enum.KeyCode.KeypadMultiply:
    struct: EnumItem
  Enum.KeyCode.KeypadNine:
    struct: EnumItem
  Enum.KeyCode.KeypadOne:
    struct: EnumItem
  Enum.KeyCode.KeypadPeriod:
    struct: EnumItem
  Enum.KeyCode.KeypadPlus:
    struct: EnumItem
  Enum.KeyCode.KeypadSeven:
    struct: EnumItem
  Enum.KeyCode.KeypadSix:
    struct: EnumItem
  Enum.KeyCode.KeypadThree:
    struct: EnumItem
  Enum.KeyCode.KeypadTwo:
    struct: EnumItem
  Enum.KeyCode.KeypadZero:
    struct: EnumItem
  Enum.KeyCode.L:
    struct: EnumItem
  Enum.KeyCode.Left:
    struct: EnumItem
  Enum.KeyCode.LeftAlt:
    struct: EnumItem
  Enum.KeyCode.LeftBracket:
    struct: EnumItem
  Enum.KeyCode.LeftControl:
    struct: EnumItem
  Enum.KeyCode.LeftCurly:
    struct: EnumItem
  Enum.KeyCode.LeftMeta:
    struct: EnumItem
  Enum.KeyCode.LeftParenthesis:
    struct: EnumItem
  Enum.KeyCode.LeftShift:
    struct: EnumItem
  Enum.KeyCode.LeftSuper:
    struct: EnumItem
  Enum.KeyCode.LessThan:
    struct: EnumItem
  Enum.KeyCode.M:
    struct: EnumItem
  Enum.KeyCode.Menu:
    struct: EnumItem
  Enum.KeyCode.Minus:
    struct: EnumItem
  Enum.KeyCode.Mode:
    struct: EnumItem
  Enum.KeyCode.MouseBackButton:
    struct: EnumItem
  Enum.KeyCode.MouseLeftButton:
    struct: EnumItem
  Enum.KeyCode.MouseMiddleButton:
    struct: EnumItem
  Enum.KeyCode.MouseNoButton:
    struct: EnumItem
  Enum.KeyCode.MouseRightButton:
    struct: EnumItem
  Enum.KeyCode.MouseX:
    struct: EnumItem
  Enum.KeyCode.MouseY:
    struct: EnumItem
  Enum.KeyCode.N:
    struct: EnumItem
  Enum.KeyCode.Nine:
    struct: EnumItem
  Enum.KeyCode.NumLock:
    struct: EnumItem
  Enum.KeyCode.O:
    struct: EnumItem
  Enum.KeyCode.One:
    struct: EnumItem
  Enum.KeyCode.P:
    struct: EnumItem
  Enum.KeyCode.PageDown:
    struct: EnumItem
  Enum.KeyCode.PageUp:
    struct: EnumItem
  Enum.KeyCode.Pause:
    struct: EnumItem
  Enum.KeyCode.Percent:
    struct: EnumItem
  Enum.KeyCode.Period:
    struct: EnumItem
  Enum.KeyCode.Pipe:
    struct: EnumItem
  Enum.KeyCode.Plus:
    struct: EnumItem
  Enum.KeyCode.Power:
    struct: EnumItem
  Enum.KeyCode.Print:
    struct: EnumItem
  Enum.KeyCode.Q:
    struct: EnumItem
  Enum.KeyCode.Question:
    struct: EnumItem
  Enum.KeyCode.Quote:
    struct: EnumItem
  Enum.KeyCode.QuotedDouble:
    struct: EnumItem
  Enum.KeyCode.R:
    struct: EnumItem
  Enum.KeyCode.Return:
    struct: EnumItem
  Enum.KeyCode.Right:
    struct: EnumItem
  Enum.KeyCode.RightAlt:
    struct: EnumItem
  Enum.KeyCode.RightBracket:
    struct: EnumItem
  Enum.KeyCode.RightControl:
    struct: EnumItem
  Enum.KeyCode.RightCurly:
    struct: EnumItem
  Enum.KeyCode.RightMeta:
    struct: EnumItem
  Enum.KeyCode.RightParenthesis:
    struct: EnumItem
  Enum.KeyCode.RightShift:
    struct: EnumItem
  Enum.KeyCode.RightSuper:
    struct: EnumItem
  Enum.KeyCode.S:
    struct: EnumItem
  Enum.KeyCode.ScrollLock:
    struct: EnumItem
  Enum.KeyCode.Semicolon:
    struct: EnumItem
  Enum.KeyCode.Seven:
    struct: EnumItem
  Enum.KeyCode.Six:
    struct: EnumItem
  Enum.KeyCode.Slash:
    struct: EnumItem
  Enum.KeyCode.Space:
    struct: EnumItem
  Enum.KeyCode.SysReq:
    struct: EnumItem
  Enum.KeyCode.T:
    struct: EnumItem
  Enum.KeyCode.Tab:
    struct: EnumItem
  Enum.KeyCode.Three:
    struct: EnumItem
  Enum.KeyCode.Thumbstick1:
    struct: EnumItem
  Enum.KeyCode.Thumbstick2:
    struct: EnumItem
  Enum.KeyCode.Tilde:
    struct: EnumItem
  Enum.KeyCode.Two:
    struct: EnumItem
  Enum.KeyCode.U:
    struct: EnumItem
  Enum.KeyCode.Underscore:
    struct: EnumItem
  Enum.KeyCode.Undo:
    struct: EnumItem
  Enum.KeyCode.Unknown:
    struct: EnumItem
  Enum.KeyCode.Up:
    struct: EnumItem
  Enum.KeyCode.V:
    struct: EnumItem
  Enum.KeyCode.W:
    struct: EnumItem
  Enum.KeyCode.World0:
    struct: EnumItem
  Enum.KeyCode.World1:
    struct: EnumItem
  Enum.KeyCode.World10:
    struct: EnumItem
  Enum.KeyCode.World11:
    struct: EnumItem
  Enum.KeyCode.World12:
    struct: EnumItem
  Enum.KeyCode.World13:
    struct: EnumItem
  Enum.KeyCode.World14:
    struct: EnumItem
  Enum.KeyCode.World15:
    struct: EnumItem
  Enum.KeyCode.World16:
    struct: EnumItem
  Enum.KeyCode.World17:
    struct: EnumItem
  Enum.KeyCode.World18:
    struct: EnumItem
  Enum.KeyCode.World19:
    struct: EnumItem
  Enum.KeyCode.World2:
    struct: EnumItem
  Enum.KeyCode.World20:
    struct: EnumItem
  Enum.KeyCode.World21:
    struct: EnumItem
  Enum.KeyCode.World22:
    struct: EnumItem
  Enum.KeyCode.World23:
    struct: EnumItem
  Enum.KeyCode.World24:
    struct: EnumItem
  Enum.KeyCode.World25:
    struct: EnumItem
  Enum.KeyCode.World26:
    struct: EnumItem
  Enum.KeyCode.World27:
    struct: EnumItem
  Enum.KeyCode.World28:
    struct: EnumItem
  Enum.KeyCode.World29:
    struct: EnumItem
  Enum.KeyCode.World3:
    struct: EnumItem
  Enum.KeyCode.World30:
    struct: EnumItem
  Enum.KeyCode.World31:
    struct: EnumItem
  Enum.KeyCode.World32:
    struct: EnumItem
  Enum.KeyCode.World33:
    struct: EnumItem
  Enum.KeyCode.World34:
    struct: EnumItem
  Enum.KeyCode.World35:
    struct: EnumItem
  Enum.KeyCode.World36:
    struct: EnumItem
  Enum.KeyCode.World37:
    struct: EnumItem
  Enum.KeyCode.World38:
    struct: EnumItem
  Enum.KeyCode.World39:
    struct: EnumItem
  Enum.KeyCode.World4:
    struct: EnumItem
  Enum.KeyCode.World40:
    struct: EnumItem
  Enum.KeyCode.World41:
    struct: EnumItem
  Enum.KeyCode.World42:
    struct: EnumItem
  Enum.KeyCode.World43:
    struct: EnumItem
  Enum.KeyCode.World44:
    struct: EnumItem
  Enum.KeyCode.World45:
    struct: EnumItem
  Enum.KeyCode.World46:
    struct: EnumItem
  Enum.KeyCode.World47:
    struct: EnumItem
  Enum.KeyCode.World48:
    struct: EnumItem
  Enum.KeyCode.World49:
    struct: EnumItem
  Enum.KeyCode.World5:
    struct: EnumItem
  Enum.KeyCode.World50:
    struct: EnumItem
  Enum.KeyCode.World51:
    struct: EnumItem
  Enum.KeyCode.World52:
    struct: EnumItem
  Enum.KeyCode.World53:
    struct: EnumItem
  Enum.KeyCode.World54:
    struct: EnumItem
  Enum.KeyCode.World55:
    struct: EnumItem
  Enum.KeyCode.World56:
    struct: EnumItem
  Enum.KeyCode.World57:
    struct: EnumItem
  Enum.KeyCode.World58:
    struct: EnumItem
  Enum.KeyCode.World59:
    struct: EnumItem
  Enum.KeyCode.World6:
    struct: EnumItem
  Enum.KeyCode.World60:
    struct: EnumItem
  Enum.KeyCode.World61:
    struct: EnumItem
  Enum.KeyCode.World62:
    struct: EnumItem
  Enum.KeyCode.World63:
    struct: EnumItem
  Enum.KeyCode.World64:
    struct: EnumItem
  Enum.KeyCode.World65:
    struct: EnumItem
  Enum.KeyCode.World66:
    struct: EnumItem
  Enum.KeyCode.World67:
    struct: EnumItem
  Enum.KeyCode.World68:
    struct: EnumItem
  Enum.KeyCode.World69:
    struct: EnumItem
  Enum.KeyCode.World7:
    struct: EnumItem
  Enum.KeyCode.World70:
    struct: EnumItem
  Enum.KeyCode.World71:
    struct: EnumItem
  Enum.KeyCode.World72:
    struct: EnumItem
  Enum.KeyCode.World73:
    struct: EnumItem
  Enum.KeyCode.World74:
    struct: EnumItem
  Enum.KeyCode.World75:
    struct: EnumItem
  Enum.KeyCode.World76:
    struct: EnumItem
  Enum.KeyCode.World77:
    struct: EnumItem
  Enum.KeyCode.World78:
    struct: EnumItem
  Enum.KeyCode.World79:
    struct: EnumItem
  Enum.KeyCode.World8:
    struct: EnumItem
  Enum.KeyCode.World80:
    struct: EnumItem
  Enum.KeyCode.World81:
    struct: EnumItem
  Enum.KeyCode.World82:
    struct: EnumItem
  Enum.KeyCode.World83:
    struct: EnumItem
  Enum.KeyCode.World84:
    struct: EnumItem
  Enum.KeyCode.World85:
    struct: EnumItem
  Enum.KeyCode.World86:
    struct: EnumItem
  Enum.KeyCode.World87:
    struct: EnumItem
  Enum.KeyCode.World88:
    struct: EnumItem
  Enum.KeyCode.World89:
    struct: EnumItem
  Enum.KeyCode.World9:
    struct: EnumItem
  Enum.KeyCode.World90:
    struct: EnumItem
  Enum.KeyCode.World91:
    struct: EnumItem
  Enum.KeyCode.World92:
    struct: EnumItem
  Enum.KeyCode.World93:
    struct: EnumItem
  Enum.KeyCode.World94:
    struct: EnumItem
  Enum.KeyCode.World95:
    struct: EnumItem
  Enum.KeyCode.X:
    struct: EnumItem
  Enum.KeyCode.Y:
    struct: EnumItem
  Enum.KeyCode.Z:
    struct: EnumItem
  Enum.KeyCode.Zero:
    struct: EnumItem
  Enum.KeyInterpolationMode.Constant:
    struct: EnumItem
  Enum.KeyInterpolationMode.Cubic:
    struct: EnumItem
  Enum.KeyInterpolationMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.KeyInterpolationMode.Linear:
    struct: EnumItem
  Enum.KeywordFilterType.Exclude:
    struct: EnumItem
  Enum.KeywordFilterType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.KeywordFilterType.Include:
    struct: EnumItem
  Enum.Language.Default:
    struct: EnumItem
  Enum.Language.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.LeftRight.Center:
    struct: EnumItem
  Enum.LeftRight.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.LeftRight.Left:
    struct: EnumItem
  Enum.LeftRight.Right:
    struct: EnumItem
  Enum.LexemeType.And:
    struct: EnumItem
  Enum.LexemeType.Colon:
    struct: EnumItem
  Enum.LexemeType.Dot:
    struct: EnumItem
  Enum.LexemeType.DoubleStar:
    struct: EnumItem
  Enum.LexemeType.Eof:
    struct: EnumItem
  Enum.LexemeType.Equal:
    struct: EnumItem
  Enum.LexemeType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.LexemeType.GreaterThan:
    struct: EnumItem
  Enum.LexemeType.GreaterThanEqual:
    struct: EnumItem
  Enum.LexemeType.LeftParenthesis:
    struct: EnumItem
  Enum.LexemeType.LessThan:
    struct: EnumItem
  Enum.LexemeType.LessThanEqual:
    struct: EnumItem
  Enum.LexemeType.Name:
    struct: EnumItem
  Enum.LexemeType.Number:
    struct: EnumItem
  Enum.LexemeType.Or:
    struct: EnumItem
  Enum.LexemeType.QuotedString:
    struct: EnumItem
  Enum.LexemeType.ReservedSpecial:
    struct: EnumItem
  Enum.LexemeType.RightParenthesis:
    struct: EnumItem
  Enum.LexemeType.Star:
    struct: EnumItem
  Enum.LexemeType.TildeEqual:
    struct: EnumItem
  Enum.LightingStyle.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.LightingStyle.Realistic:
    struct: EnumItem
  Enum.LightingStyle.Soft:
    struct: EnumItem
  Enum.Limb.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.Limb.Head:
    struct: EnumItem
  Enum.Limb.LeftArm:
    struct: EnumItem
  Enum.Limb.LeftLeg:
    struct: EnumItem
  Enum.Limb.RightArm:
    struct: EnumItem
  Enum.Limb.RightLeg:
    struct: EnumItem
  Enum.Limb.Torso:
    struct: EnumItem
  Enum.Limb.Unknown:
    struct: EnumItem
  Enum.LineJoinMode.Bevel:
    struct: EnumItem
  Enum.LineJoinMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.LineJoinMode.Miter:
    struct: EnumItem
  Enum.LineJoinMode.Round:
    struct: EnumItem
  Enum.ListDisplayMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ListDisplayMode.Horizontal:
    struct: EnumItem
  Enum.ListDisplayMode.Vertical:
    struct: EnumItem
  Enum.ListenerLocation.Camera:
    struct: EnumItem
  Enum.ListenerLocation.Character:
    struct: EnumItem
  Enum.ListenerLocation.Default:
    struct: EnumItem
  Enum.ListenerLocation.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ListenerLocation.None:
    struct: EnumItem
  Enum.ListenerType.CFrame:
    struct: EnumItem
  Enum.ListenerType.Camera:
    struct: EnumItem
  Enum.ListenerType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ListenerType.ObjectCFrame:
    struct: EnumItem
  Enum.ListenerType.ObjectPosition:
    struct: EnumItem
  Enum.LiveEditingAtomicUpdateResponse.FailureGuidNotFound:
    struct: EnumItem
  Enum.LiveEditingAtomicUpdateResponse.FailureHashMismatch:
    struct: EnumItem
  Enum.LiveEditingAtomicUpdateResponse.FailureOperationIllegal:
    struct: EnumItem
  Enum.LiveEditingAtomicUpdateResponse.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.LiveEditingAtomicUpdateResponse.Success:
    struct: EnumItem
  Enum.LiveEditingBroadcastMessageType.Error:
    struct: EnumItem
  Enum.LiveEditingBroadcastMessageType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.LiveEditingBroadcastMessageType.Normal:
    struct: EnumItem
  Enum.LiveEditingBroadcastMessageType.Warning:
    struct: EnumItem
  Enum.LoadCharacterLayeredClothing.Default:
    struct: EnumItem
  Enum.LoadCharacterLayeredClothing.Disabled:
    struct: EnumItem
  Enum.LoadCharacterLayeredClothing.Enabled:
    struct: EnumItem
  Enum.LoadCharacterLayeredClothing.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.LoadDynamicHeads.Default:
    struct: EnumItem
  Enum.LoadDynamicHeads.Disabled:
    struct: EnumItem
  Enum.LoadDynamicHeads.Enabled:
    struct: EnumItem
  Enum.LoadDynamicHeads.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.LocationType.Camera:
    struct: EnumItem
  Enum.LocationType.Character:
    struct: EnumItem
  Enum.LocationType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.LocationType.ObjectPosition:
    struct: EnumItem
  Enum.MarketplaceBulkPurchasePromptStatus.Aborted:
    struct: EnumItem
  Enum.MarketplaceBulkPurchasePromptStatus.Completed:
    struct: EnumItem
  Enum.MarketplaceBulkPurchasePromptStatus.Error:
    struct: EnumItem
  Enum.MarketplaceBulkPurchasePromptStatus.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.MarketplaceItemPurchaseStatus.AlreadyOwned:
    struct: EnumItem
  Enum.MarketplaceItemPurchaseStatus.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.MarketplaceItemPurchaseStatus.InsufficientMembership:
    struct: EnumItem
  Enum.MarketplaceItemPurchaseStatus.InsufficientRobux:
    struct: EnumItem
  Enum.MarketplaceItemPurchaseStatus.NotAvailableForPurchaser:
    struct: EnumItem
  Enum.MarketplaceItemPurchaseStatus.NotForSale:
    struct: EnumItem
  Enum.MarketplaceItemPurchaseStatus.PlaceInvalid:
    struct: EnumItem
  Enum.MarketplaceItemPurchaseStatus.PriceMismatch:
    struct: EnumItem
  Enum.MarketplaceItemPurchaseStatus.PurchaserIsSeller:
    struct: EnumItem
  Enum.MarketplaceItemPurchaseStatus.QuantityLimitExceeded:
    struct: EnumItem
  Enum.MarketplaceItemPurchaseStatus.QuotaExceeded:
    struct: EnumItem
  Enum.MarketplaceItemPurchaseStatus.SoldOut:
    struct: EnumItem
  Enum.MarketplaceItemPurchaseStatus.Success:
    struct: EnumItem
  Enum.MarketplaceItemPurchaseStatus.SystemError:
    struct: EnumItem
  Enum.MarketplaceProductType.AvatarAsset:
    struct: EnumItem
  Enum.MarketplaceProductType.AvatarBundle:
    struct: EnumItem
  Enum.MarketplaceProductType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.MarkupKind.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.MarkupKind.Markdown:
    struct: EnumItem
  Enum.MarkupKind.PlainText:
    struct: EnumItem
  Enum.MatchmakingType.Default:
    struct: EnumItem
  Enum.MatchmakingType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.MatchmakingType.PlayStationOnly:
    struct: EnumItem
  Enum.MatchmakingType.XboxOnly:
    struct: EnumItem
  Enum.Material.Air:
    struct: EnumItem
  Enum.Material.Aluminum:
    struct: EnumItem
    deprecated:
      message: Enum.Material.Aluminum was replaced with Enum.Material.Foil
      replace:
      - Enum.Material.Foil
  Enum.Material.Asphalt:
    struct: EnumItem
  Enum.Material.Basalt:
    struct: EnumItem
  Enum.Material.Brick:
    struct: EnumItem
  Enum.Material.Cardboard:
    struct: EnumItem
  Enum.Material.Carpet:
    struct: EnumItem
  Enum.Material.CeramicTiles:
    struct: EnumItem
  Enum.Material.ClayRoofTiles:
    struct: EnumItem
  Enum.Material.Cobblestone:
    struct: EnumItem
  Enum.Material.Concrete:
    struct: EnumItem
  Enum.Material.Corroded Metal:
    struct: EnumItem
    deprecated:
      message: Enum.Material.Corroded Metal was replaced with Enum.Material.CorrodedMetal
      replace:
      - Enum.Material.CorrodedMetal
  Enum.Material.CorrodedMetal:
    struct: EnumItem
  Enum.Material.CrackedLava:
    struct: EnumItem
  Enum.Material.DiamondPlate:
    struct: EnumItem
  Enum.Material.Fabric:
    struct: EnumItem
  Enum.Material.Foil:
    struct: EnumItem
  Enum.Material.ForceField:
    struct: EnumItem
  Enum.Material.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.Material.Glacier:
    struct: EnumItem
  Enum.Material.Glass:
    struct: EnumItem
  Enum.Material.Granite:
    struct: EnumItem
  Enum.Material.Grass:
    struct: EnumItem
  Enum.Material.Ground:
    struct: EnumItem
  Enum.Material.Ice:
    struct: EnumItem
  Enum.Material.LeafyGrass:
    struct: EnumItem
  Enum.Material.Leather:
    struct: EnumItem
  Enum.Material.Limestone:
    struct: EnumItem
  Enum.Material.Marble:
    struct: EnumItem
  Enum.Material.Metal:
    struct: EnumItem
  Enum.Material.Mud:
    struct: EnumItem
  Enum.Material.Neon:
    struct: EnumItem
  Enum.Material.Pavement:
    struct: EnumItem
  Enum.Material.Pebble:
    struct: EnumItem
  Enum.Material.Plaster:
    struct: EnumItem
  Enum.Material.Plastic:
    struct: EnumItem
  Enum.Material.Rock:
    struct: EnumItem
  Enum.Material.RoofShingles:
    struct: EnumItem
  Enum.Material.Rubber:
    struct: EnumItem
  Enum.Material.Salt:
    struct: EnumItem
  Enum.Material.Sand:
    struct: EnumItem
  Enum.Material.Sandstone:
    struct: EnumItem
  Enum.Material.Slate:
    struct: EnumItem
  Enum.Material.SmoothPlastic:
    struct: EnumItem
  Enum.Material.Snow:
    struct: EnumItem
  Enum.Material.Water:
    struct: EnumItem
  Enum.Material.Wood:
    struct: EnumItem
  Enum.Material.WoodPlanks:
    struct: EnumItem
  Enum.MaterialPattern.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.MaterialPattern.Organic:
    struct: EnumItem
  Enum.MaterialPattern.Regular:
    struct: EnumItem
  Enum.MembershipType.BuildersClub:
    struct: EnumItem
  Enum.MembershipType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.MembershipType.None:
    struct: EnumItem
  Enum.MembershipType.OutrageousBuildersClub:
    struct: EnumItem
  Enum.MembershipType.Premium:
    struct: EnumItem
  Enum.MembershipType.TurboBuildersClub:
    struct: EnumItem
  Enum.MeshPartDetailLevel.DistanceBased:
    struct: EnumItem
  Enum.MeshPartDetailLevel.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.MeshPartDetailLevel.Level00:
    struct: EnumItem
  Enum.MeshPartDetailLevel.Level01:
    struct: EnumItem
  Enum.MeshPartDetailLevel.Level02:
    struct: EnumItem
  Enum.MeshPartDetailLevel.Level03:
    struct: EnumItem
  Enum.MeshPartDetailLevel.Level04:
    struct: EnumItem
  Enum.MeshPartHeadsAndAccessories.Default:
    struct: EnumItem
  Enum.MeshPartHeadsAndAccessories.Disabled:
    struct: EnumItem
  Enum.MeshPartHeadsAndAccessories.Enabled:
    struct: EnumItem
  Enum.MeshPartHeadsAndAccessories.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.MeshScaleUnit.CM:
    struct: EnumItem
  Enum.MeshScaleUnit.Foot:
    struct: EnumItem
  Enum.MeshScaleUnit.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.MeshScaleUnit.Inch:
    struct: EnumItem
  Enum.MeshScaleUnit.MM:
    struct: EnumItem
  Enum.MeshScaleUnit.Meter:
    struct: EnumItem
  Enum.MeshScaleUnit.Stud:
    struct: EnumItem
  Enum.MeshType.Brick:
    struct: EnumItem
  Enum.MeshType.CornerWedge:
    struct: EnumItem
  Enum.MeshType.Cylinder:
    struct: EnumItem
  Enum.MeshType.FileMesh:
    struct: EnumItem
  Enum.MeshType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.MeshType.Head:
    struct: EnumItem
  Enum.MeshType.ParallelRamp:
    struct: EnumItem
  Enum.MeshType.Prism:
    struct: EnumItem
  Enum.MeshType.Pyramid:
    struct: EnumItem
  Enum.MeshType.RightAngleRamp:
    struct: EnumItem
  Enum.MeshType.Sphere:
    struct: EnumItem
  Enum.MeshType.Torso:
    struct: EnumItem
  Enum.MeshType.Wedge:
    struct: EnumItem
  Enum.MessageType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.MessageType.MessageError:
    struct: EnumItem
  Enum.MessageType.MessageInfo:
    struct: EnumItem
  Enum.MessageType.MessageOutput:
    struct: EnumItem
  Enum.MessageType.MessageWarning:
    struct: EnumItem
  Enum.ModelLevelOfDetail.Automatic:
    struct: EnumItem
  Enum.ModelLevelOfDetail.Disabled:
    struct: EnumItem
  Enum.ModelLevelOfDetail.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ModelLevelOfDetail.StreamingMesh:
    struct: EnumItem
  Enum.ModelStreamingBehavior.Default:
    struct: EnumItem
  Enum.ModelStreamingBehavior.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ModelStreamingBehavior.Improved:
    struct: EnumItem
  Enum.ModelStreamingBehavior.Legacy:
    struct: EnumItem
  Enum.ModelStreamingMode.Atomic:
    struct: EnumItem
  Enum.ModelStreamingMode.Default:
    struct: EnumItem
  Enum.ModelStreamingMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ModelStreamingMode.Nonatomic:
    struct: EnumItem
  Enum.ModelStreamingMode.Persistent:
    struct: EnumItem
  Enum.ModelStreamingMode.PersistentPerPlayer:
    struct: EnumItem
  Enum.ModerationStatus.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ModerationStatus.Invalid:
    struct: EnumItem
  Enum.ModerationStatus.NotApplicable:
    struct: EnumItem
  Enum.ModerationStatus.NotReviewed:
    struct: EnumItem
  Enum.ModerationStatus.ReviewedApproved:
    struct: EnumItem
  Enum.ModerationStatus.ReviewedRejected:
    struct: EnumItem
  Enum.ModifierKey.Alt:
    struct: EnumItem
  Enum.ModifierKey.Ctrl:
    struct: EnumItem
  Enum.ModifierKey.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ModifierKey.Meta:
    struct: EnumItem
  Enum.ModifierKey.Shift:
    struct: EnumItem
  Enum.MouseBehavior.Default:
    struct: EnumItem
  Enum.MouseBehavior.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.MouseBehavior.LockCenter:
    struct: EnumItem
  Enum.MouseBehavior.LockCurrentPosition:
    struct: EnumItem
  Enum.MoveState.AirFree:
    struct: EnumItem
  Enum.MoveState.Coasting:
    struct: EnumItem
  Enum.MoveState.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.MoveState.Pushing:
    struct: EnumItem
  Enum.MoveState.Stopped:
    struct: EnumItem
  Enum.MoveState.Stopping:
    struct: EnumItem
  Enum.MoverConstraintRootBehaviorMode.Default:
    struct: EnumItem
  Enum.MoverConstraintRootBehaviorMode.Disabled:
    struct: EnumItem
  Enum.MoverConstraintRootBehaviorMode.Enabled:
    struct: EnumItem
  Enum.MoverConstraintRootBehaviorMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.MuteState.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.MuteState.Muted:
    struct: EnumItem
  Enum.MuteState.Unmuted:
    struct: EnumItem
  Enum.NameOcclusion.EnemyOcclusion:
    struct: EnumItem
  Enum.NameOcclusion.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.NameOcclusion.NoOcclusion:
    struct: EnumItem
  Enum.NameOcclusion.OccludeAll:
    struct: EnumItem
  Enum.NetworkOwnership.Automatic:
    struct: EnumItem
  Enum.NetworkOwnership.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.NetworkOwnership.Manual:
    struct: EnumItem
  Enum.NetworkOwnership.OnContact:
    struct: EnumItem
  Enum.NetworkStatus.Connected:
    struct: EnumItem
  Enum.NetworkStatus.Disconnected:
    struct: EnumItem
  Enum.NetworkStatus.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.NetworkStatus.Unknown:
    struct: EnumItem
  Enum.NoiseType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.NoiseType.SimplexGabor:
    struct: EnumItem
  Enum.NormalId.Back:
    struct: EnumItem
  Enum.NormalId.Bottom:
    struct: EnumItem
  Enum.NormalId.Front:
    struct: EnumItem
  Enum.NormalId.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.NormalId.Left:
    struct: EnumItem
  Enum.NormalId.Right:
    struct: EnumItem
  Enum.NormalId.Top:
    struct: EnumItem
  Enum.NotificationButtonType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.NotificationButtonType.Primary:
    struct: EnumItem
  Enum.NotificationButtonType.Secondary:
    struct: EnumItem
  Enum.OperationType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.OperationType.Intersection:
    struct: EnumItem
  Enum.OperationType.Null:
    struct: EnumItem
  Enum.OperationType.Primitive:
    struct: EnumItem
  Enum.OperationType.Subtraction:
    struct: EnumItem
  Enum.OperationType.Union:
    struct: EnumItem
  Enum.OrientationAlignmentMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.OrientationAlignmentMode.OneAttachment:
    struct: EnumItem
  Enum.OrientationAlignmentMode.TwoAttachment:
    struct: EnumItem
  Enum.OutfitSource.All:
    struct: EnumItem
  Enum.OutfitSource.Created:
    struct: EnumItem
  Enum.OutfitSource.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.OutfitSource.Purchased:
    struct: EnumItem
  Enum.OutfitType.All:
    struct: EnumItem
  Enum.OutfitType.Avatar:
    struct: EnumItem
  Enum.OutfitType.DynamicHead:
    struct: EnumItem
  Enum.OutfitType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.OutputLayoutMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.OutputLayoutMode.Horizontal:
    struct: EnumItem
  Enum.OutputLayoutMode.Vertical:
    struct: EnumItem
  Enum.OverrideMouseIconBehavior.ForceHide:
    struct: EnumItem
  Enum.OverrideMouseIconBehavior.ForceShow:
    struct: EnumItem
  Enum.OverrideMouseIconBehavior.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.OverrideMouseIconBehavior.None:
    struct: EnumItem
  Enum.PackagePermission.Edit:
    struct: EnumItem
  Enum.PackagePermission.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PackagePermission.NoAccess:
    struct: EnumItem
  Enum.PackagePermission.None:
    struct: EnumItem
  Enum.PackagePermission.Own:
    struct: EnumItem
  Enum.PackagePermission.Revoked:
    struct: EnumItem
  Enum.PackagePermission.UseView:
    struct: EnumItem
  Enum.PartType.Ball:
    struct: EnumItem
  Enum.PartType.Block:
    struct: EnumItem
  Enum.PartType.CornerWedge:
    struct: EnumItem
  Enum.PartType.Cylinder:
    struct: EnumItem
  Enum.PartType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PartType.Wedge:
    struct: EnumItem
  Enum.ParticleEmitterShape.Box:
    struct: EnumItem
  Enum.ParticleEmitterShape.Cylinder:
    struct: EnumItem
  Enum.ParticleEmitterShape.Disc:
    struct: EnumItem
  Enum.ParticleEmitterShape.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ParticleEmitterShape.Sphere:
    struct: EnumItem
  Enum.ParticleEmitterShapeInOut.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ParticleEmitterShapeInOut.InAndOut:
    struct: EnumItem
  Enum.ParticleEmitterShapeInOut.Inward:
    struct: EnumItem
  Enum.ParticleEmitterShapeInOut.Outward:
    struct: EnumItem
  Enum.ParticleEmitterShapeStyle.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ParticleEmitterShapeStyle.Surface:
    struct: EnumItem
  Enum.ParticleEmitterShapeStyle.Volume:
    struct: EnumItem
  Enum.ParticleFlipbookLayout.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ParticleFlipbookLayout.Grid2x2:
    struct: EnumItem
  Enum.ParticleFlipbookLayout.Grid4x4:
    struct: EnumItem
  Enum.ParticleFlipbookLayout.Grid8x8:
    struct: EnumItem
  Enum.ParticleFlipbookLayout.None:
    struct: EnumItem
  Enum.ParticleFlipbookMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ParticleFlipbookMode.Loop:
    struct: EnumItem
  Enum.ParticleFlipbookMode.OneShot:
    struct: EnumItem
  Enum.ParticleFlipbookMode.PingPong:
    struct: EnumItem
  Enum.ParticleFlipbookMode.Random:
    struct: EnumItem
  Enum.ParticleFlipbookTextureCompatible.Compatible:
    struct: EnumItem
  Enum.ParticleFlipbookTextureCompatible.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ParticleFlipbookTextureCompatible.NotCompatible:
    struct: EnumItem
  Enum.ParticleFlipbookTextureCompatible.Unknown:
    struct: EnumItem
  Enum.ParticleOrientation.FacingCamera:
    struct: EnumItem
  Enum.ParticleOrientation.FacingCameraWorldUp:
    struct: EnumItem
  Enum.ParticleOrientation.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ParticleOrientation.VelocityParallel:
    struct: EnumItem
  Enum.ParticleOrientation.VelocityPerpendicular:
    struct: EnumItem
  Enum.PathStatus.ClosestNoPath:
    struct: EnumItem
  Enum.PathStatus.ClosestOutOfRange:
    struct: EnumItem
  Enum.PathStatus.FailFinishNotEmpty:
    struct: EnumItem
  Enum.PathStatus.FailStartNotEmpty:
    struct: EnumItem
  Enum.PathStatus.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PathStatus.NoPath:
    struct: EnumItem
  Enum.PathStatus.Success:
    struct: EnumItem
  Enum.PathWaypointAction.Custom:
    struct: EnumItem
  Enum.PathWaypointAction.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PathWaypointAction.Jump:
    struct: EnumItem
  Enum.PathWaypointAction.Walk:
    struct: EnumItem
  Enum.PathfindingUseImprovedSearch.Default:
    struct: EnumItem
  Enum.PathfindingUseImprovedSearch.Disabled:
    struct: EnumItem
  Enum.PathfindingUseImprovedSearch.Enabled:
    struct: EnumItem
  Enum.PathfindingUseImprovedSearch.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PermissionLevelShown.Game:
    struct: EnumItem
  Enum.PermissionLevelShown.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PermissionLevelShown.Roblox:
    struct: EnumItem
  Enum.PermissionLevelShown.RobloxGame:
    struct: EnumItem
  Enum.PermissionLevelShown.RobloxScript:
    struct: EnumItem
  Enum.PermissionLevelShown.Studio:
    struct: EnumItem
  Enum.PhysicsSimulationRate.Fixed120Hz:
    struct: EnumItem
  Enum.PhysicsSimulationRate.Fixed240Hz:
    struct: EnumItem
  Enum.PhysicsSimulationRate.Fixed60Hz:
    struct: EnumItem
  Enum.PhysicsSimulationRate.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PhysicsSteppingMethod.Adaptive:
    struct: EnumItem
  Enum.PhysicsSteppingMethod.Default:
    struct: EnumItem
  Enum.PhysicsSteppingMethod.Fixed:
    struct: EnumItem
  Enum.PhysicsSteppingMethod.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.Platform.Android:
    struct: EnumItem
  Enum.Platform.AndroidTV:
    struct: EnumItem
  Enum.Platform.BeOS:
    struct: EnumItem
  Enum.Platform.Chromecast:
    struct: EnumItem
  Enum.Platform.DOS:
    struct: EnumItem
  Enum.Platform.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.Platform.IOS:
    struct: EnumItem
  Enum.Platform.Linux:
    struct: EnumItem
  Enum.Platform.MetaOS:
    struct: EnumItem
  Enum.Platform.NX:
    struct: EnumItem
  Enum.Platform.None:
    struct: EnumItem
  Enum.Platform.OSX:
    struct: EnumItem
  Enum.Platform.Ouya:
    struct: EnumItem
  Enum.Platform.PS3:
    struct: EnumItem
  Enum.Platform.PS4:
    struct: EnumItem
  Enum.Platform.PS5:
    struct: EnumItem
  Enum.Platform.SteamOS:
    struct: EnumItem
  Enum.Platform.UWP:
    struct: EnumItem
  Enum.Platform.WebOS:
    struct: EnumItem
  Enum.Platform.WiiU:
    struct: EnumItem
  Enum.Platform.Windows:
    struct: EnumItem
  Enum.Platform.XBox360:
    struct: EnumItem
  Enum.Platform.XBoxOne:
    struct: EnumItem
  Enum.PlaybackState.Begin:
    struct: EnumItem
  Enum.PlaybackState.Cancelled:
    struct: EnumItem
  Enum.PlaybackState.Completed:
    struct: EnumItem
  Enum.PlaybackState.Delayed:
    struct: EnumItem
  Enum.PlaybackState.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PlaybackState.Paused:
    struct: EnumItem
  Enum.PlaybackState.Playing:
    struct: EnumItem
  Enum.PlayerActions.CharacterBackward:
    struct: EnumItem
  Enum.PlayerActions.CharacterForward:
    struct: EnumItem
  Enum.PlayerActions.CharacterJump:
    struct: EnumItem
  Enum.PlayerActions.CharacterLeft:
    struct: EnumItem
  Enum.PlayerActions.CharacterRight:
    struct: EnumItem
  Enum.PlayerActions.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PlayerCharacterDestroyBehavior.Default:
    struct: EnumItem
  Enum.PlayerCharacterDestroyBehavior.Disabled:
    struct: EnumItem
  Enum.PlayerCharacterDestroyBehavior.Enabled:
    struct: EnumItem
  Enum.PlayerCharacterDestroyBehavior.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PlayerChatType.All:
    struct: EnumItem
  Enum.PlayerChatType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PlayerChatType.Team:
    struct: EnumItem
  Enum.PlayerChatType.Whisper:
    struct: EnumItem
  Enum.PlayerDataErrorState.FlushFailed:
    struct: EnumItem
  Enum.PlayerDataErrorState.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PlayerDataErrorState.LoadFailed:
    struct: EnumItem
  Enum.PlayerDataErrorState.None:
    struct: EnumItem
  Enum.PlayerDataErrorState.ReleaseFailed:
    struct: EnumItem
  Enum.PlayerDataLoadFailureBehavior.Failure:
    struct: EnumItem
  Enum.PlayerDataLoadFailureBehavior.FallbackToDefault:
    struct: EnumItem
  Enum.PlayerDataLoadFailureBehavior.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PlayerDataLoadFailureBehavior.Kick:
    struct: EnumItem
  Enum.PoseEasingDirection.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PoseEasingDirection.In:
    struct: EnumItem
  Enum.PoseEasingDirection.InOut:
    struct: EnumItem
  Enum.PoseEasingDirection.Out:
    struct: EnumItem
  Enum.PoseEasingStyle.Bounce:
    struct: EnumItem
  Enum.PoseEasingStyle.Constant:
    struct: EnumItem
  Enum.PoseEasingStyle.Cubic:
    struct: EnumItem
  Enum.PoseEasingStyle.CubicV2:
    struct: EnumItem
  Enum.PoseEasingStyle.Elastic:
    struct: EnumItem
  Enum.PoseEasingStyle.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PoseEasingStyle.Linear:
    struct: EnumItem
  Enum.PositionAlignmentMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PositionAlignmentMode.OneAttachment:
    struct: EnumItem
  Enum.PositionAlignmentMode.TwoAttachment:
    struct: EnumItem
  Enum.PreferredTextSize.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PreferredTextSize.Large:
    struct: EnumItem
  Enum.PreferredTextSize.Larger:
    struct: EnumItem
  Enum.PreferredTextSize.Largest:
    struct: EnumItem
  Enum.PreferredTextSize.Medium:
    struct: EnumItem
  Enum.PrimalPhysicsSolver.Default:
    struct: EnumItem
  Enum.PrimalPhysicsSolver.Disabled:
    struct: EnumItem
  Enum.PrimalPhysicsSolver.Experimental:
    struct: EnumItem
  Enum.PrimalPhysicsSolver.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PrimitiveType.Ball:
    struct: EnumItem
  Enum.PrimitiveType.Block:
    struct: EnumItem
  Enum.PrimitiveType.CornerWedge:
    struct: EnumItem
  Enum.PrimitiveType.Cylinder:
    struct: EnumItem
  Enum.PrimitiveType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PrimitiveType.Null:
    struct: EnumItem
  Enum.PrimitiveType.Wedge:
    struct: EnumItem
  Enum.PrivilegeType.Admin:
    struct: EnumItem
  Enum.PrivilegeType.Banned:
    struct: EnumItem
  Enum.PrivilegeType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PrivilegeType.Member:
    struct: EnumItem
  Enum.PrivilegeType.Owner:
    struct: EnumItem
  Enum.PrivilegeType.Visitor:
    struct: EnumItem
  Enum.ProductLocationRestriction.AllGames:
    struct: EnumItem
  Enum.ProductLocationRestriction.AllowedGames:
    struct: EnumItem
  Enum.ProductLocationRestriction.AvatarShop:
    struct: EnumItem
  Enum.ProductLocationRestriction.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ProductPurchaseChannel.AdReward:
    struct: EnumItem
  Enum.ProductPurchaseChannel.CommerceProduct:
    struct: EnumItem
  Enum.ProductPurchaseChannel.ExperienceDetailsPage:
    struct: EnumItem
  Enum.ProductPurchaseChannel.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ProductPurchaseChannel.InExperience:
    struct: EnumItem
  Enum.ProductPurchaseDecision.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ProductPurchaseDecision.NotProcessedYet:
    struct: EnumItem
  Enum.ProductPurchaseDecision.PurchaseGranted:
    struct: EnumItem
  Enum.PromptCreateAssetResult.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PromptCreateAssetResult.NoUserInput:
    struct: EnumItem
  Enum.PromptCreateAssetResult.PermissionDenied:
    struct: EnumItem
  Enum.PromptCreateAssetResult.Success:
    struct: EnumItem
  Enum.PromptCreateAssetResult.Timeout:
    struct: EnumItem
  Enum.PromptCreateAssetResult.UnknownFailure:
    struct: EnumItem
  Enum.PromptCreateAssetResult.UploadFailed:
    struct: EnumItem
  Enum.PromptCreateAvatarResult.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PromptCreateAvatarResult.InvalidHumanoidDescription:
    struct: EnumItem
  Enum.PromptCreateAvatarResult.MaxOutfits:
    struct: EnumItem
  Enum.PromptCreateAvatarResult.ModeratedName:
    struct: EnumItem
  Enum.PromptCreateAvatarResult.NoUserInput:
    struct: EnumItem
  Enum.PromptCreateAvatarResult.PermissionDenied:
    struct: EnumItem
  Enum.PromptCreateAvatarResult.PurchaseFailure:
    struct: EnumItem
  Enum.PromptCreateAvatarResult.Success:
    struct: EnumItem
  Enum.PromptCreateAvatarResult.Timeout:
    struct: EnumItem
  Enum.PromptCreateAvatarResult.TokenInvalid:
    struct: EnumItem
  Enum.PromptCreateAvatarResult.UGCValidationFailed:
    struct: EnumItem
  Enum.PromptCreateAvatarResult.UnknownFailure:
    struct: EnumItem
  Enum.PromptCreateAvatarResult.UploadFailed:
    struct: EnumItem
  Enum.PromptPublishAssetResult.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PromptPublishAssetResult.NoUserInput:
    struct: EnumItem
  Enum.PromptPublishAssetResult.PermissionDenied:
    struct: EnumItem
  Enum.PromptPublishAssetResult.Success:
    struct: EnumItem
  Enum.PromptPublishAssetResult.Timeout:
    struct: EnumItem
  Enum.PromptPublishAssetResult.UnknownFailure:
    struct: EnumItem
  Enum.PromptPublishAssetResult.UploadFailed:
    struct: EnumItem
  Enum.PropertyStatus.Error:
    struct: EnumItem
  Enum.PropertyStatus.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.PropertyStatus.Ok:
    struct: EnumItem
  Enum.PropertyStatus.Warning:
    struct: EnumItem
  Enum.ProximityPromptExclusivity.AlwaysShow:
    struct: EnumItem
  Enum.ProximityPromptExclusivity.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ProximityPromptExclusivity.OneGlobally:
    struct: EnumItem
  Enum.ProximityPromptExclusivity.OnePerButton:
    struct: EnumItem
  Enum.ProximityPromptInputType.Gamepad:
    struct: EnumItem
  Enum.ProximityPromptInputType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ProximityPromptInputType.Keyboard:
    struct: EnumItem
  Enum.ProximityPromptInputType.Touch:
    struct: EnumItem
  Enum.ProximityPromptStyle.Custom:
    struct: EnumItem
  Enum.ProximityPromptStyle.Default:
    struct: EnumItem
  Enum.ProximityPromptStyle.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.QualityLevel.Automatic:
    struct: EnumItem
  Enum.QualityLevel.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.QualityLevel.Level  1:
    struct: EnumItem
    deprecated:
      message: Enum.QualityLevel.Level  1 was replaced with Enum.QualityLevel.Level01
      replace:
      - Enum.QualityLevel.Level01
  Enum.QualityLevel.Level  2:
    struct: EnumItem
    deprecated:
      message: Enum.QualityLevel.Level  2 was replaced with Enum.QualityLevel.Level02
      replace:
      - Enum.QualityLevel.Level02
  Enum.QualityLevel.Level  3:
    struct: EnumItem
    deprecated:
      message: Enum.QualityLevel.Level  3 was replaced with Enum.QualityLevel.Level03
      replace:
      - Enum.QualityLevel.Level03
  Enum.QualityLevel.Level  4:
    struct: EnumItem
    deprecated:
      message: Enum.QualityLevel.Level  4 was replaced with Enum.QualityLevel.Level04
      replace:
      - Enum.QualityLevel.Level04
  Enum.QualityLevel.Level  5:
    struct: EnumItem
    deprecated:
      message: Enum.QualityLevel.Level  5 was replaced with Enum.QualityLevel.Level05
      replace:
      - Enum.QualityLevel.Level05
  Enum.QualityLevel.Level  6:
    struct: EnumItem
    deprecated:
      message: Enum.QualityLevel.Level  6 was replaced with Enum.QualityLevel.Level06
      replace:
      - Enum.QualityLevel.Level06
  Enum.QualityLevel.Level  7:
    struct: EnumItem
    deprecated:
      message: Enum.QualityLevel.Level  7 was replaced with Enum.QualityLevel.Level07
      replace:
      - Enum.QualityLevel.Level07
  Enum.QualityLevel.Level  8:
    struct: EnumItem
    deprecated:
      message: Enum.QualityLevel.Level  8 was replaced with Enum.QualityLevel.Level08
      replace:
      - Enum.QualityLevel.Level08
  Enum.QualityLevel.Level  9:
    struct: EnumItem
    deprecated:
      message: Enum.QualityLevel.Level  9 was replaced with Enum.QualityLevel.Level09
      replace:
      - Enum.QualityLevel.Level09
  Enum.QualityLevel.Level 10:
    struct: EnumItem
    deprecated:
      message: Enum.QualityLevel.Level 10 was replaced with Enum.QualityLevel.Level10
      replace:
      - Enum.QualityLevel.Level10
  Enum.QualityLevel.Level 11:
    struct: EnumItem
    deprecated:
      message: Enum.QualityLevel.Level 11 was replaced with Enum.QualityLevel.Level11
      replace:
      - Enum.QualityLevel.Level11
  Enum.QualityLevel.Level 12:
    struct: EnumItem
    deprecated:
      message: Enum.QualityLevel.Level 12 was replaced with Enum.QualityLevel.Level12
      replace:
      - Enum.QualityLevel.Level12
  Enum.QualityLevel.Level 13:
    struct: EnumItem
    deprecated:
      message: Enum.QualityLevel.Level 13 was replaced with Enum.QualityLevel.Level13
      replace:
      - Enum.QualityLevel.Level13
  Enum.QualityLevel.Level 14:
    struct: EnumItem
    deprecated:
      message: Enum.QualityLevel.Level 14 was replaced with Enum.QualityLevel.Level14
      replace:
      - Enum.QualityLevel.Level14
  Enum.QualityLevel.Level 15:
    struct: EnumItem
    deprecated:
      message: Enum.QualityLevel.Level 15 was replaced with Enum.QualityLevel.Level15
      replace:
      - Enum.QualityLevel.Level15
  Enum.QualityLevel.Level 16:
    struct: EnumItem
    deprecated:
      message: Enum.QualityLevel.Level 16 was replaced with Enum.QualityLevel.Level16
      replace:
      - Enum.QualityLevel.Level16
  Enum.QualityLevel.Level 17:
    struct: EnumItem
    deprecated:
      message: Enum.QualityLevel.Level 17 was replaced with Enum.QualityLevel.Level17
      replace:
      - Enum.QualityLevel.Level17
  Enum.QualityLevel.Level 18:
    struct: EnumItem
    deprecated:
      message: Enum.QualityLevel.Level 18 was replaced with Enum.QualityLevel.Level18
      replace:
      - Enum.QualityLevel.Level18
  Enum.QualityLevel.Level 19:
    struct: EnumItem
    deprecated:
      message: Enum.QualityLevel.Level 19 was replaced with Enum.QualityLevel.Level19
      replace:
      - Enum.QualityLevel.Level19
  Enum.QualityLevel.Level 20:
    struct: EnumItem
    deprecated:
      message: Enum.QualityLevel.Level 20 was replaced with Enum.QualityLevel.Level20
      replace:
      - Enum.QualityLevel.Level20
  Enum.QualityLevel.Level 21:
    struct: EnumItem
    deprecated:
      message: Enum.QualityLevel.Level 21 was replaced with Enum.QualityLevel.Level21
      replace:
      - Enum.QualityLevel.Level21
  Enum.QualityLevel.Level01:
    struct: EnumItem
  Enum.QualityLevel.Level02:
    struct: EnumItem
  Enum.QualityLevel.Level03:
    struct: EnumItem
  Enum.QualityLevel.Level04:
    struct: EnumItem
  Enum.QualityLevel.Level05:
    struct: EnumItem
  Enum.QualityLevel.Level06:
    struct: EnumItem
  Enum.QualityLevel.Level07:
    struct: EnumItem
  Enum.QualityLevel.Level08:
    struct: EnumItem
  Enum.QualityLevel.Level09:
    struct: EnumItem
  Enum.QualityLevel.Level10:
    struct: EnumItem
  Enum.QualityLevel.Level11:
    struct: EnumItem
  Enum.QualityLevel.Level12:
    struct: EnumItem
  Enum.QualityLevel.Level13:
    struct: EnumItem
  Enum.QualityLevel.Level14:
    struct: EnumItem
  Enum.QualityLevel.Level15:
    struct: EnumItem
  Enum.QualityLevel.Level16:
    struct: EnumItem
  Enum.QualityLevel.Level17:
    struct: EnumItem
  Enum.QualityLevel.Level18:
    struct: EnumItem
  Enum.QualityLevel.Level19:
    struct: EnumItem
  Enum.QualityLevel.Level20:
    struct: EnumItem
  Enum.QualityLevel.Level21:
    struct: EnumItem
  Enum.R15CollisionType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.R15CollisionType.InnerBox:
    struct: EnumItem
  Enum.R15CollisionType.OuterBox:
    struct: EnumItem
  Enum.RaycastFilterType.Blacklist:
    struct: EnumItem
    deprecated:
      message: Enum.RaycastFilterType.Blacklist was replaced with Enum.RaycastFilterType.Exclude
      replace:
      - Enum.RaycastFilterType.Exclude
  Enum.RaycastFilterType.Exclude:
    struct: EnumItem
  Enum.RaycastFilterType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.RaycastFilterType.Include:
    struct: EnumItem
  Enum.RaycastFilterType.Whitelist:
    struct: EnumItem
    deprecated:
      message: Enum.RaycastFilterType.Whitelist was replaced with Enum.RaycastFilterType.Include
      replace:
      - Enum.RaycastFilterType.Include
  Enum.RejectCharacterDeletions.Default:
    struct: EnumItem
  Enum.RejectCharacterDeletions.Disabled:
    struct: EnumItem
  Enum.RejectCharacterDeletions.Enabled:
    struct: EnumItem
  Enum.RejectCharacterDeletions.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.RenderFidelity.Automatic:
    struct: EnumItem
  Enum.RenderFidelity.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.RenderFidelity.Performance:
    struct: EnumItem
  Enum.RenderFidelity.Precise:
    struct: EnumItem
  Enum.RenderPriority.Camera:
    struct: EnumItem
  Enum.RenderPriority.Character:
    struct: EnumItem
  Enum.RenderPriority.First:
    struct: EnumItem
  Enum.RenderPriority.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.RenderPriority.Input:
    struct: EnumItem
  Enum.RenderPriority.Last:
    struct: EnumItem
  Enum.RenderingCacheOptimizationMode.Default:
    struct: EnumItem
  Enum.RenderingCacheOptimizationMode.Disabled:
    struct: EnumItem
  Enum.RenderingCacheOptimizationMode.Enabled:
    struct: EnumItem
  Enum.RenderingCacheOptimizationMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.RenderingTestComparisonMethod.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.RenderingTestComparisonMethod.diff:
    struct: EnumItem
  Enum.RenderingTestComparisonMethod.psnr:
    struct: EnumItem
  Enum.ReplicateInstanceDestroySetting.Default:
    struct: EnumItem
  Enum.ReplicateInstanceDestroySetting.Disabled:
    struct: EnumItem
  Enum.ReplicateInstanceDestroySetting.Enabled:
    struct: EnumItem
  Enum.ReplicateInstanceDestroySetting.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ResamplerMode.Default:
    struct: EnumItem
  Enum.ResamplerMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ResamplerMode.Pixelated:
    struct: EnumItem
  Enum.ReservedHighlightId.Active:
    struct: EnumItem
  Enum.ReservedHighlightId.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ReservedHighlightId.Hover:
    struct: EnumItem
  Enum.ReservedHighlightId.Selection:
    struct: EnumItem
  Enum.ReservedHighlightId.Standard:
    struct: EnumItem
  Enum.RestPose.Custom:
    struct: EnumItem
  Enum.RestPose.Default:
    struct: EnumItem
  Enum.RestPose.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.RestPose.RotationsReset:
    struct: EnumItem
  Enum.ReturnKeyType.Default:
    struct: EnumItem
  Enum.ReturnKeyType.Done:
    struct: EnumItem
  Enum.ReturnKeyType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ReturnKeyType.Go:
    struct: EnumItem
  Enum.ReturnKeyType.Next:
    struct: EnumItem
  Enum.ReturnKeyType.Search:
    struct: EnumItem
  Enum.ReturnKeyType.Send:
    struct: EnumItem
  Enum.ReverbType.Alley:
    struct: EnumItem
  Enum.ReverbType.Arena:
    struct: EnumItem
  Enum.ReverbType.Auditorium:
    struct: EnumItem
  Enum.ReverbType.Bathroom:
    struct: EnumItem
  Enum.ReverbType.CarpettedHallway:
    struct: EnumItem
  Enum.ReverbType.Cave:
    struct: EnumItem
  Enum.ReverbType.City:
    struct: EnumItem
  Enum.ReverbType.ConcertHall:
    struct: EnumItem
  Enum.ReverbType.Forest:
    struct: EnumItem
  Enum.ReverbType.GenericReverb:
    struct: EnumItem
  Enum.ReverbType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ReverbType.Hallway:
    struct: EnumItem
  Enum.ReverbType.Hangar:
    struct: EnumItem
  Enum.ReverbType.LivingRoom:
    struct: EnumItem
  Enum.ReverbType.Mountains:
    struct: EnumItem
  Enum.ReverbType.NoReverb:
    struct: EnumItem
  Enum.ReverbType.PaddedCell:
    struct: EnumItem
  Enum.ReverbType.ParkingLot:
    struct: EnumItem
  Enum.ReverbType.Plain:
    struct: EnumItem
  Enum.ReverbType.Quarry:
    struct: EnumItem
  Enum.ReverbType.Room:
    struct: EnumItem
  Enum.ReverbType.SewerPipe:
    struct: EnumItem
  Enum.ReverbType.StoneCorridor:
    struct: EnumItem
  Enum.ReverbType.StoneRoom:
    struct: EnumItem
  Enum.ReverbType.UnderWater:
    struct: EnumItem
  Enum.RibbonTool.ColorPicker:
    struct: EnumItem
  Enum.RibbonTool.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.RibbonTool.Group:
    struct: EnumItem
  Enum.RibbonTool.MaterialPicker:
    struct: EnumItem
  Enum.RibbonTool.Move:
    struct: EnumItem
  Enum.RibbonTool.None:
    struct: EnumItem
  Enum.RibbonTool.PivotEditor:
    struct: EnumItem
  Enum.RibbonTool.Rotate:
    struct: EnumItem
  Enum.RibbonTool.Scale:
    struct: EnumItem
  Enum.RibbonTool.Select:
    struct: EnumItem
  Enum.RibbonTool.Transform:
    struct: EnumItem
  Enum.RibbonTool.Ungroup:
    struct: EnumItem
  Enum.RigScale.Default:
    struct: EnumItem
  Enum.RigScale.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.RigScale.Rthro:
    struct: EnumItem
  Enum.RigScale.RthroNarrow:
    struct: EnumItem
  Enum.RigType.Custom:
    struct: EnumItem
  Enum.RigType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.RigType.None:
    struct: EnumItem
  Enum.RigType.R15:
    struct: EnumItem
  Enum.RollOffMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.RollOffMode.Inverse:
    struct: EnumItem
  Enum.RollOffMode.InverseTapered:
    struct: EnumItem
  Enum.RollOffMode.Linear:
    struct: EnumItem
  Enum.RollOffMode.LinearSquare:
    struct: EnumItem
  Enum.RolloutState.Default:
    struct: EnumItem
  Enum.RolloutState.Disabled:
    struct: EnumItem
  Enum.RolloutState.Enabled:
    struct: EnumItem
  Enum.RolloutState.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.RotationOrder.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.RotationOrder.XYZ:
    struct: EnumItem
  Enum.RotationOrder.XZY:
    struct: EnumItem
  Enum.RotationOrder.YXZ:
    struct: EnumItem
  Enum.RotationOrder.YZX:
    struct: EnumItem
  Enum.RotationOrder.ZXY:
    struct: EnumItem
  Enum.RotationOrder.ZYX:
    struct: EnumItem
  Enum.RotationType.CameraRelative:
    struct: EnumItem
  Enum.RotationType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.RotationType.MovementRelative:
    struct: EnumItem
  Enum.RtlTextSupport.Default:
    struct: EnumItem
  Enum.RtlTextSupport.Disabled:
    struct: EnumItem
  Enum.RtlTextSupport.Enabled:
    struct: EnumItem
  Enum.RtlTextSupport.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.RunContext.Client:
    struct: EnumItem
  Enum.RunContext.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.RunContext.Legacy:
    struct: EnumItem
  Enum.RunContext.Plugin:
    struct: EnumItem
  Enum.RunContext.Server:
    struct: EnumItem
  Enum.RunState.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.RunState.Paused:
    struct: EnumItem
  Enum.RunState.Running:
    struct: EnumItem
  Enum.RunState.Stopped:
    struct: EnumItem
  Enum.RuntimeUndoBehavior.Aggregate:
    struct: EnumItem
  Enum.RuntimeUndoBehavior.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.RuntimeUndoBehavior.Hybrid:
    struct: EnumItem
  Enum.RuntimeUndoBehavior.Snapshot:
    struct: EnumItem
  Enum.SafeAreaCompatibility.FullscreenExtension:
    struct: EnumItem
  Enum.SafeAreaCompatibility.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SafeAreaCompatibility.None:
    struct: EnumItem
  Enum.SalesTypeFilter.All:
    struct: EnumItem
  Enum.SalesTypeFilter.Collectibles:
    struct: EnumItem
  Enum.SalesTypeFilter.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SalesTypeFilter.Premium:
    struct: EnumItem
  Enum.SandboxedInstanceMode.Default:
    struct: EnumItem
  Enum.SandboxedInstanceMode.Experimental:
    struct: EnumItem
  Enum.SandboxedInstanceMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SaveAvatarThumbnailCustomizationFailure.BadDistanceScale:
    struct: EnumItem
  Enum.SaveAvatarThumbnailCustomizationFailure.BadFieldOfViewDeg:
    struct: EnumItem
  Enum.SaveAvatarThumbnailCustomizationFailure.BadThumbnailType:
    struct: EnumItem
  Enum.SaveAvatarThumbnailCustomizationFailure.BadYRotDeg:
    struct: EnumItem
  Enum.SaveAvatarThumbnailCustomizationFailure.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SaveAvatarThumbnailCustomizationFailure.Other:
    struct: EnumItem
  Enum.SaveAvatarThumbnailCustomizationFailure.Throttled:
    struct: EnumItem
  Enum.SaveFilter.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SaveFilter.SaveAll:
    struct: EnumItem
  Enum.SaveFilter.SaveGame:
    struct: EnumItem
  Enum.SaveFilter.SaveWorld:
    struct: EnumItem
  Enum.SavedQualitySetting.Automatic:
    struct: EnumItem
  Enum.SavedQualitySetting.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SavedQualitySetting.QualityLevel1:
    struct: EnumItem
  Enum.SavedQualitySetting.QualityLevel10:
    struct: EnumItem
  Enum.SavedQualitySetting.QualityLevel2:
    struct: EnumItem
  Enum.SavedQualitySetting.QualityLevel3:
    struct: EnumItem
  Enum.SavedQualitySetting.QualityLevel4:
    struct: EnumItem
  Enum.SavedQualitySetting.QualityLevel5:
    struct: EnumItem
  Enum.SavedQualitySetting.QualityLevel6:
    struct: EnumItem
  Enum.SavedQualitySetting.QualityLevel7:
    struct: EnumItem
  Enum.SavedQualitySetting.QualityLevel8:
    struct: EnumItem
  Enum.SavedQualitySetting.QualityLevel9:
    struct: EnumItem
  Enum.ScaleType.Crop:
    struct: EnumItem
  Enum.ScaleType.Fit:
    struct: EnumItem
  Enum.ScaleType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ScaleType.Slice:
    struct: EnumItem
  Enum.ScaleType.Stretch:
    struct: EnumItem
  Enum.ScaleType.Tile:
    struct: EnumItem
  Enum.ScopeCheckResult.BackendError:
    struct: EnumItem
  Enum.ScopeCheckResult.ConsentAccepted:
    struct: EnumItem
  Enum.ScopeCheckResult.ConsentDenied:
    struct: EnumItem
  Enum.ScopeCheckResult.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ScopeCheckResult.InvalidArgument:
    struct: EnumItem
  Enum.ScopeCheckResult.InvalidScopes:
    struct: EnumItem
  Enum.ScopeCheckResult.NoUserInput:
    struct: EnumItem
  Enum.ScopeCheckResult.Timeout:
    struct: EnumItem
  Enum.ScopeCheckResult.UnexpectedError:
    struct: EnumItem
  Enum.ScreenInsets.CoreUISafeInsets:
    struct: EnumItem
  Enum.ScreenInsets.DeviceSafeInsets:
    struct: EnumItem
  Enum.ScreenInsets.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ScreenInsets.None:
    struct: EnumItem
  Enum.ScreenInsets.TopbarSafeInsets:
    struct: EnumItem
  Enum.ScreenOrientation.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ScreenOrientation.LandscapeLeft:
    struct: EnumItem
  Enum.ScreenOrientation.LandscapeRight:
    struct: EnumItem
  Enum.ScreenOrientation.LandscapeSensor:
    struct: EnumItem
  Enum.ScreenOrientation.Portrait:
    struct: EnumItem
  Enum.ScreenOrientation.Sensor:
    struct: EnumItem
  Enum.ScrollBarInset.Always:
    struct: EnumItem
  Enum.ScrollBarInset.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ScrollBarInset.None:
    struct: EnumItem
  Enum.ScrollBarInset.ScrollBar:
    struct: EnumItem
  Enum.ScrollingDirection.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ScrollingDirection.X:
    struct: EnumItem
  Enum.ScrollingDirection.XY:
    struct: EnumItem
  Enum.ScrollingDirection.Y:
    struct: EnumItem
  Enum.SecurityCapability.AccessOutsideWrite:
    struct: EnumItem
  Enum.SecurityCapability.Animation:
    struct: EnumItem
  Enum.SecurityCapability.AssetRequire:
    struct: EnumItem
  Enum.SecurityCapability.Audio:
    struct: EnumItem
  Enum.SecurityCapability.Avatar:
    struct: EnumItem
  Enum.SecurityCapability.Basic:
    struct: EnumItem
  Enum.SecurityCapability.CSG:
    struct: EnumItem
  Enum.SecurityCapability.Chat:
    struct: EnumItem
  Enum.SecurityCapability.CreateInstances:
    struct: EnumItem
  Enum.SecurityCapability.DataStore:
    struct: EnumItem
  Enum.SecurityCapability.Environment:
    struct: EnumItem
  Enum.SecurityCapability.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SecurityCapability.Input:
    struct: EnumItem
  Enum.SecurityCapability.LegacySound:
    struct: EnumItem
  Enum.SecurityCapability.LoadString:
    struct: EnumItem
  Enum.SecurityCapability.Network:
    struct: EnumItem
  Enum.SecurityCapability.Physics:
    struct: EnumItem
  Enum.SecurityCapability.Players:
    struct: EnumItem
  Enum.SecurityCapability.RemoteEvent:
    struct: EnumItem
  Enum.SecurityCapability.RunClientScript:
    struct: EnumItem
  Enum.SecurityCapability.RunServerScript:
    struct: EnumItem
  Enum.SecurityCapability.ScriptGlobals:
    struct: EnumItem
  Enum.SecurityCapability.UI:
    struct: EnumItem
  Enum.SelectionBehavior.Escape:
    struct: EnumItem
  Enum.SelectionBehavior.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SelectionBehavior.Stop:
    struct: EnumItem
  Enum.SelectionRenderMode.Both:
    struct: EnumItem
  Enum.SelectionRenderMode.BoundingBoxes:
    struct: EnumItem
  Enum.SelectionRenderMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SelectionRenderMode.Outlines:
    struct: EnumItem
  Enum.SelfViewPosition.BottomLeft:
    struct: EnumItem
  Enum.SelfViewPosition.BottomRight:
    struct: EnumItem
  Enum.SelfViewPosition.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SelfViewPosition.LastPosition:
    struct: EnumItem
  Enum.SelfViewPosition.TopLeft:
    struct: EnumItem
  Enum.SelfViewPosition.TopRight:
    struct: EnumItem
  Enum.SensorMode.Floor:
    struct: EnumItem
  Enum.SensorMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SensorMode.Ladder:
    struct: EnumItem
  Enum.SensorUpdateType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SensorUpdateType.Manual:
    struct: EnumItem
  Enum.SensorUpdateType.OnRead:
    struct: EnumItem
  Enum.ServerLiveEditingMode.Disabled:
    struct: EnumItem
  Enum.ServerLiveEditingMode.Enabled:
    struct: EnumItem
  Enum.ServerLiveEditingMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ServerLiveEditingMode.Uninitialized:
    struct: EnumItem
  Enum.ServiceVisibility.Always:
    struct: EnumItem
  Enum.ServiceVisibility.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ServiceVisibility.Off:
    struct: EnumItem
  Enum.ServiceVisibility.WithChildren:
    struct: EnumItem
  Enum.Severity.Error:
    struct: EnumItem
  Enum.Severity.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.Severity.Hint:
    struct: EnumItem
  Enum.Severity.Information:
    struct: EnumItem
  Enum.Severity.Warning:
    struct: EnumItem
  Enum.ShowAdResult.AdAlreadyShowing:
    struct: EnumItem
  Enum.ShowAdResult.AdNotReady:
    struct: EnumItem
  Enum.ShowAdResult.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ShowAdResult.InsufficientMemory:
    struct: EnumItem
  Enum.ShowAdResult.InternalError:
    struct: EnumItem
  Enum.ShowAdResult.ShowCompleted:
    struct: EnumItem
  Enum.ShowAdResult.ShowInterrupted:
    struct: EnumItem
  Enum.SignalBehavior.AncestryDeferred:
    struct: EnumItem
  Enum.SignalBehavior.Default:
    struct: EnumItem
  Enum.SignalBehavior.Deferred:
    struct: EnumItem
  Enum.SignalBehavior.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SignalBehavior.Immediate:
    struct: EnumItem
  Enum.SizeConstraint.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SizeConstraint.RelativeXX:
    struct: EnumItem
  Enum.SizeConstraint.RelativeXY:
    struct: EnumItem
  Enum.SizeConstraint.RelativeYY:
    struct: EnumItem
  Enum.SolverConvergenceMetricType.AlgorithmAgnostic:
    struct: EnumItem
  Enum.SolverConvergenceMetricType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SolverConvergenceMetricType.IterationBased:
    struct: EnumItem
  Enum.SolverConvergenceVisualizationMode.Disabled:
    struct: EnumItem
  Enum.SolverConvergenceVisualizationMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SolverConvergenceVisualizationMode.PerEdge:
    struct: EnumItem
  Enum.SolverConvergenceVisualizationMode.PerIsland:
    struct: EnumItem
  Enum.SortDirection.Ascending:
    struct: EnumItem
  Enum.SortDirection.Descending:
    struct: EnumItem
  Enum.SortDirection.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SortOrder.Custom:
    struct: EnumItem
  Enum.SortOrder.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SortOrder.LayoutOrder:
    struct: EnumItem
  Enum.SortOrder.Name:
    struct: EnumItem
  Enum.SpecialKey.ChatHotkey:
    struct: EnumItem
  Enum.SpecialKey.End:
    struct: EnumItem
  Enum.SpecialKey.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SpecialKey.Home:
    struct: EnumItem
  Enum.SpecialKey.Insert:
    struct: EnumItem
  Enum.SpecialKey.PageDown:
    struct: EnumItem
  Enum.SpecialKey.PageUp:
    struct: EnumItem
  Enum.StartCorner.BottomLeft:
    struct: EnumItem
  Enum.StartCorner.BottomRight:
    struct: EnumItem
  Enum.StartCorner.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.StartCorner.TopLeft:
    struct: EnumItem
  Enum.StartCorner.TopRight:
    struct: EnumItem
  Enum.StateObjectFieldType.Boolean:
    struct: EnumItem
  Enum.StateObjectFieldType.CFrame:
    struct: EnumItem
  Enum.StateObjectFieldType.Float:
    struct: EnumItem
  Enum.StateObjectFieldType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.StateObjectFieldType.INVALID:
    struct: EnumItem
  Enum.StateObjectFieldType.Instance:
    struct: EnumItem
  Enum.StateObjectFieldType.Random:
    struct: EnumItem
  Enum.StateObjectFieldType.Vector2:
    struct: EnumItem
  Enum.StateObjectFieldType.Vector3:
    struct: EnumItem
  Enum.Status.Confusion:
    struct: EnumItem
  Enum.Status.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.Status.Poison:
    struct: EnumItem
  Enum.StreamOutBehavior.Default:
    struct: EnumItem
  Enum.StreamOutBehavior.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.StreamOutBehavior.LowMemory:
    struct: EnumItem
  Enum.StreamOutBehavior.Opportunistic:
    struct: EnumItem
  Enum.StreamingIntegrityMode.Default:
    struct: EnumItem
  Enum.StreamingIntegrityMode.Disabled:
    struct: EnumItem
  Enum.StreamingIntegrityMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.StreamingIntegrityMode.MinimumRadiusPause:
    struct: EnumItem
  Enum.StreamingIntegrityMode.PauseOutsideLoadedArea:
    struct: EnumItem
  Enum.StreamingPauseMode.ClientPhysicsPause:
    struct: EnumItem
  Enum.StreamingPauseMode.Default:
    struct: EnumItem
  Enum.StreamingPauseMode.Disabled:
    struct: EnumItem
  Enum.StreamingPauseMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.StudioCloseMode.CloseDoc:
    struct: EnumItem
  Enum.StudioCloseMode.CloseStudio:
    struct: EnumItem
  Enum.StudioCloseMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.StudioCloseMode.LogOut:
    struct: EnumItem
  Enum.StudioCloseMode.None:
    struct: EnumItem
  Enum.StudioDataModelType.Edit:
    struct: EnumItem
  Enum.StudioDataModelType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.StudioDataModelType.None:
    struct: EnumItem
  Enum.StudioDataModelType.PlayClient:
    struct: EnumItem
  Enum.StudioDataModelType.PlayServer:
    struct: EnumItem
  Enum.StudioDataModelType.Standalone:
    struct: EnumItem
  Enum.StudioPlaceUpdateFailureReason.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.StudioPlaceUpdateFailureReason.Other:
    struct: EnumItem
  Enum.StudioPlaceUpdateFailureReason.TeamCreateConflict:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.AICOOverlayButtonBackground:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.AICOOverlayButtonBackgroundHover:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.AICOOverlayButtonBackgroundPressed:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.AICOOverlayText:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.ActiveLine:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.Background:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.Bool:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.Bracket:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.Builtin:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.Comment:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.DebuggerCurrentLine:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.DebuggerErrorLine:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.Default:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.DocViewCodeBackground:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.Error:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.FindSelectionBackground:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.Function:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.FunctionName:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.StudioScriptEditorColorCategories.Hint:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.IndentationRuler:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.Info:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.Keyword:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.Local:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.LuauKeyword:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.MatchingWordBackground:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.MenuBackground:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.MenuBorder:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.MenuPrimaryText:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.MenuScrollbarBackground:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.MenuScrollbarHandle:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.MenuSecondaryText:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.MenuSelectedBackground:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.MenuSelectedText:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.Method:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.Nil:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.Number:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.Operator:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.Property:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.Ruler:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.SelectionBackground:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.SelectionText:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.Self:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.String:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.TODO:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.Type:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.Warning:
    struct: EnumItem
  Enum.StudioScriptEditorColorCategories.Whitespace:
    struct: EnumItem
  Enum.StudioScriptEditorColorPresets.Custom:
    struct: EnumItem
  Enum.StudioScriptEditorColorPresets.Extra1:
    struct: EnumItem
  Enum.StudioScriptEditorColorPresets.Extra2:
    struct: EnumItem
  Enum.StudioScriptEditorColorPresets.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.StudioScriptEditorColorPresets.RobloxDefault:
    struct: EnumItem
  Enum.StudioStyleGuideColor.AICOOverlayButtonBackground:
    struct: EnumItem
  Enum.StudioStyleGuideColor.AICOOverlayButtonBackgroundHover:
    struct: EnumItem
  Enum.StudioStyleGuideColor.AICOOverlayButtonBackgroundPressed:
    struct: EnumItem
  Enum.StudioStyleGuideColor.AICOOverlayText:
    struct: EnumItem
  Enum.StudioStyleGuideColor.AttributeCog:
    struct: EnumItem
  Enum.StudioStyleGuideColor.Border:
    struct: EnumItem
  Enum.StudioStyleGuideColor.BreakpointMarker:
    struct: EnumItem
  Enum.StudioStyleGuideColor.BrightText:
    struct: EnumItem
  Enum.StudioStyleGuideColor.Button:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ButtonBorder:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ButtonText:
    struct: EnumItem
  Enum.StudioStyleGuideColor.CategoryItem:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ChatIncomingBgColor:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ChatIncomingTextColor:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ChatModeratedMessageColor:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ChatOutgoingBgColor:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ChatOutgoingTextColor:
    struct: EnumItem
  Enum.StudioStyleGuideColor.CheckedFieldBackground:
    struct: EnumItem
  Enum.StudioStyleGuideColor.CheckedFieldBorder:
    struct: EnumItem
  Enum.StudioStyleGuideColor.CheckedFieldIndicator:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ColorPickerFrame:
    struct: EnumItem
  Enum.StudioStyleGuideColor.CurrentMarker:
    struct: EnumItem
  Enum.StudioStyleGuideColor.Dark:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DebuggerCurrentLine:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DebuggerErrorLine:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DialogButton:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DialogButtonBorder:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DialogButtonText:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DialogMainButton:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DialogMainButtonText:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DiffFilePathBackground:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DiffFilePathBorder:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DiffFilePathText:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DiffLineNum:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DiffLineNumAdditionBackground:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DiffLineNumDeletionBackground:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DiffLineNumHover:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DiffLineNumNoChangeBackground:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DiffLineNumSeparatorBackground:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DiffLineNumSeparatorBackgroundHover:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DiffTextAddition:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DiffTextAdditionBackground:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DiffTextDeletion:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DiffTextDeletionBackground:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DiffTextHunkInfo:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DiffTextNoChange:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DiffTextNoChangeBackground:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DiffTextSeparatorBackground:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DimmedText:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DocViewCodeBackground:
    struct: EnumItem
  Enum.StudioStyleGuideColor.DropShadow:
    struct: EnumItem
  Enum.StudioStyleGuideColor.Dropdown:
    struct: EnumItem
  Enum.StudioStyleGuideColor.EmulatorBar:
    struct: EnumItem
  Enum.StudioStyleGuideColor.EmulatorDropDown:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ErrorText:
    struct: EnumItem
  Enum.StudioStyleGuideColor.FilterButtonAccent:
    struct: EnumItem
  Enum.StudioStyleGuideColor.FilterButtonBorder:
    struct: EnumItem
  Enum.StudioStyleGuideColor.FilterButtonBorderAlt:
    struct: EnumItem
  Enum.StudioStyleGuideColor.FilterButtonChecked:
    struct: EnumItem
  Enum.StudioStyleGuideColor.FilterButtonDefault:
    struct: EnumItem
  Enum.StudioStyleGuideColor.FilterButtonHover:
    struct: EnumItem
  Enum.StudioStyleGuideColor.GameSettingsTableItem:
    struct: EnumItem
  Enum.StudioStyleGuideColor.GameSettingsTooltip:
    struct: EnumItem
  Enum.StudioStyleGuideColor.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.StudioStyleGuideColor.HeaderSection:
    struct: EnumItem
  Enum.StudioStyleGuideColor.InfoBarWarningBackground:
    struct: EnumItem
  Enum.StudioStyleGuideColor.InfoBarWarningText:
    struct: EnumItem
  Enum.StudioStyleGuideColor.InfoText:
    struct: EnumItem
  Enum.StudioStyleGuideColor.InputFieldBackground:
    struct: EnumItem
  Enum.StudioStyleGuideColor.InputFieldBorder:
    struct: EnumItem
  Enum.StudioStyleGuideColor.Item:
    struct: EnumItem
  Enum.StudioStyleGuideColor.Light:
    struct: EnumItem
  Enum.StudioStyleGuideColor.LinkText:
    struct: EnumItem
  Enum.StudioStyleGuideColor.MainBackground:
    struct: EnumItem
  Enum.StudioStyleGuideColor.MainButton:
    struct: EnumItem
  Enum.StudioStyleGuideColor.MainText:
    struct: EnumItem
  Enum.StudioStyleGuideColor.Mid:
    struct: EnumItem
  Enum.StudioStyleGuideColor.Midlight:
    struct: EnumItem
  Enum.StudioStyleGuideColor.Notification:
    struct: EnumItem
  Enum.StudioStyleGuideColor.OnboardingCover:
    struct: EnumItem
  Enum.StudioStyleGuideColor.OnboardingHighlight:
    struct: EnumItem
  Enum.StudioStyleGuideColor.OnboardingShadow:
    struct: EnumItem
  Enum.StudioStyleGuideColor.RibbonButton:
    struct: EnumItem
  Enum.StudioStyleGuideColor.RibbonTab:
    struct: EnumItem
  Enum.StudioStyleGuideColor.RibbonTabTopBar:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptBackground:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptBool:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptBracket:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptBuiltInFunction:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptComment:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptEditorCurrentLine:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptError:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptFindSelectionBackground:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptFunction:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptFunctionName:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptHint:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptInformation:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptKeyword:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptLocal:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptLuauKeyword:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptMatchingWordSelectionBackground:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptMethod:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptNil:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptNumber:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptOperator:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptProperty:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptRuler:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptSelectionBackground:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptSelectionText:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptSelf:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptSideWidget:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptString:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptText:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptTodo:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptWarning:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScriptWhitespace:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScrollBar:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ScrollBarBackground:
    struct: EnumItem
  Enum.StudioStyleGuideColor.SensitiveText:
    struct: EnumItem
  Enum.StudioStyleGuideColor.Separator:
    struct: EnumItem
  Enum.StudioStyleGuideColor.Shadow:
    struct: EnumItem
  Enum.StudioStyleGuideColor.StatusBar:
    struct: EnumItem
  Enum.StudioStyleGuideColor.SubText:
    struct: EnumItem
  Enum.StudioStyleGuideColor.Tab:
    struct: EnumItem
  Enum.StudioStyleGuideColor.TabBar:
    struct: EnumItem
  Enum.StudioStyleGuideColor.TableItem:
    struct: EnumItem
  Enum.StudioStyleGuideColor.Titlebar:
    struct: EnumItem
  Enum.StudioStyleGuideColor.TitlebarText:
    struct: EnumItem
  Enum.StudioStyleGuideColor.Tooltip:
    struct: EnumItem
  Enum.StudioStyleGuideColor.ViewPortBackground:
    struct: EnumItem
  Enum.StudioStyleGuideColor.WarningText:
    struct: EnumItem
  Enum.StudioStyleGuideModifier.Default:
    struct: EnumItem
  Enum.StudioStyleGuideModifier.Disabled:
    struct: EnumItem
  Enum.StudioStyleGuideModifier.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.StudioStyleGuideModifier.Hover:
    struct: EnumItem
  Enum.StudioStyleGuideModifier.Pressed:
    struct: EnumItem
  Enum.StudioStyleGuideModifier.Selected:
    struct: EnumItem
  Enum.Style.Alternating Supports:
    struct: EnumItem
    deprecated:
      message: Enum.Style.Alternating Supports was replaced with Enum.Style.AlternatingSupports
      replace:
      - Enum.Style.AlternatingSupports
  Enum.Style.AlternatingSupports:
    struct: EnumItem
  Enum.Style.Bridge Style Supports:
    struct: EnumItem
    deprecated:
      message: Enum.Style.Bridge Style Supports was replaced with Enum.Style.BridgeStyleSupports
      replace:
      - Enum.Style.BridgeStyleSupports
  Enum.Style.BridgeStyleSupports:
    struct: EnumItem
  Enum.Style.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.Style.No Supports:
    struct: EnumItem
    deprecated:
      message: Enum.Style.No Supports was replaced with Enum.Style.NoSupports
      replace:
      - Enum.Style.NoSupports
  Enum.Style.NoSupports:
    struct: EnumItem
  Enum.SubscriptionExpirationReason.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SubscriptionExpirationReason.Lapsed:
    struct: EnumItem
  Enum.SubscriptionExpirationReason.ProductDeleted:
    struct: EnumItem
  Enum.SubscriptionExpirationReason.ProductInactive:
    struct: EnumItem
  Enum.SubscriptionExpirationReason.SubscriberCancelled:
    struct: EnumItem
  Enum.SubscriptionExpirationReason.SubscriberRefunded:
    struct: EnumItem
  Enum.SubscriptionPaymentStatus.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SubscriptionPaymentStatus.Paid:
    struct: EnumItem
  Enum.SubscriptionPaymentStatus.Refunded:
    struct: EnumItem
  Enum.SubscriptionPeriod.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SubscriptionPeriod.Month:
    struct: EnumItem
  Enum.SubscriptionState.Expired:
    struct: EnumItem
  Enum.SubscriptionState.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SubscriptionState.NeverSubscribed:
    struct: EnumItem
  Enum.SubscriptionState.SubscribedRenewalPaymentPending:
    struct: EnumItem
  Enum.SubscriptionState.SubscribedWillNotRenew:
    struct: EnumItem
  Enum.SubscriptionState.SubscribedWillRenew:
    struct: EnumItem
  Enum.SurfaceConstraint.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SurfaceConstraint.Hinge:
    struct: EnumItem
  Enum.SurfaceConstraint.Motor:
    struct: EnumItem
  Enum.SurfaceConstraint.None:
    struct: EnumItem
  Enum.SurfaceConstraint.SteppingMotor:
    struct: EnumItem
  Enum.SurfaceGuiShape.CurvedHorizontally:
    struct: EnumItem
  Enum.SurfaceGuiShape.Flat:
    struct: EnumItem
  Enum.SurfaceGuiShape.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SurfaceGuiSizingMode.FixedSize:
    struct: EnumItem
  Enum.SurfaceGuiSizingMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SurfaceGuiSizingMode.PixelsPerStud:
    struct: EnumItem
  Enum.SurfaceType.Bumps:
    struct: EnumItem
    deprecated:
      message: Enum.SurfaceType.Bumps was replaced with Enum.SurfaceType.Glue
      replace:
      - Enum.SurfaceType.Glue
  Enum.SurfaceType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SurfaceType.Glue:
    struct: EnumItem
  Enum.SurfaceType.Hinge:
    struct: EnumItem
  Enum.SurfaceType.Inlet:
    struct: EnumItem
  Enum.SurfaceType.Motor:
    struct: EnumItem
  Enum.SurfaceType.Smooth:
    struct: EnumItem
  Enum.SurfaceType.SmoothNoOutlines:
    struct: EnumItem
  Enum.SurfaceType.Spawn:
    struct: EnumItem
    deprecated:
      message: Enum.SurfaceType.Spawn was replaced with Enum.SurfaceType.Smooth
      replace:
      - Enum.SurfaceType.Smooth
  Enum.SurfaceType.SteppingMotor:
    struct: EnumItem
  Enum.SurfaceType.Studs:
    struct: EnumItem
  Enum.SurfaceType.Universal:
    struct: EnumItem
  Enum.SurfaceType.Unjoinable:
    struct: EnumItem
    deprecated:
      message: Enum.SurfaceType.Unjoinable was replaced with Enum.SurfaceType.Smooth
      replace:
      - Enum.SurfaceType.Smooth
  Enum.SurfaceType.Weld:
    struct: EnumItem
  Enum.SwipeDirection.Down:
    struct: EnumItem
  Enum.SwipeDirection.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.SwipeDirection.Left:
    struct: EnumItem
  Enum.SwipeDirection.None:
    struct: EnumItem
  Enum.SwipeDirection.Right:
    struct: EnumItem
  Enum.SwipeDirection.Up:
    struct: EnumItem
  Enum.TableMajorAxis.ColumnMajor:
    struct: EnumItem
  Enum.TableMajorAxis.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TableMajorAxis.RowMajor:
    struct: EnumItem
  Enum.TeamCreateErrorState.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TeamCreateErrorState.NoError:
    struct: EnumItem
  Enum.TeamCreateErrorState.PlaceSizeApproachingLimit:
    struct: EnumItem
  Enum.TeamCreateErrorState.PlaceSizeTooLarge:
    struct: EnumItem
  Enum.Technology.Compatibility:
    struct: EnumItem
  Enum.Technology.Future:
    struct: EnumItem
  Enum.Technology.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.Technology.Legacy:
    struct: EnumItem
  Enum.Technology.ShadowMap:
    struct: EnumItem
  Enum.Technology.Unified:
    struct: EnumItem
  Enum.Technology.Voxel:
    struct: EnumItem
  Enum.TeleportMethod.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TeleportMethod.TeleportPartyAsync:
    struct: EnumItem
  Enum.TeleportMethod.TeleportToInstanceBack:
    struct: EnumItem
  Enum.TeleportMethod.TeleportToPlaceInstance:
    struct: EnumItem
  Enum.TeleportMethod.TeleportToPrivateServer:
    struct: EnumItem
  Enum.TeleportMethod.TeleportToSpawnByName:
    struct: EnumItem
  Enum.TeleportMethod.TeleportToVIPServer:
    struct: EnumItem
  Enum.TeleportMethod.TeleportUnknown:
    struct: EnumItem
  Enum.TeleportResult.Failure:
    struct: EnumItem
  Enum.TeleportResult.Flooded:
    struct: EnumItem
  Enum.TeleportResult.GameEnded:
    struct: EnumItem
  Enum.TeleportResult.GameFull:
    struct: EnumItem
  Enum.TeleportResult.GameNotFound:
    struct: EnumItem
  Enum.TeleportResult.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TeleportResult.IsTeleporting:
    struct: EnumItem
  Enum.TeleportResult.Success:
    struct: EnumItem
  Enum.TeleportResult.Unauthorized:
    struct: EnumItem
  Enum.TeleportState.Failed:
    struct: EnumItem
  Enum.TeleportState.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TeleportState.InProgress:
    struct: EnumItem
  Enum.TeleportState.RequestedFromServer:
    struct: EnumItem
  Enum.TeleportState.Started:
    struct: EnumItem
  Enum.TeleportState.WaitingForServer:
    struct: EnumItem
  Enum.TeleportType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TeleportType.ToInstance:
    struct: EnumItem
  Enum.TeleportType.ToInstanceBack:
    struct: EnumItem
  Enum.TeleportType.ToPlace:
    struct: EnumItem
  Enum.TeleportType.ToReservedServer:
    struct: EnumItem
  Enum.TeleportType.ToVIPServer:
    struct: EnumItem
  Enum.TerrainAcquisitionMethod.Convert:
    struct: EnumItem
  Enum.TerrainAcquisitionMethod.EditAddTool:
    struct: EnumItem
  Enum.TerrainAcquisitionMethod.EditReplaceTool:
    struct: EnumItem
  Enum.TerrainAcquisitionMethod.EditSeaLevelTool:
    struct: EnumItem
  Enum.TerrainAcquisitionMethod.Generate:
    struct: EnumItem
  Enum.TerrainAcquisitionMethod.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TerrainAcquisitionMethod.Import:
    struct: EnumItem
  Enum.TerrainAcquisitionMethod.Legacy:
    struct: EnumItem
  Enum.TerrainAcquisitionMethod.None:
    struct: EnumItem
  Enum.TerrainAcquisitionMethod.Other:
    struct: EnumItem
  Enum.TerrainAcquisitionMethod.RegionFillTool:
    struct: EnumItem
  Enum.TerrainAcquisitionMethod.RegionPasteTool:
    struct: EnumItem
  Enum.TerrainAcquisitionMethod.Template:
    struct: EnumItem
  Enum.TerrainFace.Bottom:
    struct: EnumItem
  Enum.TerrainFace.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TerrainFace.Side:
    struct: EnumItem
  Enum.TerrainFace.Top:
    struct: EnumItem
  Enum.TextChatMessageStatus.Floodchecked:
    struct: EnumItem
  Enum.TextChatMessageStatus.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TextChatMessageStatus.InvalidPrivacySettings:
    struct: EnumItem
  Enum.TextChatMessageStatus.InvalidTextChannelPermissions:
    struct: EnumItem
  Enum.TextChatMessageStatus.MessageTooLong:
    struct: EnumItem
  Enum.TextChatMessageStatus.ModerationTimeout:
    struct: EnumItem
  Enum.TextChatMessageStatus.Sending:
    struct: EnumItem
  Enum.TextChatMessageStatus.Success:
    struct: EnumItem
  Enum.TextChatMessageStatus.TextFilterFailed:
    struct: EnumItem
  Enum.TextChatMessageStatus.Unknown:
    struct: EnumItem
  Enum.TextDirection.Auto:
    struct: EnumItem
  Enum.TextDirection.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TextDirection.LeftToRight:
    struct: EnumItem
  Enum.TextDirection.RightToLeft:
    struct: EnumItem
  Enum.TextFilterContext.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TextFilterContext.PrivateChat:
    struct: EnumItem
  Enum.TextFilterContext.PublicChat:
    struct: EnumItem
  Enum.TextInputType.Default:
    struct: EnumItem
  Enum.TextInputType.Email:
    struct: EnumItem
  Enum.TextInputType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TextInputType.NoSuggestions:
    struct: EnumItem
  Enum.TextInputType.Number:
    struct: EnumItem
  Enum.TextInputType.OneTimePassword:
    struct: EnumItem
  Enum.TextInputType.Password:
    struct: EnumItem
  Enum.TextInputType.PasswordShown:
    struct: EnumItem
  Enum.TextInputType.Phone:
    struct: EnumItem
  Enum.TextInputType.Username:
    struct: EnumItem
  Enum.TextTruncate.AtEnd:
    struct: EnumItem
  Enum.TextTruncate.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TextTruncate.None:
    struct: EnumItem
  Enum.TextTruncate.SplitWord:
    struct: EnumItem
  Enum.TextXAlignment.Center:
    struct: EnumItem
  Enum.TextXAlignment.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TextXAlignment.Left:
    struct: EnumItem
  Enum.TextXAlignment.Right:
    struct: EnumItem
  Enum.TextYAlignment.Bottom:
    struct: EnumItem
  Enum.TextYAlignment.Center:
    struct: EnumItem
  Enum.TextYAlignment.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TextYAlignment.Top:
    struct: EnumItem
  Enum.TextureMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TextureMode.Static:
    struct: EnumItem
  Enum.TextureMode.Stretch:
    struct: EnumItem
  Enum.TextureMode.Wrap:
    struct: EnumItem
  Enum.TextureQueryType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TextureQueryType.Humanoid:
    struct: EnumItem
  Enum.TextureQueryType.HumanoidOrphaned:
    struct: EnumItem
  Enum.TextureQueryType.NonHumanoid:
    struct: EnumItem
  Enum.TextureQueryType.NonHumanoidOrphaned:
    struct: EnumItem
  Enum.ThreadPoolConfig.Auto:
    struct: EnumItem
  Enum.ThreadPoolConfig.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ThreadPoolConfig.PartialThread:
    struct: EnumItem
    deprecated:
      message: Enum.ThreadPoolConfig.PartialThread was replaced with Enum.ThreadPoolConfig.Auto
      replace:
      - Enum.ThreadPoolConfig.Auto
  Enum.ThreadPoolConfig.PerCore1:
    struct: EnumItem
  Enum.ThreadPoolConfig.PerCore2:
    struct: EnumItem
  Enum.ThreadPoolConfig.PerCore3:
    struct: EnumItem
  Enum.ThreadPoolConfig.PerCore4:
    struct: EnumItem
  Enum.ThreadPoolConfig.Threads1:
    struct: EnumItem
  Enum.ThreadPoolConfig.Threads16:
    struct: EnumItem
  Enum.ThreadPoolConfig.Threads2:
    struct: EnumItem
  Enum.ThreadPoolConfig.Threads3:
    struct: EnumItem
  Enum.ThreadPoolConfig.Threads4:
    struct: EnumItem
  Enum.ThreadPoolConfig.Threads8:
    struct: EnumItem
  Enum.ThrottlingPriority.Default:
    struct: EnumItem
  Enum.ThrottlingPriority.ElevatedOnServer:
    struct: EnumItem
  Enum.ThrottlingPriority.Extreme:
    struct: EnumItem
  Enum.ThrottlingPriority.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ThumbnailSize.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ThumbnailSize.Size100x100:
    struct: EnumItem
  Enum.ThumbnailSize.Size150x150:
    struct: EnumItem
  Enum.ThumbnailSize.Size180x180:
    struct: EnumItem
  Enum.ThumbnailSize.Size352x352:
    struct: EnumItem
  Enum.ThumbnailSize.Size420x420:
    struct: EnumItem
  Enum.ThumbnailSize.Size48x48:
    struct: EnumItem
  Enum.ThumbnailSize.Size60x60:
    struct: EnumItem
  Enum.ThumbnailType.AvatarBust:
    struct: EnumItem
  Enum.ThumbnailType.AvatarThumbnail:
    struct: EnumItem
  Enum.ThumbnailType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ThumbnailType.HeadShot:
    struct: EnumItem
  Enum.TickCountSampleMethod.Benchmark:
    struct: EnumItem
  Enum.TickCountSampleMethod.Fast:
    struct: EnumItem
  Enum.TickCountSampleMethod.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TickCountSampleMethod.Precise:
    struct: EnumItem
  Enum.TonemapperPreset.Default:
    struct: EnumItem
  Enum.TonemapperPreset.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TonemapperPreset.Retro:
    struct: EnumItem
  Enum.TopBottom.Bottom:
    struct: EnumItem
  Enum.TopBottom.Center:
    struct: EnumItem
  Enum.TopBottom.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TopBottom.Top:
    struct: EnumItem
  Enum.TouchCameraMovementMode.Classic:
    struct: EnumItem
  Enum.TouchCameraMovementMode.Default:
    struct: EnumItem
  Enum.TouchCameraMovementMode.Follow:
    struct: EnumItem
  Enum.TouchCameraMovementMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TouchCameraMovementMode.Orbital:
    struct: EnumItem
  Enum.TouchMovementMode.ClickToMove:
    struct: EnumItem
  Enum.TouchMovementMode.DPad:
    struct: EnumItem
  Enum.TouchMovementMode.Default:
    struct: EnumItem
  Enum.TouchMovementMode.DynamicThumbstick:
    struct: EnumItem
  Enum.TouchMovementMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TouchMovementMode.Thumbpad:
    struct: EnumItem
  Enum.TouchMovementMode.Thumbstick:
    struct: EnumItem
  Enum.TrackerError.AudioError:
    struct: EnumItem
  Enum.TrackerError.AudioNoPermission:
    struct: EnumItem
  Enum.TrackerError.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TrackerError.InitFailed:
    struct: EnumItem
  Enum.TrackerError.NoAudio:
    struct: EnumItem
  Enum.TrackerError.NoService:
    struct: EnumItem
  Enum.TrackerError.NoVideo:
    struct: EnumItem
  Enum.TrackerError.Ok:
    struct: EnumItem
  Enum.TrackerError.UnsupportedDevice:
    struct: EnumItem
  Enum.TrackerError.VideoError:
    struct: EnumItem
  Enum.TrackerError.VideoNoPermission:
    struct: EnumItem
  Enum.TrackerError.VideoUnsupported:
    struct: EnumItem
  Enum.TrackerExtrapolationFlagMode.Auto:
    struct: EnumItem
  Enum.TrackerExtrapolationFlagMode.ExtrapolateFacsAndPose:
    struct: EnumItem
  Enum.TrackerExtrapolationFlagMode.ExtrapolateFacsOnly:
    struct: EnumItem
  Enum.TrackerExtrapolationFlagMode.ForceDisabled:
    struct: EnumItem
  Enum.TrackerExtrapolationFlagMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TrackerFaceTrackingStatus.FaceTrackingHasTrackingError:
    struct: EnumItem
  Enum.TrackerFaceTrackingStatus.FaceTrackingIsOccluded:
    struct: EnumItem
  Enum.TrackerFaceTrackingStatus.FaceTrackingLost:
    struct: EnumItem
  Enum.TrackerFaceTrackingStatus.FaceTrackingNoFaceFound:
    struct: EnumItem
  Enum.TrackerFaceTrackingStatus.FaceTrackingSuccess:
    struct: EnumItem
  Enum.TrackerFaceTrackingStatus.FaceTrackingUninitialized:
    struct: EnumItem
  Enum.TrackerFaceTrackingStatus.FaceTrackingUnknown:
    struct: EnumItem
  Enum.TrackerFaceTrackingStatus.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TrackerLodFlagMode.Auto:
    struct: EnumItem
  Enum.TrackerLodFlagMode.ForceFalse:
    struct: EnumItem
  Enum.TrackerLodFlagMode.ForceTrue:
    struct: EnumItem
  Enum.TrackerLodFlagMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TrackerLodValueMode.Auto:
    struct: EnumItem
  Enum.TrackerLodValueMode.Force0:
    struct: EnumItem
  Enum.TrackerLodValueMode.Force1:
    struct: EnumItem
  Enum.TrackerLodValueMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TrackerMode.Audio:
    struct: EnumItem
  Enum.TrackerMode.AudioVideo:
    struct: EnumItem
  Enum.TrackerMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TrackerMode.None:
    struct: EnumItem
  Enum.TrackerMode.Video:
    struct: EnumItem
  Enum.TrackerPromptEvent.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TrackerPromptEvent.LODCameraRecommendDisable:
    struct: EnumItem
  Enum.TrackerType.Face:
    struct: EnumItem
  Enum.TrackerType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TrackerType.None:
    struct: EnumItem
  Enum.TrackerType.UpperBody:
    struct: EnumItem
  Enum.TriStateBoolean.False:
    struct: EnumItem
  Enum.TriStateBoolean.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.TriStateBoolean.True:
    struct: EnumItem
  Enum.TriStateBoolean.Unknown:
    struct: EnumItem
  Enum.TweenStatus.Canceled:
    struct: EnumItem
  Enum.TweenStatus.Completed:
    struct: EnumItem
  Enum.TweenStatus.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.UICaptureMode.All:
    struct: EnumItem
  Enum.UICaptureMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.UICaptureMode.None:
    struct: EnumItem
  Enum.UIDragDetectorBoundingBehavior.Automatic:
    struct: EnumItem
  Enum.UIDragDetectorBoundingBehavior.EntireObject:
    struct: EnumItem
  Enum.UIDragDetectorBoundingBehavior.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.UIDragDetectorBoundingBehavior.HitPoint:
    struct: EnumItem
  Enum.UIDragDetectorDragRelativity.Absolute:
    struct: EnumItem
  Enum.UIDragDetectorDragRelativity.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.UIDragDetectorDragRelativity.Relative:
    struct: EnumItem
  Enum.UIDragDetectorDragSpace.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.UIDragDetectorDragSpace.LayerCollector:
    struct: EnumItem
  Enum.UIDragDetectorDragSpace.Parent:
    struct: EnumItem
  Enum.UIDragDetectorDragSpace.Reference:
    struct: EnumItem
  Enum.UIDragDetectorDragStyle.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.UIDragDetectorDragStyle.Rotate:
    struct: EnumItem
  Enum.UIDragDetectorDragStyle.Scriptable:
    struct: EnumItem
  Enum.UIDragDetectorDragStyle.TranslateLine:
    struct: EnumItem
  Enum.UIDragDetectorDragStyle.TranslatePlane:
    struct: EnumItem
  Enum.UIDragDetectorResponseStyle.CustomOffset:
    struct: EnumItem
  Enum.UIDragDetectorResponseStyle.CustomScale:
    struct: EnumItem
  Enum.UIDragDetectorResponseStyle.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.UIDragDetectorResponseStyle.Offset:
    struct: EnumItem
  Enum.UIDragDetectorResponseStyle.Scale:
    struct: EnumItem
  Enum.UIDragSpeedAxisMapping.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.UIDragSpeedAxisMapping.XX:
    struct: EnumItem
  Enum.UIDragSpeedAxisMapping.XY:
    struct: EnumItem
  Enum.UIDragSpeedAxisMapping.YY:
    struct: EnumItem
  Enum.UIFlexAlignment.Fill:
    struct: EnumItem
  Enum.UIFlexAlignment.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.UIFlexAlignment.None:
    struct: EnumItem
  Enum.UIFlexAlignment.SpaceAround:
    struct: EnumItem
  Enum.UIFlexAlignment.SpaceBetween:
    struct: EnumItem
  Enum.UIFlexAlignment.SpaceEvenly:
    struct: EnumItem
  Enum.UIFlexMode.Custom:
    struct: EnumItem
  Enum.UIFlexMode.Fill:
    struct: EnumItem
  Enum.UIFlexMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.UIFlexMode.Grow:
    struct: EnumItem
  Enum.UIFlexMode.None:
    struct: EnumItem
  Enum.UIFlexMode.Shrink:
    struct: EnumItem
  Enum.UITheme.Dark:
    struct: EnumItem
  Enum.UITheme.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.UITheme.Light:
    struct: EnumItem
  Enum.UiMessageType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.UiMessageType.UiMessageError:
    struct: EnumItem
  Enum.UiMessageType.UiMessageInfo:
    struct: EnumItem
  Enum.UsageContext.Default:
    struct: EnumItem
  Enum.UsageContext.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.UsageContext.Preview:
    struct: EnumItem
  Enum.UserCFrame.Floor:
    struct: EnumItem
  Enum.UserCFrame.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.UserCFrame.Head:
    struct: EnumItem
  Enum.UserCFrame.LeftHand:
    struct: EnumItem
  Enum.UserCFrame.RightHand:
    struct: EnumItem
  Enum.UserInputState.Begin:
    struct: EnumItem
  Enum.UserInputState.Cancel:
    struct: EnumItem
  Enum.UserInputState.Change:
    struct: EnumItem
  Enum.UserInputState.End:
    struct: EnumItem
  Enum.UserInputState.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.UserInputState.None:
    struct: EnumItem
  Enum.UserInputType.Accelerometer:
    struct: EnumItem
  Enum.UserInputType.Focus:
    struct: EnumItem
  Enum.UserInputType.Gamepad1:
    struct: EnumItem
  Enum.UserInputType.Gamepad2:
    struct: EnumItem
  Enum.UserInputType.Gamepad3:
    struct: EnumItem
  Enum.UserInputType.Gamepad4:
    struct: EnumItem
  Enum.UserInputType.Gamepad5:
    struct: EnumItem
  Enum.UserInputType.Gamepad6:
    struct: EnumItem
  Enum.UserInputType.Gamepad7:
    struct: EnumItem
  Enum.UserInputType.Gamepad8:
    struct: EnumItem
  Enum.UserInputType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.UserInputType.Gyro:
    struct: EnumItem
  Enum.UserInputType.InputMethod:
    struct: EnumItem
  Enum.UserInputType.Keyboard:
    struct: EnumItem
  Enum.UserInputType.MouseButton1:
    struct: EnumItem
  Enum.UserInputType.MouseButton2:
    struct: EnumItem
  Enum.UserInputType.MouseButton3:
    struct: EnumItem
  Enum.UserInputType.MouseMovement:
    struct: EnumItem
  Enum.UserInputType.MouseWheel:
    struct: EnumItem
  Enum.UserInputType.None:
    struct: EnumItem
  Enum.UserInputType.TextInput:
    struct: EnumItem
  Enum.UserInputType.Touch:
    struct: EnumItem
  Enum.VRComfortSetting.Comfort:
    struct: EnumItem
  Enum.VRComfortSetting.Custom:
    struct: EnumItem
  Enum.VRComfortSetting.Expert:
    struct: EnumItem
  Enum.VRComfortSetting.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.VRComfortSetting.Normal:
    struct: EnumItem
  Enum.VRControllerModelMode.Disabled:
    struct: EnumItem
  Enum.VRControllerModelMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.VRControllerModelMode.Transparent:
    struct: EnumItem
  Enum.VRDeviceType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.VRDeviceType.HTCVive:
    struct: EnumItem
  Enum.VRDeviceType.OculusQuest:
    struct: EnumItem
  Enum.VRDeviceType.OculusRift:
    struct: EnumItem
  Enum.VRDeviceType.Unknown:
    struct: EnumItem
  Enum.VRDeviceType.ValveIndex:
    struct: EnumItem
  Enum.VRLaserPointerMode.Disabled:
    struct: EnumItem
  Enum.VRLaserPointerMode.DualPointer:
    struct: EnumItem
  Enum.VRLaserPointerMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.VRLaserPointerMode.Pointer:
    struct: EnumItem
  Enum.VRSafetyBubbleMode.Anyone:
    struct: EnumItem
  Enum.VRSafetyBubbleMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.VRSafetyBubbleMode.NoOne:
    struct: EnumItem
  Enum.VRSafetyBubbleMode.OnlyFriends:
    struct: EnumItem
  Enum.VRScaling.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.VRScaling.Off:
    struct: EnumItem
  Enum.VRScaling.World:
    struct: EnumItem
  Enum.VRSessionState.Focused:
    struct: EnumItem
  Enum.VRSessionState.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.VRSessionState.Idle:
    struct: EnumItem
  Enum.VRSessionState.Stopping:
    struct: EnumItem
  Enum.VRSessionState.Undefined:
    struct: EnumItem
  Enum.VRSessionState.Visible:
    struct: EnumItem
  Enum.VRTouchpad.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.VRTouchpad.Left:
    struct: EnumItem
  Enum.VRTouchpad.Right:
    struct: EnumItem
  Enum.VRTouchpadMode.ABXY:
    struct: EnumItem
  Enum.VRTouchpadMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.VRTouchpadMode.Touch:
    struct: EnumItem
  Enum.VRTouchpadMode.VirtualThumbstick:
    struct: EnumItem
  Enum.VelocityConstraintMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.VelocityConstraintMode.Line:
    struct: EnumItem
  Enum.VelocityConstraintMode.Plane:
    struct: EnumItem
  Enum.VelocityConstraintMode.Vector:
    struct: EnumItem
  Enum.VerticalAlignment.Bottom:
    struct: EnumItem
  Enum.VerticalAlignment.Center:
    struct: EnumItem
  Enum.VerticalAlignment.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.VerticalAlignment.Top:
    struct: EnumItem
  Enum.VerticalScrollBarPosition.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.VerticalScrollBarPosition.Left:
    struct: EnumItem
  Enum.VerticalScrollBarPosition.Right:
    struct: EnumItem
  Enum.VibrationMotor.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.VibrationMotor.Large:
    struct: EnumItem
  Enum.VibrationMotor.LeftHand:
    struct: EnumItem
  Enum.VibrationMotor.LeftTrigger:
    struct: EnumItem
  Enum.VibrationMotor.RightHand:
    struct: EnumItem
  Enum.VibrationMotor.RightTrigger:
    struct: EnumItem
  Enum.VibrationMotor.Small:
    struct: EnumItem
  Enum.VideoDeviceCaptureQuality.Default:
    struct: EnumItem
  Enum.VideoDeviceCaptureQuality.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.VideoDeviceCaptureQuality.High:
    struct: EnumItem
  Enum.VideoDeviceCaptureQuality.Low:
    struct: EnumItem
  Enum.VideoDeviceCaptureQuality.Medium:
    struct: EnumItem
  Enum.VideoError.AllocFailed:
    struct: EnumItem
  Enum.VideoError.BadParameter:
    struct: EnumItem
  Enum.VideoError.CodecCloseFailed:
    struct: EnumItem
  Enum.VideoError.CodecInitFailed:
    struct: EnumItem
  Enum.VideoError.CreateFailed:
    struct: EnumItem
  Enum.VideoError.DecodeFailed:
    struct: EnumItem
  Enum.VideoError.DownloadFailed:
    struct: EnumItem
  Enum.VideoError.EAgain:
    struct: EnumItem
  Enum.VideoError.EncodeFailed:
    struct: EnumItem
  Enum.VideoError.Eof:
    struct: EnumItem
  Enum.VideoError.Generic:
    struct: EnumItem
  Enum.VideoError.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.VideoError.NoPermission:
    struct: EnumItem
  Enum.VideoError.NoService:
    struct: EnumItem
  Enum.VideoError.Ok:
    struct: EnumItem
  Enum.VideoError.ParsingFailed:
    struct: EnumItem
  Enum.VideoError.ReleaseFailed:
    struct: EnumItem
  Enum.VideoError.StreamNotFound:
    struct: EnumItem
  Enum.VideoError.Unknown:
    struct: EnumItem
  Enum.VideoError.Unsupported:
    struct: EnumItem
  Enum.ViewMode.Decal:
    struct: EnumItem
  Enum.ViewMode.GeometryComplexity:
    struct: EnumItem
  Enum.ViewMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ViewMode.None:
    struct: EnumItem
  Enum.ViewMode.Transparent:
    struct: EnumItem
  Enum.VirtualCursorMode.Default:
    struct: EnumItem
  Enum.VirtualCursorMode.Disabled:
    struct: EnumItem
  Enum.VirtualCursorMode.Enabled:
    struct: EnumItem
  Enum.VirtualCursorMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.VirtualInputMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.VirtualInputMode.None:
    struct: EnumItem
  Enum.VirtualInputMode.Playing:
    struct: EnumItem
  Enum.VirtualInputMode.Recording:
    struct: EnumItem
  Enum.VoiceChatDistanceAttenuationType.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.VoiceChatDistanceAttenuationType.Inverse:
    struct: EnumItem
  Enum.VoiceChatDistanceAttenuationType.Legacy:
    struct: EnumItem
  Enum.VoiceChatState.Ended:
    struct: EnumItem
  Enum.VoiceChatState.Failed:
    struct: EnumItem
  Enum.VoiceChatState.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.VoiceChatState.Idle:
    struct: EnumItem
  Enum.VoiceChatState.Joined:
    struct: EnumItem
  Enum.VoiceChatState.Joining:
    struct: EnumItem
  Enum.VoiceChatState.JoiningRetry:
    struct: EnumItem
  Enum.VoiceChatState.Leaving:
    struct: EnumItem
  Enum.VoiceControlPath.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.VoiceControlPath.Join:
    struct: EnumItem
  Enum.VoiceControlPath.Publish:
    struct: EnumItem
  Enum.VoiceControlPath.Subscribe:
    struct: EnumItem
  Enum.VolumetricAudio.Automatic:
    struct: EnumItem
  Enum.VolumetricAudio.Disabled:
    struct: EnumItem
  Enum.VolumetricAudio.Enabled:
    struct: EnumItem
  Enum.VolumetricAudio.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.WaterDirection.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.WaterDirection.NegX:
    struct: EnumItem
  Enum.WaterDirection.NegY:
    struct: EnumItem
  Enum.WaterDirection.NegZ:
    struct: EnumItem
  Enum.WaterDirection.X:
    struct: EnumItem
  Enum.WaterDirection.Y:
    struct: EnumItem
  Enum.WaterDirection.Z:
    struct: EnumItem
  Enum.WaterForce.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.WaterForce.Max:
    struct: EnumItem
  Enum.WaterForce.Medium:
    struct: EnumItem
  Enum.WaterForce.None:
    struct: EnumItem
  Enum.WaterForce.Small:
    struct: EnumItem
  Enum.WaterForce.Strong:
    struct: EnumItem
  Enum.WebSocketState.Closed:
    struct: EnumItem
  Enum.WebSocketState.Closing:
    struct: EnumItem
  Enum.WebSocketState.Connecting:
    struct: EnumItem
  Enum.WebSocketState.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.WebSocketState.Open:
    struct: EnumItem
  Enum.WeldConstraintPreserve.All:
    struct: EnumItem
  Enum.WeldConstraintPreserve.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.WeldConstraintPreserve.None:
    struct: EnumItem
  Enum.WeldConstraintPreserve.Touching:
    struct: EnumItem
  Enum.WhisperChatPrivacyMode.AllUsers:
    struct: EnumItem
  Enum.WhisperChatPrivacyMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.WhisperChatPrivacyMode.NoOne:
    struct: EnumItem
  Enum.WrapLayerAutoSkin.Disabled:
    struct: EnumItem
  Enum.WrapLayerAutoSkin.EnabledOverride:
    struct: EnumItem
  Enum.WrapLayerAutoSkin.EnabledPreserve:
    struct: EnumItem
  Enum.WrapLayerAutoSkin.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.WrapLayerDebugMode.BoundCage:
    struct: EnumItem
  Enum.WrapLayerDebugMode.BoundCageAndLinks:
    struct: EnumItem
  Enum.WrapLayerDebugMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.WrapLayerDebugMode.HSRInner:
    struct: EnumItem
  Enum.WrapLayerDebugMode.HSRInnerReverse:
    struct: EnumItem
  Enum.WrapLayerDebugMode.HSROuter:
    struct: EnumItem
  Enum.WrapLayerDebugMode.HSROuterDetail:
    struct: EnumItem
  Enum.WrapLayerDebugMode.LayerCage:
    struct: EnumItem
  Enum.WrapLayerDebugMode.LayerCageFittedToBase:
    struct: EnumItem
  Enum.WrapLayerDebugMode.LayerCageFittedToPrev:
    struct: EnumItem
  Enum.WrapLayerDebugMode.None:
    struct: EnumItem
  Enum.WrapLayerDebugMode.OuterCage:
    struct: EnumItem
  Enum.WrapLayerDebugMode.Rbf:
    struct: EnumItem
  Enum.WrapLayerDebugMode.Reference:
    struct: EnumItem
  Enum.WrapLayerDebugMode.ReferenceMeshAfterMorph:
    struct: EnumItem
  Enum.WrapTargetDebugMode.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.WrapTargetDebugMode.None:
    struct: EnumItem
  Enum.WrapTargetDebugMode.OuterCageDetail:
    struct: EnumItem
  Enum.WrapTargetDebugMode.Rbf:
    struct: EnumItem
  Enum.WrapTargetDebugMode.TargetCageCompressed:
    struct: EnumItem
  Enum.WrapTargetDebugMode.TargetCageInterface:
    struct: EnumItem
  Enum.WrapTargetDebugMode.TargetCageOriginal:
    struct: EnumItem
  Enum.WrapTargetDebugMode.TargetLayerCageCompressed:
    struct: EnumItem
  Enum.WrapTargetDebugMode.TargetLayerCageOriginal:
    struct: EnumItem
  Enum.WrapTargetDebugMode.TargetLayerInterface:
    struct: EnumItem
  Enum.ZIndexBehavior.GetEnumItems:
    args: []
    method: true
    must_use: true
  Enum.ZIndexBehavior.Global:
    struct: EnumItem
  Enum.ZIndexBehavior.Sibling:
    struct: EnumItem
  Faces.new:
    args:
    - type: '...'
    must_use: true
  FloatCurveKey.new:
    args:
    - type: number
    - type: number
    - type:
        display: KeyInterpolationMode
    must_use: true
  Font.fromEnum:
    args:
    - type:
        display: Font
    must_use: true
  Font.fromId:
    args:
    - type: number
    - required: false
      type:
        display: FontWeight
    - required: false
      type:
        display: FontStyle
    must_use: true
  Font.fromName:
    args:
    - type: string
    - required: false
      type:
        display: FontWeight
    - required: false
      type:
        display: FontStyle
    must_use: true
  Font.new:
    args:
    - type: string
    - required: false
      type:
        display: FontWeight
    - required: false
      type:
        display: FontStyle
    must_use: true
  Instance.new:
    args:
    - type:
      - AccessoryDescription
      - Accoutrement
      - Accessory
      - Hat
      - AdPortal
      - AdvancedDragger
      - Animation
      - CurveAnimation
      - KeyframeSequence
      - AnimationController
      - AnimationRigData
      - Animator
      - Annotation
      - WorkspaceAnnotation
      - Atmosphere
      - Attachment
      - Bone
      - AudioAnalyzer
      - AudioChannelMixer
      - AudioChannelSplitter
      - AudioChorus
      - AudioCompressor
      - AudioDeviceInput
      - AudioDeviceOutput
      - AudioDistortion
      - AudioEcho
      - AudioEmitter
      - AudioEqualizer
      - AudioFader
      - AudioFilter
      - AudioFlanger
      - AudioLimiter
      - AudioListener
      - AudioPitchShifter
      - AudioPlayer
      - AudioReverb
      - AudioSearchParams
      - AudioTextToSpeech
      - Backpack
      - RemoteEvent
      - UnreliableRemoteEvent
      - WrapDeformer
      - WrapLayer
      - WrapTarget
      - Beam
      - BindableEvent
      - BindableFunction
      - BodyAngularVelocity
      - BodyForce
      - BodyGyro
      - BodyPosition
      - BodyThrust
      - BodyVelocity
      - RocketPropulsion
      - BodyPartDescription
      - Breakpoint
      - BodyColors
      - CharacterMesh
      - Pants
      - Shirt
      - ShirtGraphic
      - Skin
      - ClickDetector
      - DragDetector
      - Clouds
      - Configuration
      - AlignOrientation
      - AlignPosition
      - AngularVelocity
      - AnimationConstraint
      - BallSocketConstraint
      - HingeConstraint
      - LineForce
      - LinearVelocity
      - PlaneConstraint
      - Plane
      - RigidConstraint
      - RodConstraint
      - RopeConstraint
      - CylindricalConstraint
      - PrismaticConstraint
      - SpringConstraint
      - Torque
      - TorsionSpringConstraint
      - UniversalConstraint
      - VectorForce
      - HumanoidController
      - SkateboardController
      - VehicleController
      - AirController
      - ClimbController
      - GroundController
      - SwimController
      - ControllerManager
      - CustomEvent
      - CustomEventReceiver
      - CustomLog
      - BlockMesh
      - CylinderMesh
      - FileMesh
      - SpecialMesh
      - DataStoreGetOptions
      - DataStoreIncrementOptions
      - DataStoreOptions
      - DataStoreSetOptions
      - DebuggerWatch
      - Dialog
      - DialogChoice
      - Dragger
      - EulerRotationCurve
      - ExperienceInviteOptions
      - ExplorerFilter
      - Explosion
      - FaceControls
      - Decal
      - Texture
      - Hole
      - MotorFeature
      - Fire
      - FloatCurve
      - FlyweightService
      - CSGDictionaryService
      - NonReplicatedCSGDictionaryService
      - Folder
      - ForceField
      - FunctionalTest
      - GetTextBoundsParams
      - CanvasGroup
      - Frame
      - ImageButton
      - TextButton
      - ImageLabel
      - TextLabel
      - RelativeGui
      - ScrollingFrame
      - TextBox
      - VideoDisplay
      - VideoFrame
      - ViewportFrame
      - BillboardGui
      - ScreenGui
      - GuiMain
      - AdGui
      - SurfaceGui
      - FloorWire
      - SelectionBox
      - BoxHandleAdornment
      - ConeHandleAdornment
      - CylinderHandleAdornment
      - ImageHandleAdornment
      - LineHandleAdornment
      - SphereHandleAdornment
      - WireframeHandleAdornment
      - ParabolaAdornment
      - SelectionSphere
      - ArcHandles
      - Handles
      - SurfaceSelection
      - SelectionPartLasso
      - SelectionPointLasso
      - Path2D
      - HapticEffect
      - HeightmapImporterService
      - HiddenSurfaceRemovalAsset
      - Highlight
      - Humanoid
      - HumanoidDescription
      - HumanoidRigDescription
      - IKControl
      - InputAction
      - InputBinding
      - InputContext
      - RotateP
      - RotateV
      - Glue
      - ManualGlue
      - ManualWeld
      - Motor
      - Motor6D
      - Rotate
      - Snap
      - VelocityMotor
      - Weld
      - Keyframe
      - KeyframeMarker
      - PointLight
      - SpotLight
      - SurfaceLight
      - LocalizationTable
      - Script
      - LocalScript
      - ModuleScript
      - MarkerCurve
      - MaterialVariant
      - MemoryStoreService
      - Message
      - Hint
      - NoCollisionConstraint
      - Noise
      - OperationGraph
      - CornerWedgePart
      - Part
      - FlagStand
      - Seat
      - SkateboardPlatform
      - SpawnLocation
      - WedgePart
      - MeshPart
      - PartOperation
      - IntersectOperation
      - NegateOperation
      - UnionOperation
      - TrussPart
      - VehicleSeat
      - Camera
      - Model
      - Actor
      - HopperBin
      - Tool
      - Flag
      - WorldModel
      - PartOperationAsset
      - ParticleEmitter
      - PathfindingLink
      - PathfindingModifier
      - Player
      - PluginAction
      - PluginCapabilities
      - NumberPose
      - Pose
      - BloomEffect
      - BlurEffect
      - ColorCorrectionEffect
      - ColorGradingEffect
      - DepthOfFieldEffect
      - SunRaysEffect
      - ProximityPrompt
      - ProximityPromptService
      - RTAnimationTracker
      - ReflectionMetadata
      - ReflectionMetadataCallbacks
      - ReflectionMetadataClasses
      - ReflectionMetadataEnums
      - ReflectionMetadataEvents
      - ReflectionMetadataFunctions
      - ReflectionMetadataClass
      - ReflectionMetadataEnum
      - ReflectionMetadataEnumItem
      - ReflectionMetadataMember
      - ReflectionMetadataProperties
      - ReflectionMetadataYieldFunctions
      - RemoteFunction
      - RenderingTest
      - RotationCurve
      - AtmosphereSensor
      - BuoyancySensor
      - ControllerPartSensor
      - FluidForceSensor
      - Sky
      - Smoke
      - Sound
      - ChorusSoundEffect
      - CompressorSoundEffect
      - DistortionSoundEffect
      - EchoSoundEffect
      - EqualizerSoundEffect
      - FlangeSoundEffect
      - PitchShiftSoundEffect
      - ReverbSoundEffect
      - TremoloSoundEffect
      - SoundGroup
      - Sparkles
      - StandalonePluginScripts
      - StarterGear
      - StudioAttachment
      - StudioCallout
      - StyleRule
      - StyleSheet
      - StyleDerive
      - StyleLink
      - SurfaceAppearance
      - Team
      - TeleportOptions
      - TerrainDetail
      - TerrainRegion
      - TestService
      - TextChannel
      - TextChatCommand
      - TextChatMessageProperties
      - BubbleChatMessageProperties
      - TrackerStreamAnimation
      - Trail
      - Tween
      - UIAspectRatioConstraint
      - UISizeConstraint
      - UITextSizeConstraint
      - UICorner
      - UIDragDetector
      - UIFlexItem
      - UIGradient
      - UIGridLayout
      - UIListLayout
      - UIPageLayout
      - UITableLayout
      - UIPadding
      - UIScale
      - UIStroke
      - BinaryStringValue
      - BoolValue
      - BrickColorValue
      - CFrameValue
      - Color3Value
      - DoubleConstrainedValue
      - IntConstrainedValue
      - IntValue
      - NumberValue
      - ObjectValue
      - RayValue
      - StringValue
      - Vector3Value
      - Vector3Curve
      - VideoDeviceInput
      - VideoPlayer
      - VirtualInputManager
      - VisualizationMode
      - VisualizationModeCategory
      - WeldConstraint
      - Wire
    - required: false
      type:
        display: Instance
      deprecated:
        message: set the instance's parent separately
        replace: []
    must_use: true
  NumberRange.new:
    args:
    - type: number
    - required: false
      type: number
    must_use: true
  NumberSequence.new:
    args:
    - type: any
    - required: false
      type: number
    must_use: true
  NumberSequenceKeypoint.new:
    args:
    - type: number
    - type: number
    - required: false
      type: number
    must_use: true
  OverlapParams.new:
    args: []
    must_use: true
  Path2DControlPoint.new:
    args:
    - required: false
      type:
        display: UDim2
    - required: false
      type:
        display: UDim2
    - required: false
      type:
        display: UDim2
    must_use: true
  PathWaypoint.new:
    args:
    - required: false
      type:
        display: Vector3
    - required: false
      type:
        display: PathWaypointAction
    - required: false
      type: string
    must_use: true
  PhysicalProperties.new:
    args:
    - type: any
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    must_use: true
  Random.new:
    args:
    - required: false
      type: number
    must_use: true
  Ray.new:
    args:
    - type:
        display: Vector3
    - type:
        display: Vector3
    must_use: true
  RaycastParams.new:
    args: []
    must_use: true
  Rect.new:
    args:
    - type: any
    - type: any
    - required: false
      type: number
    - required: false
      type: number
    must_use: true
  Region3.new:
    args:
    - type:
        display: Vector3
    - type:
        display: Vector3
    must_use: true
  Region3int16.new:
    args:
    - required: false
      type:
        display: Vector3
    - required: false
      type:
        display: Vector3
    must_use: true
  RotationCurveKey.new:
    args:
    - type: number
    - type:
        display: CFrame
    - type:
        display: KeyInterpolationMode
    must_use: true
  SharedTable.clear:
    args:
    - type:
        display: SharedTable
  SharedTable.clone:
    args:
    - type:
        display: SharedTable
    - required: false
      type: bool
    must_use: true
  SharedTable.cloneAndFreeze:
    args:
    - type:
        display: SharedTable
    - required: false
      type: bool
    must_use: true
  SharedTable.increment:
    args:
    - type:
        display: SharedTable
    - type: any
    - type: number
  SharedTable.isFrozen:
    args:
    - type:
        display: SharedTable
    must_use: true
  SharedTable.new:
    args:
    - required: false
      type: table
    must_use: true
  SharedTable.size:
    args:
    - type:
        display: SharedTable
    must_use: true
  SharedTable.update:
    args:
    - type:
        display: SharedTable
    - type: any
    - type: function
  TweenInfo.new:
    args:
    - required: false
      type: number
    - required: false
      type:
        display: EasingStyle
    - required: false
      type:
        display: EasingDirection
    - required: false
      type: number
    - required: false
      type: bool
    - required: false
      type: number
    must_use: true
  UDim.new:
    args:
    - required: false
      type: number
    - required: false
      type: number
    must_use: true
  UDim2.fromOffset:
    args:
    - required: use UDim2.new() if you want an empty UDim2
      type: number
    - required: false
      type: number
    must_use: true
  UDim2.fromScale:
    args:
    - required: use UDim2.new() if you want an empty UDim2
      type: number
    - required: false
      type: number
    must_use: true
  UDim2.new:
    args:
    - required: false
      type: any
    - required: false
      type: any
    - required: false
      type: number
    - required: false
      type: number
    must_use: true
  UserSettings:
    args: []
    must_use: true
  Vector2.new:
    args:
    - required: false
      type: number
    - required: false
      type: number
    must_use: true
  Vector2.one:
    property: read-only
  Vector2.xAxis:
    property: read-only
  Vector2.yAxis:
    property: read-only
  Vector2.zero:
    property: read-only
  Vector2int16.new:
    args:
    - required: false
      type: number
    - required: false
      type: number
    must_use: true
  Vector3.FromAxis:
    args:
    - type:
        display: Axis
    must_use: true
  Vector3.FromNormalId:
    args:
    - type:
        display: NormalId
    must_use: true
  Vector3.new:
    args:
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
    must_use: true
  Vector3.one:
    property: read-only
  Vector3.xAxis:
    property: read-only
  Vector3.yAxis:
    property: read-only
  Vector3.zAxis:
    property: read-only
  Vector3.zero:
    property: read-only
  Vector3int16.new:
    args:
    - required: false
      type: number
    - required: false
      type: number
    - required: false
      type: number
  _G:
    property: new-fields
  _VERSION:
    property: read-only
  arg:
    property: new-fields
  assert:
    args:
    - type: any
    - required: A failed assertion without a message is unhelpful to users.
      type: string
  bit32.arshift:
    args:
    - type: number
    - type: number
    must_use: true
  bit32.band:
    args:
    - type: '...'
    must_use: true
  bit32.bnot:
    args:
    - type: number
    must_use: true
  bit32.bor:
    args:
    - type: '...'
    must_use: true
  bit32.btest:
    args:
    - type: '...'
    must_use: true
  bit32.bxor:
    args:
    - type: '...'
    must_use: true
  bit32.byteswap:
    args:
    - type: number
    must_use: true
  bit32.countlz:
    args:
    - type: number
    must_use: true
  bit32.countrz:
    args:
    - type: number
    must_use: true
  bit32.extract:
    args:
    - type: number
    - type: number
    - required: false
      type: number
    must_use: true
  bit32.lrotate:
    args:
    - type: number
    - type: number
    must_use: true
  bit32.lshift:
    args:
    - type: number
    - type: number
    must_use: true
  bit32.replace:
    args:
    - type: number
    - type: number
    - type: number
    - required: false
      type: number
    must_use: true
  bit32.rrotate:
    args:
    - type: number
    - type: number
    must_use: true
  bit32.rshift:
    args:
    - type: number
    - type: number
    must_use: true
  buffer.copy:
    args:
    - type:
        display: buffer
    - type: number
    - type:
        display: buffer
    - required: false
      type: number
    - required: false
      type: number
  buffer.create:
    args:
    - type: number
    must_use: true
  buffer.fill:
    args:
    - type:
        display: buffer
    - type: number
    - type: number
    - required: false
      type: number
  buffer.fromstring:
    args:
    - type: string
    must_use: true
  buffer.len:
    args:
    - type:
        display: buffer
    must_use: true
  buffer.readf32:
    args:
    - type:
        display: buffer
    - type: number
    must_use: true
  buffer.readf64:
    args:
    - type:
        display: buffer
    - type: number
    must_use: true
  buffer.readi16:
    args:
    - type:
        display: buffer
    - type: number
    must_use: true
  buffer.readi32:
    args:
    - type:
        display: buffer
    - type: number
    must_use: true
  buffer.readi8:
    args:
    - type:
        display: buffer
    - type: number
    must_use: true
  buffer.readstring:
    args:
    - type:
        display: buffer
    - type: number
    - type: number
    must_use: true
  buffer.readu16:
    args:
    - type:
        display: buffer
    - type: number
    must_use: true
  buffer.readu32:
    args:
    - type:
        display: buffer
    - type: number
    must_use: true
  buffer.readu8:
    args:
    - type:
        display: buffer
    - type: number
    must_use: true
  buffer.tostring:
    args:
    - type:
        display: buffer
    must_use: true
  buffer.writef32:
    args:
    - type:
        display: buffer
    - type: number
    - type: number
  buffer.writef64:
    args:
    - type:
        display: buffer
    - type: number
    - type: number
  buffer.writei16:
    args:
    - type:
        display: buffer
    - type: number
    - type: number
  buffer.writei32:
    args:
    - type:
        display: buffer
    - type: number
    - type: number
  buffer.writei8:
    args:
    - type:
        display: buffer
    - type: number
    - type: number
  buffer.writestring:
    args:
    - type:
        display: buffer
    - type: number
    - type: string
    - required: false
      type: number
  buffer.writeu16:
    args:
    - type:
        display: buffer
    - type: number
    - type: number
  buffer.writeu32:
    args:
    - type:
        display: buffer
    - type: number
    - type: number
  buffer.writeu8:
    args:
    - type:
        display: buffer
    - type: number
    - type: number
  collectgarbage:
    args:
    - type:
      - count
    must_use: true
  coroutine.close:
    args:
    - type:
        display: thread
  coroutine.create:
    args:
    - type: function
    must_use: true
  coroutine.isyieldable:
    args: []
  coroutine.resume:
    args:
    - type:
        display: coroutine
    - required: false
      type: '...'
  coroutine.running:
    args: []
    must_use: true
  coroutine.status:
    args:
    - type:
        display: coroutine
    must_use: true
  coroutine.wrap:
    args:
    - type: function
  coroutine.yield:
    args:
    - required: false
      type: '...'
  debug.info:
    args:
    - type: any
    - type: any
    - required: false
      type: string
    must_use: true
  debug.profilebegin:
    args:
    - type: string
  debug.profileend:
    args: []
  debug.resetmemorycategory:
    args: []
  debug.setmemorycategory:
    args:
    - type: string
  debug.traceback:
    args:
    - required: false
      type: any
    - required: false
      type: any
    - required: false
      type: any
    must_use: true
  delay:
    args:
    - type: number
    - type: function
  elapsedTime:
    args: []
  error:
    args:
    - required: Erroring without an explanation is unhelpful to users.
      type: any
    - required: false
      type: number
  game:
    struct: DataModel
  gcinfo:
    args: []
    must_use: true
  getfenv:
    args:
    - required: false
      type: any
    must_use: true
  getmetatable:
    args:
    - type: table
    must_use: true
  io.close:
    args:
    - required: false
      type:
        display: file
  io.flush:
    args: []
  io.input:
    args:
    - required: false
      type:
        display: file
  io.lines:
    args:
    - type: string
  io.open:
    args:
    - type: string
    - required: false
      type:
      - r
      - rb
      - w
      - wb
      - a
      - ab
      - r+
      - rb+
      - w+
      - wb+
      - a+
      - ab+
  io.output:
    args:
    - required: false
      type:
        display: file
  io.popen:
    args:
    - type: string
    - required: false
      type:
      - r
      - rb
      - w
      - wb
      - a
      - ab
      - r+
      - rb+
      - w+
      - wb+
      - a+
      - ab+
  io.read:
    args:
    - type: '...'
  io.stderr:
    property: read-only
  io.stdin:
    property: read-only
  io.stdout:
    property: read-only
  io.tmpfile:
    args: []
  io.type:
    args:
    - type:
        display: potentially file-like object
  io.write:
    args:
    - type: '...'
  ipairs:
    args:
    - type: table
    must_use: true
  loadstring:
    args:
    - type: string
    - required: false
      type: string
  math.abs:
    args:
    - type: number
    must_use: true
  math.acos:
    args:
    - type: number
    must_use: true
  math.asin:
    args:
    - type: number
    must_use: true
  math.atan:
    args:
    - type: number
    must_use: true
  math.atan2:
    args:
    - type: number
    - type: number
    must_use: true
  math.ceil:
    args:
    - type: number
    must_use: true
  math.clamp:
    args:
    - type: number
    - type: number
    - type: number
    must_use: true
  math.cos:
    args:
    - type: number
    must_use: true
  math.cosh:
    args:
    - type: number
    must_use: true
  math.deg:
    args:
    - type: number
    must_use: true
  math.exp:
    args:
    - type: number
    must_use: true
  math.floor:
    args:
    - type: number
    must_use: true
  math.fmod:
    args:
    - type: number
    - type: number
    must_use: true
  math.frexp:
    args:
    - type: number
    must_use: true
  math.huge:
    property: read-only
  math.ldexp:
    args:
    - type: number
    - type: number
    must_use: true
  math.log:
    args:
    - type: number
    - required: false
      type: number
    must_use: true
  math.log10:
    args:
    - type: number
    must_use: true
  math.map:
    args:
    - type: number
    - type: number
    - type: number
    - type: number
    - type: number
    must_use: true
  math.max:
    args:
    - type: number
    - required: use of max only makes sense with more than 1 parameter
      type: '...'
    must_use: true
  math.min:
    args:
    - type: number
    - required: use of min only makes sense with more than 1 parameter
      type: '...'
    must_use: true
  math.modf:
    args:
    - type: number
    must_use: true
  math.noise:
    args:
    - type: number
    - required: false
      type: number
    - required: false
      type: number
    must_use: true
  math.pi:
    property: read-only
  math.pow:
    args:
    - type: number
    - type: number
    must_use: true
  math.rad:
    args:
    - type: number
    must_use: true
  math.random:
    args:
    - required: false
      type: number
    - required: false
      type: number
    must_use: true
  math.randomseed:
    args:
    - type: number
  math.round:
    args:
    - type: number
    must_use: true
  math.sign:
    args:
    - type: number
    must_use: true
  math.sin:
    args:
    - type: number
    must_use: true
  math.sinh:
    args:
    - type: number
    must_use: true
  math.sqrt:
    args:
    - type: number
    must_use: true
  math.tan:
    args:
    - type: number
    must_use: true
  math.tanh:
    args:
    - type: number
    must_use: true
  newproxy:
    args:
    - required: false
      type: bool
    must_use: true
  next:
    args:
    - type: table
    - required: false
      type: number
  os.clock:
    args: []
    must_use: true
  os.date:
    args:
    - required: false
      type: string
    - required: false
      type: number
    must_use: true
  os.difftime:
    args:
    - type: number
    - type: number
    must_use: true
  os.time:
    args:
    - required: false
      type: table
    must_use: true
  package.cpath:
    property: full-write
  package.loaded:
    property: new-fields
  package.loaders:
    property: new-fields
  package.loadlib:
    args:
    - type: string
    - type: string
  package.path:
    property: full-write
  package.preload:
    property: new-fields
  package.seeall:
    args:
    - type: table
  pairs:
    args:
    - type: table
    must_use: true
  pcall:
    args:
    - type: function
    - required: false
      type: '...'
  plugin:
    struct: Plugin
  print:
    args:
    - required: false
      type: '...'
  rawequal:
    args:
    - type: any
    - type: any
    must_use: true
  rawget:
    args:
    - type: any
    - type: any
    must_use: true
  rawlen:
    args:
    - type: table
    must_use: true
  rawset:
    args:
    - type: any
    - type: any
    - type: any
  require:
    args:
    - type: number
  script:
    struct: Script
  select:
    args:
    - type: any
    - type: '...'
    must_use: true
  setfenv:
    args:
    - type: any
    - type: table
  setmetatable:
    args:
    - type: table
    - required: false
      type: table
  settings:
    args: []
  shared:
    property: new-fields
  spawn:
    args:
    - type: function
  string.byte:
    args:
    - type: string
    - required: false
      type: number
    - required: false
      type: number
  string.char:
    args:
    - required: string.char should be used with an argument despite it not throwing
      type: number
    - required: false
      type: '...'
    must_use: true
  string.find:
    args:
    - type: string
    - type: string
    - required: false
      type: number
    - required: false
      type: bool
    must_use: true
  string.format:
    args:
    - type: string
    - required: string.format should only be used for strings that need formatting
      type: '...'
    must_use: true
  string.gmatch:
    args:
    - type: string
    - type: string
    must_use: true
  string.gsub:
    args:
    - type: string
    - type: string
    - type: any
    - required: false
      type: number
    must_use: true
  string.len:
    args:
    - type: string
    must_use: true
  string.lower:
    args:
    - type: string
    must_use: true
  string.match:
    args:
    - type: string
    - type: string
    - required: false
      type: number
    must_use: true
  string.pack:
    args:
    - type: string
    - type: '...'
    must_use: true
  string.packsize:
    args:
    - type: string
    must_use: true
  string.rep:
    args:
    - type: string
    - type: number
    must_use: true
  string.reverse:
    args:
    - type: string
    must_use: true
  string.split:
    args:
    - type: string
    - required: false
      type: string
    must_use: true
  string.sub:
    args:
    - type: string
    - type: number
    - required: false
      type: number
    must_use: true
  string.unpack:
    args:
    - type: string
    - type: string
    - required: false
      type: number
    must_use: true
  string.upper:
    args:
    - type: string
    must_use: true
  table.clear:
    args:
    - type: table
  table.clone:
    args:
    - type: table
    must_use: true
  table.concat:
    args:
    - type: table
    - required: false
      type: string
    - required: false
      type: number
    - required: false
      type: number
    must_use: true
  table.create:
    args:
    - type: number
    - required: false
      type: any
    must_use: true
  table.find:
    args:
    - type: table
    - type: any
    - required: false
      type: number
    must_use: true
  table.foreach:
    args:
    - type: table
    - type: function
    deprecated:
      message: use a for loop instead.
      replace: []
  table.foreachi:
    args:
    - type: table
    - type: function
    deprecated:
      message: use a for loop instead.
      replace: []
  table.freeze:
    args:
    - type: table
  table.getn:
    args:
    - type: table
    - type: number
    must_use: true
    deprecated:
      message: '`table.getn` has been superseded by #.'
      replace:
      - '#%1'
  table.insert:
    args:
    - type: table
      observes: write
    - type: any
    - required: false
      type: any
  table.isfrozen:
    args:
    - type: table
    must_use: true
  table.maxn:
    args:
    - type: table
    must_use: true
  table.move:
    args:
    - type: table
    - type: number
    - type: number
    - type: number
    - required: false
      type: table
  table.pack:
    args:
    - type: '...'
    must_use: true
  table.remove:
    args:
    - type: table
    - required: false
      type: number
  table.sort:
    args:
    - type: table
    - required: false
      type: function
  table.unpack:
    args:
    - type: table
    - required: false
      type: number
    - required: false
      type: number
    must_use: true
  task.cancel:
    args:
    - type:
        display: thread
  task.defer:
    args:
    - type: function
    - required: false
      type: '...'
  task.delay:
    args:
    - required: false
      type: number
    - type: function
    - required: false
      type: '...'
  task.desynchronize:
    args: []
  task.spawn:
    args:
    - type: function
    - required: false
      type: '...'
  task.synchronize:
    args: []
  task.wait:
    args:
    - required: false
      type: number
  tick:
    args: []
  time:
    args: []
  tonumber:
    args:
    - type: any
    - required: false
      type: number
    must_use: true
  tostring:
    args:
    - type: any
    must_use: true
  type:
    args:
    - type: any
  typeof:
    args:
    - type: any
  unpack:
    args:
    - type: table
    - required: false
      type: number
    - required: false
      type: number
    must_use: true
  utf8.char:
    args:
    - required: utf8.char should be used with an argument despite it not throwing
      type: number
    - required: false
      type: '...'
    must_use: true
  utf8.charpattern:
    property: read-only
  utf8.codepoint:
    args:
    - type: string
    - required: false
      type: number
    - required: false
      type: number
    must_use: true
  utf8.codes:
    args:
    - type: string
    must_use: true
  utf8.graphemes:
    args:
    - type: string
    - required: false
      type: number
    - required: false
      type: number
    must_use: true
  utf8.len:
    args:
    - type: string
    - required: false
      type: number
    - required: false
      type: number
    must_use: true
  utf8.nfcnormalize:
    args:
    - type: string
    must_use: true
  utf8.nfdnormalize:
    args:
    - type: string
    must_use: true
  utf8.offset:
    args:
    - type: string
    - required: false
      type: number
    - required: false
      type: number
    must_use: true
  vector.abs:
    args:
    - type:
        display: vector
    must_use: true
  vector.angle:
    args:
    - type:
        display: vector
    - type:
        display: vector
    - required: false
      type:
        display: vector
    must_use: true
  vector.ceil:
    args:
    - type:
        display: vector
    must_use: true
  vector.clamp:
    args:
    - type:
        display: vector
    - type:
        display: vector
    - type:
        display: vector
    must_use: true
  vector.create:
    args:
    - type: number
    - type: number
    - type: number
    must_use: true
  vector.cross:
    args:
    - type:
        display: vector
    - type:
        display: vector
    must_use: true
  vector.dot:
    args:
    - type:
        display: vector
    - type:
        display: vector
    must_use: true
  vector.floor:
    args:
    - type:
        display: vector
    must_use: true
  vector.magnitude:
    args:
    - type:
        display: vector
    must_use: true
  vector.max:
    args:
    - type:
        display: vector
    - required: false
      type: '...'
    must_use: true
  vector.min:
    args:
    - type:
        display: vector
    - required: false
      type: '...'
    must_use: true
  vector.normalize:
    args:
    - type:
        display: vector
    must_use: true
  vector.one:
    property: read-only
  vector.sign:
    args:
    - type:
        display: vector
    must_use: true
  vector.zero:
    property: read-only
  wait:
    args:
    - required: false
      type: number
  warn:
    args:
    - type: any
    - required: false
      type: '...'
  workspace:
    struct: Workspace
  xpcall:
    args:
    - type: function
    - required: false
      type: '...'
structs:
  BasePart:
    '*':
      struct: Instance
    AddTag:
      args:
      - required: false
        type: any
      method: true
    AncestryChanged:
      struct: Event
    Anchored:
      property: override-fields
    AngularAccelerationToTorque:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    ApplyAngularImpulse:
      args:
      - required: false
        type: any
      method: true
    ApplyImpulse:
      args:
      - required: false
        type: any
      method: true
    ApplyImpulseAtPosition:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    Archivable:
      property: override-fields
    AssemblyAngularVelocity:
      any: true
    AssemblyCenterOfMass:
      any: true
    AssemblyLinearVelocity:
      any: true
    AssemblyMass:
      property: read-only
    AssemblyRootPart:
      struct: BasePart
    AttributeChanged:
      struct: Event
    AudioCanCollide:
      property: override-fields
    BackParamA:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    BackParamB:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    BackSurface:
      property: override-fields
    BackSurfaceInput:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    BottomParamA:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    BottomParamB:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    BottomSurface:
      property: override-fields
    BottomSurfaceInput:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    BreakJoints:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    BrickColor:
      property: override-fields
    CFrame:
      any: true
    CanCollide:
      property: override-fields
    CanCollideWith:
      args:
      - required: false
        type: any
      method: true
    CanQuery:
      property: override-fields
    CanSetNetworkOwnership:
      args: []
      method: true
    CanTouch:
      property: override-fields
    CastShadow:
      property: override-fields
    CenterOfMass:
      any: true
    Changed:
      struct: Event
    ChildAdded:
      struct: Event
    ChildRemoved:
      struct: Event
    ClassName:
      property: read-only
    ClearAllChildren:
      args: []
      method: true
    Clone:
      args: []
      method: true
    CollisionGroup:
      property: override-fields
    CollisionGroupId:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    Color:
      property: override-fields
    CurrentPhysicalProperties:
      property: read-only
    CustomPhysicalProperties:
      property: override-fields
    DescendantAdded:
      struct: Event
    DescendantRemoving:
      struct: Event
    Destroy:
      args: []
      method: true
    Destroying:
      struct: Event
    Elasticity:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    EnableFluidForces:
      property: override-fields
    ExtentsCFrame:
      any: true
    ExtentsSize:
      any: true
    FindFirstAncestor:
      args:
      - required: false
        type: any
      method: true
    FindFirstAncestorOfClass:
      args:
      - required: false
        type: any
      method: true
    FindFirstAncestorWhichIsA:
      args:
      - required: false
        type: any
      method: true
    FindFirstChild:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    FindFirstChildOfClass:
      args:
      - required: false
        type: any
      method: true
    FindFirstChildWhichIsA:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    FindFirstDescendant:
      args:
      - required: false
        type: any
      method: true
    Friction:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    FrontParamA:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    FrontParamB:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    FrontSurface:
      property: override-fields
    FrontSurfaceInput:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    GetActor:
      args: []
      method: true
    GetAttribute:
      args:
      - required: false
        type: any
      method: true
    GetAttributeChangedSignal:
      args:
      - required: false
        type: any
      method: true
    GetAttributes:
      args: []
      method: true
    GetChildren:
      args: []
      method: true
    GetClosestPointOnSurface:
      args:
      - required: false
        type: any
      method: true
    GetConnectedParts:
      args:
      - required: false
        type: any
      method: true
    GetDebugId:
      args:
      - required: false
        type: any
      method: true
    GetDescendants:
      args: []
      method: true
    GetFullName:
      args: []
      method: true
    GetJoints:
      args: []
      method: true
    GetMass:
      args: []
      method: true
    GetNetworkOwner:
      args: []
      method: true
    GetNetworkOwnershipAuto:
      args: []
      method: true
    GetNoCollisionConstraints:
      args: []
      method: true
    GetPivot:
      args: []
      method: true
    GetPropertyChangedSignal:
      args:
      - required: false
        type: any
      method: true
    GetRenderCFrame:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    GetRootPart:
      args: []
      method: true
    GetStyled:
      args:
      - required: false
        type: any
      method: true
    GetStyledPropertyChangedSignal:
      args:
      - required: false
        type: any
      method: true
    GetTags:
      args: []
      method: true
    GetTouchingParts:
      args: []
      method: true
    GetVelocityAtPosition:
      args:
      - required: false
        type: any
      method: true
    HasTag:
      args:
      - required: false
        type: any
      method: true
    IntersectAsync:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    IsA:
      args:
      - required: false
        type: any
      method: true
    IsAncestorOf:
      args:
      - required: false
        type: any
      method: true
    IsDescendantOf:
      args:
      - required: false
        type: any
      method: true
    IsGrounded:
      args: []
      method: true
    IsPropertyModified:
      args:
      - required: false
        type: any
      method: true
    LeftParamA:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    LeftParamB:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    LeftSurface:
      property: override-fields
    LeftSurfaceInput:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    LocalSimulationTouched:
      struct: Event
      deprecated:
        message: this property is deprecated.
        replace: []
    LocalTransparencyModifier:
      property: override-fields
    Locked:
      property: override-fields
    MakeJoints:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    Mass:
      property: read-only
    Massless:
      property: override-fields
    Material:
      property: override-fields
    MaterialVariant:
      property: override-fields
    Name:
      property: override-fields
    Orientation:
      any: true
    Origin:
      any: true
    OutfitChanged:
      struct: Event
      deprecated:
        message: this property is deprecated.
        replace: []
    Parent:
      struct: Instance
    Pivot Offset:
      any: true
    PivotOffset:
      any: true
    PivotTo:
      args:
      - required: false
        type: any
      method: true
    Position:
      any: true
    ReceiveAge:
      property: read-only
    Reflectance:
      property: override-fields
    Remove:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    RemoveTag:
      args:
      - required: false
        type: any
      method: true
    ResetPropertyToDefault:
      args:
      - required: false
        type: any
      method: true
    Resize:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    ResizeIncrement:
      property: read-only
    ResizeableFaces:
      property: read-only
    RightParamA:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    RightParamB:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    RightSurface:
      property: override-fields
    RightSurfaceInput:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    RootPriority:
      property: override-fields
    RotVelocity:
      any: true
      deprecated:
        message: this property is deprecated.
        replace: []
    Rotation:
      any: true
    SetAttribute:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    SetNetworkOwner:
      args:
      - required: false
        type: any
      method: true
    SetNetworkOwnershipAuto:
      args: []
      method: true
    Size:
      any: true
    SpecificGravity:
      property: read-only
      deprecated:
        message: this property is deprecated.
        replace: []
    StoppedTouching:
      struct: Event
      deprecated:
        message: this property is deprecated.
        replace: []
    StyledPropertiesChanged:
      struct: Event
    SubtractAsync:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    TopParamA:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    TopParamB:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    TopSurface:
      property: override-fields
    TopSurfaceInput:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    TorqueToAngularAcceleration:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    TouchEnded:
      struct: Event
    Touched:
      struct: Event
    Transparency:
      property: override-fields
    UnionAsync:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    Velocity:
      any: true
      deprecated:
        message: this property is deprecated.
        replace: []
    WaitForChild:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    archivable:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    breakJoints:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    brickColor:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    childAdded:
      struct: Event
      deprecated:
        message: this property is deprecated.
        replace: []
    children:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    className:
      property: read-only
      deprecated:
        message: this property is deprecated.
        replace: []
    clone:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    destroy:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    findFirstChild:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    getChildren:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    getMass:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    isA:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    isDescendantOf:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    makeJoints:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    remove:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    resize:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
  Camera:
    '*':
      struct: Instance
    AddTag:
      args:
      - required: false
        type: any
      method: true
    AncestryChanged:
      struct: Event
    Archivable:
      property: override-fields
    AttributeChanged:
      struct: Event
    CFrame:
      any: true
    CameraSubject:
      struct: Instance
    CameraType:
      property: override-fields
    Changed:
      struct: Event
    ChildAdded:
      struct: Event
    ChildRemoved:
      struct: Event
    ClassName:
      property: read-only
    ClearAllChildren:
      args: []
      method: true
    Clone:
      args: []
      method: true
    CoordinateFrame:
      any: true
      deprecated:
        message: this property is deprecated.
        replace: []
    DescendantAdded:
      struct: Event
    DescendantRemoving:
      struct: Event
    Destroy:
      args: []
      method: true
    Destroying:
      struct: Event
    DiagonalFieldOfView:
      property: override-fields
    FieldOfView:
      property: override-fields
    FieldOfViewMode:
      property: override-fields
    FindFirstAncestor:
      args:
      - required: false
        type: any
      method: true
    FindFirstAncestorOfClass:
      args:
      - required: false
        type: any
      method: true
    FindFirstAncestorWhichIsA:
      args:
      - required: false
        type: any
      method: true
    FindFirstChild:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    FindFirstChildOfClass:
      args:
      - required: false
        type: any
      method: true
    FindFirstChildWhichIsA:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    FindFirstDescendant:
      args:
      - required: false
        type: any
      method: true
    FirstPersonTransition:
      struct: Event
    Focus:
      any: true
    GetActor:
      args: []
      method: true
    GetAttribute:
      args:
      - required: false
        type: any
      method: true
    GetAttributeChangedSignal:
      args:
      - required: false
        type: any
      method: true
    GetAttributes:
      args: []
      method: true
    GetChildren:
      args: []
      method: true
    GetDebugId:
      args:
      - required: false
        type: any
      method: true
    GetDescendants:
      args: []
      method: true
    GetFullName:
      args: []
      method: true
    GetLargestCutoffDistance:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    GetPanSpeed:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    GetPartsObscuringTarget:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    GetPivot:
      args: []
      method: true
    GetPropertyChangedSignal:
      args:
      - required: false
        type: any
      method: true
    GetRenderCFrame:
      args: []
      method: true
    GetRoll:
      args: []
      method: true
    GetStyled:
      args:
      - required: false
        type: any
      method: true
    GetStyledPropertyChangedSignal:
      args:
      - required: false
        type: any
      method: true
    GetTags:
      args: []
      method: true
    GetTiltSpeed:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    HasTag:
      args:
      - required: false
        type: any
      method: true
    HeadLocked:
      property: override-fields
    HeadScale:
      property: override-fields
    Interpolate:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    InterpolationFinished:
      struct: Event
    IsA:
      args:
      - required: false
        type: any
      method: true
    IsAncestorOf:
      args:
      - required: false
        type: any
      method: true
    IsDescendantOf:
      args:
      - required: false
        type: any
      method: true
    IsPropertyModified:
      args:
      - required: false
        type: any
      method: true
    MaxAxisFieldOfView:
      property: override-fields
    Name:
      property: override-fields
    NearPlaneZ:
      property: read-only
    Origin:
      any: true
    PanUnits:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    Parent:
      struct: Instance
    Pivot Offset:
      any: true
    PivotTo:
      args:
      - required: false
        type: any
      method: true
    Remove:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    RemoveTag:
      args:
      - required: false
        type: any
      method: true
    ResetPropertyToDefault:
      args:
      - required: false
        type: any
      method: true
    ScreenPointToRay:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    SetAttribute:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    SetCameraPanMode:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    SetImageServerView:
      args:
      - required: false
        type: any
      method: true
    SetRoll:
      args:
      - required: false
        type: any
      method: true
    StyledPropertiesChanged:
      struct: Event
    TiltUnits:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    VRTiltAndRollEnabled:
      property: override-fields
    ViewportPointToRay:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    ViewportSize:
      any: true
    WaitForChild:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    WorldToScreenPoint:
      args:
      - required: false
        type: any
      method: true
    WorldToViewportPoint:
      args:
      - required: false
        type: any
      method: true
    Zoom:
      args:
      - required: false
        type: any
      method: true
    ZoomToExtents:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    archivable:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    childAdded:
      struct: Event
      deprecated:
        message: this property is deprecated.
        replace: []
    children:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    className:
      property: read-only
      deprecated:
        message: this property is deprecated.
        replace: []
    clone:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    destroy:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    findFirstChild:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    focus:
      any: true
      deprecated:
        message: this property is deprecated.
        replace: []
    getChildren:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    isA:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    isDescendantOf:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    remove:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
  DataModel:
    '*':
      struct: Instance
    AddTag:
      args:
      - required: false
        type: any
      method: true
    AllowedGearTypeChanged:
      struct: Event
      deprecated:
        message: this property is deprecated.
        replace: []
    AncestryChanged:
      struct: Event
    Archivable:
      property: override-fields
    AttributeChanged:
      struct: Event
    BindToClose:
      args:
      - required: false
        type: any
      method: true
    Changed:
      struct: Event
    ChildAdded:
      struct: Event
    ChildRemoved:
      struct: Event
    ClassName:
      property: read-only
    ClearAllChildren:
      args: []
      method: true
    Clone:
      args: []
      method: true
    Close:
      struct: Event
    CloseLate:
      struct: Event
    CreatorId:
      property: read-only
    CreatorType:
      property: read-only
    DefineFastFlag:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    DefineFastInt:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    DefineFastString:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    DescendantAdded:
      struct: Event
    DescendantRemoving:
      struct: Event
    Destroy:
      args: []
      method: true
    Destroying:
      struct: Event
    FindFirstAncestor:
      args:
      - required: false
        type: any
      method: true
    FindFirstAncestorOfClass:
      args:
      - required: false
        type: any
      method: true
    FindFirstAncestorWhichIsA:
      args:
      - required: false
        type: any
      method: true
    FindFirstChild:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    FindFirstChildOfClass:
      args:
      - required: false
        type: any
      method: true
    FindFirstChildWhichIsA:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    FindFirstDescendant:
      args:
      - required: false
        type: any
      method: true
    FindService:
      args:
      - required: false
        type: any
      method: true
    GameId:
      property: read-only
    GearGenreSetting:
      property: read-only
      deprecated:
        message: this property is deprecated.
        replace: []
    Genre:
      property: read-only
    GetActor:
      args: []
      method: true
    GetAttribute:
      args:
      - required: false
        type: any
      method: true
    GetAttributeChangedSignal:
      args:
      - required: false
        type: any
      method: true
    GetAttributes:
      args: []
      method: true
    GetChildren:
      args: []
      method: true
    GetDebugId:
      args:
      - required: false
        type: any
      method: true
    GetDescendants:
      args: []
      method: true
    GetEngineFeature:
      args:
      - required: false
        type: any
      method: true
    GetFastFlag:
      args:
      - required: false
        type: any
      method: true
    GetFastInt:
      args:
      - required: false
        type: any
      method: true
    GetFastString:
      args:
      - required: false
        type: any
      method: true
    GetFullName:
      args: []
      method: true
    GetJobsInfo:
      args: []
      method: true
    GetMessage:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    GetObjects:
      args:
      - required: false
        type: any
      method: true
    GetObjectsAllOrNone:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    GetObjectsAsync:
      args:
      - required: false
        type: any
      method: true
    GetObjectsList:
      args:
      - required: false
        type: any
      method: true
    GetPlaySessionId:
      args: []
      method: true
    GetPropertyChangedSignal:
      args:
      - required: false
        type: any
      method: true
    GetRemoteBuildMode:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    GetService:
      args:
      - type:
        - AccountService
        - AchievementService
        - ActivityHistoryEventService
        - AdService
        - AnalyticsService
        - AnimationClipProvider
        - AnimationFromVideoCreatorService
        - AnimationFromVideoCreatorStudioService
        - AnnotationsService
        - AppLifecycleObserverService
        - AppUpdateService
        - AssetCounterService
        - AssetDeliveryProxy
        - AssetImportService
        - AssetManagerService
        - AssetService
        - AudioFocusService
        - AvatarChatService
        - AvatarCreationService
        - AvatarEditorService
        - AvatarImportService
        - AvatarPreloader
        - BadgeService
        - CoreGui
        - StarterGui
        - BrowserService
        - BugReporterService
        - BulkImportService
        - CacheableContentProvider
        - HSRDataContentProvider
        - MeshContentProvider
        - SolidModelContentProvider
        - CalloutService
        - CaptureService
        - ChangeHistoryService
        - Chat
        - ChatbotUIService
        - CloudCRUDService
        - ClusterPacketCache
        - CollaboratorsService
        - CollectionService
        - CommandService
        - CommerceService
        - ConfigService
        - ConfigureServerService
        - ConnectivityService
        - ContentProvider
        - ContextActionService
        - ControllerService
        - ConversationalAIAcceptanceService
        - CookiesService
        - CorePackages
        - CoreScriptDebuggingManagerHelper
        - CoreScriptSyncService
        - CreationDBService
        - CreatorStoreService
        - CrossDMScriptChangeListener
        - DataModelPatchService
        - DataStoreService
        - Debris
        - DebuggablePluginWatcher
        - DebuggerConnectionManager
        - DebuggerManager
        - DebuggerUIService
        - DeviceIdService
        - DraftsService
        - DraggerService
        - EditableService
        - EventIngestService
        - ExampleService
        - ExperienceAuthService
        - ExperienceNotificationService
        - ExperienceService
        - ExperienceStateCaptureService
        - ExplorerServiceVisibilityService
        - FaceAnimatorService
        - FacialAnimationRecordingService
        - FacialAnimationStreamingServiceV2
        - FeatureRestrictionManager
        - FlagStandService
        - FlyweightService
        - CSGDictionaryService
        - NonReplicatedCSGDictionaryService
        - FriendService
        - GamePassService
        - GamepadService
        - GenerationService
        - GenericChallengeService
        - Geometry
        - GeometryService
        - GoogleAnalyticsConfiguration
        - GroupService
        - GuiService
        - GuidRegistryService
        - HapticService
        - HeapProfilerService
        - HeatmapService
        - HeightmapImporterService
        - Hopper
        - HttpRbxApiService
        - HttpService
        - ILegacyStudioBridge
        - LegacyStudioBridge
        - IXPService
        - IncrementalPatchBuilder
        - InsertService
        - JointsService
        - KeyboardService
        - KeyframeSequenceProvider
        - LSPFileSyncService
        - LanguageService
        - Lighting
        - LinkingService
        - LiveScriptingService
        - LiveSyncService
        - LocalStorageService
        - AppStorageService
        - UserStorageService
        - LocalizationService
        - LodDataService
        - LogReporterService
        - LogService
        - LoginService
        - LuaWebService
        - LuauScriptAnalyzerService
        - MLModelDeliveryService
        - MarketplaceService
        - MatchmakingService
        - MaterialGenerationService
        - MaterialService
        - MemStorageService
        - MemoryStoreService
        - MessageBusService
        - MessagingService
        - MetaBreakpointManager
        - MouseService
        - NetworkClient
        - NetworkServer
        - NetworkSettings
        - NotificationService
        - OmniRecommendationsService
        - OpenCloudService
        - Workspace
        - PackageService
        - PackageUIService
        - PatchBundlerFileWatch
        - PathfindingService
        - PerformanceControlService
        - PermissionsService
        - PhysicsService
        - PlaceStatsService
        - PlacesService
        - PlatformCloudStorageService
        - PlatformFriendsService
        - PlayerDataService
        - PlayerEmulatorService
        - PlayerHydrationService
        - PlayerViewService
        - Players
        - PluginDebugService
        - PluginGuiService
        - PluginManagementService
        - PluginPolicyService
        - PointsService
        - PolicyService
        - ProcessInstancePhysicsService
        - ProximityPromptService
        - PublishService
        - RbxAnalyticsService
        - ReflectionService
        - RemoteCursorService
        - RemoteDebuggerServer
        - RenderSettings
        - ReplicatedFirst
        - ReplicatedStorage
        - RibbonNotificationService
        - RobloxPluginGuiService
        - RobloxReplicatedStorage
        - RobloxServerStorage
        - RomarkRbxAnalyticsService
        - RomarkService
        - RtMessagingService
        - RunService
        - RuntimeScriptService
        - SafetyService
        - ScriptChangeService
        - ScriptCloneWatcher
        - ScriptCloneWatcherHelper
        - ScriptCommitService
        - ScriptContext
        - ScriptEditorService
        - ScriptProfilerService
        - ScriptRegistrationService
        - ScriptService
        - Selection
        - SelectionHighlightManager
        - SerializationService
        - ServerScriptService
        - ServerStorage
        - ServiceVisibilityService
        - SessionService
        - SharedTableRegistry
        - SmoothVoxelsUpgraderService
        - SnippetService
        - SocialService
        - SoundService
        - SpawnerService
        - StartPageService
        - StarterPack
        - StarterPlayer
        - StartupMessageService
        - Stats
        - StopWatchReporter
        - StreamingService
        - Studio
        - StudioAssetService
        - StudioCameraService
        - StudioData
        - StudioDeviceEmulatorService
        - StudioPublishService
        - StudioScriptDebugEventListener
        - StudioSdkService
        - StudioService
        - StudioUserService
        - StudioWidgetsService
        - StylingService
        - TaskScheduler
        - TeamCreateData
        - TeamCreatePublishService
        - TeamCreateService
        - Teams
        - TelemetryService
        - TeleportService
        - TemporaryCageMeshProvider
        - TemporaryScriptService
        - TestService
        - TextBoxService
        - TextChatService
        - TextService
        - TextureGenerationService
        - ThirdPartyUserService
        - TimerService
        - ToastNotificationService
        - TouchInputService
        - TracerService
        - TutorialService
        - TweenService
        - UGCAvatarService
        - UGCValidationService
        - UIDragDetectorService
        - UniqueIdLookupService
        - UnvalidatedAssetService
        - UserInputService
        - UserService
        - VRService
        - VRStatusService
        - VersionControlService
        - VideoCaptureService
        - VideoService
        - VirtualInputManager
        - VirtualUser
        - VisibilityCheckDispatcher
        - Visit
        - VisualizationModeService
        - VoiceChatInternal
        - VoiceChatService
        - WebSocketService
        - WebViewService
      method: true
      must_use: true
    GetStyled:
      args:
      - required: false
        type: any
      method: true
    GetStyledPropertyChangedSignal:
      args:
      - required: false
        type: any
      method: true
    GetTags:
      args: []
      method: true
    GraphicsQualityChangeRequest:
      struct: Event
    HasTag:
      args:
      - required: false
        type: any
      method: true
    HttpGetAsync:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    HttpPostAsync:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    InsertObjectsAndJoinIfLegacyAsync:
      args:
      - required: false
        type: any
      method: true
    IsA:
      args:
      - required: false
        type: any
      method: true
    IsAncestorOf:
      args:
      - required: false
        type: any
      method: true
    IsContentLoaded:
      args: []
      method: true
    IsDescendantOf:
      args:
      - required: false
        type: any
      method: true
    IsGearTypeAllowed:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    IsLoaded:
      args: []
      method: true
    IsPropertyModified:
      args:
      - required: false
        type: any
      method: true
    IsUniverseMetadataLoaded:
      args: []
      method: true
    ItemChanged:
      struct: Event
      deprecated:
        message: this property is deprecated.
        replace: []
    JobId:
      property: read-only
    Load:
      args:
      - required: false
        type: any
      method: true
    Loaded:
      struct: Event
    MatchmakingType:
      property: read-only
    Name:
      property: override-fields
    OnClose:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    OpenScreenshotsFolder:
      args: []
      method: true
    OpenVideosFolder:
      args: []
      method: true
    Parent:
      struct: Instance
    PlaceId:
      property: read-only
    PlaceVersion:
      property: read-only
    PrivateServerId:
      property: read-only
    PrivateServerOwnerId:
      property: read-only
    Remove:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    RemoveTag:
      args:
      - required: false
        type: any
      method: true
    ReportInGoogleAnalytics:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    ResetPropertyToDefault:
      args:
      - required: false
        type: any
      method: true
    SavePlace:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    ScreenshotReady:
      struct: Event
    ScreenshotSavedToAlbum:
      struct: Event
    ServiceAdded:
      struct: Event
    ServiceRemoving:
      struct: Event
    SetAttribute:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    SetFastFlagForTesting:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    SetFastIntForTesting:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    SetFastStringForTesting:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    SetFlagVersion:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    SetIsLoaded:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    SetPlaceId:
      args:
      - required: false
        type: any
      method: true
    SetUniverseId:
      args:
      - required: false
        type: any
      method: true
    Shutdown:
      args: []
      method: true
    StyledPropertiesChanged:
      struct: Event
    UniverseMetadataLoaded:
      struct: Event
    VIPServerId:
      property: read-only
      deprecated:
        message: this property is deprecated.
        replace: []
    VIPServerOwnerId:
      property: read-only
      deprecated:
        message: this property is deprecated.
        replace: []
    WaitForChild:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    Workspace:
      struct: Workspace
    archivable:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    childAdded:
      struct: Event
      deprecated:
        message: this property is deprecated.
        replace: []
    children:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    className:
      property: read-only
      deprecated:
        message: this property is deprecated.
        replace: []
    clone:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    destroy:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    findFirstChild:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    getChildren:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    getService:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    isA:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    isDescendantOf:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    lighting:
      struct: Instance
      deprecated:
        message: this property is deprecated.
        replace: []
    remove:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    service:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    workspace:
      struct: Workspace
      deprecated:
        message: this property is deprecated.
        replace: []
  EnumItem:
    Name:
      property: read-only
    Value:
      property: read-only
  Event:
    Connect:
      args:
      - type: function
      method: true
    Once:
      args:
      - type: function
      method: true
    Wait:
      args: []
      method: true
    connect:
      args:
      - type: function
      method: true
      deprecated:
        message: lowercase methods have been superseded by uppercase ones
        replace:
        - Connect(%1)
    wait:
      args:
      - type: function
      method: true
      deprecated:
        message: lowercase methods have been superseded by uppercase ones
        replace:
        - Wait(%1)
  Instance:
    '*':
      any: true
  Plugin:
    '*':
      struct: Instance
    Activate:
      args:
      - required: false
        type: any
      method: true
    AddTag:
      args:
      - required: false
        type: any
      method: true
    AncestryChanged:
      struct: Event
    Archivable:
      property: override-fields
    AttributeChanged:
      struct: Event
    Changed:
      struct: Event
    ChildAdded:
      struct: Event
    ChildRemoved:
      struct: Event
    ClassName:
      property: read-only
    ClearAllChildren:
      args: []
      method: true
    Clone:
      args: []
      method: true
    CollisionEnabled:
      property: read-only
    CreateDockWidgetPluginGui:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    CreatePluginAction:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    CreatePluginMenu:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    CreateQWidgetPluginGui:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    CreateToolbar:
      args:
      - required: false
        type: any
      method: true
    Deactivate:
      args: []
      method: true
    Deactivation:
      struct: Event
    DescendantAdded:
      struct: Event
    DescendantRemoving:
      struct: Event
    Destroy:
      args: []
      method: true
    Destroying:
      struct: Event
    FindFirstAncestor:
      args:
      - required: false
        type: any
      method: true
    FindFirstAncestorOfClass:
      args:
      - required: false
        type: any
      method: true
    FindFirstAncestorWhichIsA:
      args:
      - required: false
        type: any
      method: true
    FindFirstChild:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    FindFirstChildOfClass:
      args:
      - required: false
        type: any
      method: true
    FindFirstChildWhichIsA:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    FindFirstDescendant:
      args:
      - required: false
        type: any
      method: true
    GetActor:
      args: []
      method: true
    GetAttribute:
      args:
      - required: false
        type: any
      method: true
    GetAttributeChangedSignal:
      args:
      - required: false
        type: any
      method: true
    GetAttributes:
      args: []
      method: true
    GetChildren:
      args: []
      method: true
    GetDebugId:
      args:
      - required: false
        type: any
      method: true
    GetDescendants:
      args: []
      method: true
    GetFullName:
      args: []
      method: true
    GetItem:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    GetJoinMode:
      args: []
      method: true
    GetMouse:
      args: []
      method: true
    GetPluginComponent:
      args:
      - required: false
        type: any
      method: true
    GetPropertyChangedSignal:
      args:
      - required: false
        type: any
      method: true
    GetSelectedRibbonTool:
      args: []
      method: true
    GetSetting:
      args:
      - required: false
        type: any
      method: true
    GetStudioUserId:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    GetStyled:
      args:
      - required: false
        type: any
      method: true
    GetStyledPropertyChangedSignal:
      args:
      - required: false
        type: any
      method: true
    GetTags:
      args: []
      method: true
    GetUri:
      args: []
      method: true
    GridSize:
      property: read-only
    HasTag:
      args:
      - required: false
        type: any
      method: true
    ImportFbxAnimation:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    ImportFbxRig:
      args:
      - required: false
        type: any
      method: true
    Intersect:
      args:
      - required: false
        type: any
      method: true
    Invoke:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    IsA:
      args:
      - required: false
        type: any
      method: true
    IsActivated:
      args: []
      method: true
    IsActivatedWithExclusiveMouse:
      args: []
      method: true
    IsAncestorOf:
      args:
      - required: false
        type: any
      method: true
    IsDescendantOf:
      args:
      - required: false
        type: any
      method: true
    IsLoadedFromProject:
      args: []
      method: true
    IsPropertyModified:
      args:
      - required: false
        type: any
      method: true
    Name:
      property: override-fields
    Negate:
      args:
      - required: false
        type: any
      method: true
    OnInvoke:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    OnInvokeSuspendOverride:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    OnSetItem:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    OpenScript:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    OpenWikiPage:
      args:
      - required: false
        type: any
      method: true
    Parent:
      struct: Instance
    PauseSound:
      args:
      - required: false
        type: any
      method: true
    PlaySound:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    ProcessAssetInsertionDrag:
      property: override-fields
    ProcessAssetInsertionDrop:
      property: override-fields
    PromptForExistingAssetId:
      args:
      - required: false
        type: any
      method: true
    PromptSaveSelection:
      args:
      - required: false
        type: any
      method: true
    Ready:
      struct: Event
    Remove:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    RemoveTag:
      args:
      - required: false
        type: any
      method: true
    ResetPropertyToDefault:
      args:
      - required: false
        type: any
      method: true
    ResumeSound:
      args:
      - required: false
        type: any
      method: true
    SaveSelectedToRoblox:
      args: []
      method: true
    SelectRibbonTool:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    Separate:
      args:
      - required: false
        type: any
      method: true
    SetAttribute:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    SetItem:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    SetReady:
      args: []
      method: true
    SetSetting:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    StartDecalDrag:
      args:
      - required: false
        type: any
      method: true
    StartDrag:
      args:
      - required: false
        type: any
      method: true
    StopAllSounds:
      args: []
      method: true
    StyledPropertiesChanged:
      struct: Event
    Union:
      args:
      - required: false
        type: any
      method: true
    Unloading:
      struct: Event
    WaitForChild:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    archivable:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    childAdded:
      struct: Event
      deprecated:
        message: this property is deprecated.
        replace: []
    children:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    className:
      property: read-only
      deprecated:
        message: this property is deprecated.
        replace: []
    clone:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    destroy:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    findFirstChild:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    getChildren:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    isA:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    isDescendantOf:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    remove:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
  Script:
    '*':
      struct: Instance
    AddTag:
      args:
      - required: false
        type: any
      method: true
    AncestryChanged:
      struct: Event
    Archivable:
      property: override-fields
    AttributeChanged:
      struct: Event
    Changed:
      struct: Event
    ChildAdded:
      struct: Event
    ChildRemoved:
      struct: Event
    ClassName:
      property: read-only
    ClearAllChildren:
      args: []
      method: true
    Clone:
      args: []
      method: true
    DescendantAdded:
      struct: Event
    DescendantRemoving:
      struct: Event
    Destroy:
      args: []
      method: true
    Destroying:
      struct: Event
    Disabled:
      property: override-fields
    Enabled:
      property: override-fields
    FindFirstAncestor:
      args:
      - required: false
        type: any
      method: true
    FindFirstAncestorOfClass:
      args:
      - required: false
        type: any
      method: true
    FindFirstAncestorWhichIsA:
      args:
      - required: false
        type: any
      method: true
    FindFirstChild:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    FindFirstChildOfClass:
      args:
      - required: false
        type: any
      method: true
    FindFirstChildWhichIsA:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    FindFirstDescendant:
      args:
      - required: false
        type: any
      method: true
    GetActor:
      args: []
      method: true
    GetAttribute:
      args:
      - required: false
        type: any
      method: true
    GetAttributeChangedSignal:
      args:
      - required: false
        type: any
      method: true
    GetAttributes:
      args: []
      method: true
    GetChildren:
      args: []
      method: true
    GetDebugId:
      args:
      - required: false
        type: any
      method: true
    GetDescendants:
      args: []
      method: true
    GetFullName:
      args: []
      method: true
    GetHash:
      args: []
      method: true
    GetPropertyChangedSignal:
      args:
      - required: false
        type: any
      method: true
    GetStyled:
      args:
      - required: false
        type: any
      method: true
    GetStyledPropertyChangedSignal:
      args:
      - required: false
        type: any
      method: true
    GetTags:
      args: []
      method: true
    HasTag:
      args:
      - required: false
        type: any
      method: true
    IsA:
      args:
      - required: false
        type: any
      method: true
    IsAncestorOf:
      args:
      - required: false
        type: any
      method: true
    IsDescendantOf:
      args:
      - required: false
        type: any
      method: true
    IsPropertyModified:
      args:
      - required: false
        type: any
      method: true
    LinkedSource:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    Name:
      property: override-fields
    Parent:
      struct: Instance
    Remove:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    RemoveTag:
      args:
      - required: false
        type: any
      method: true
    ResetPropertyToDefault:
      args:
      - required: false
        type: any
      method: true
    SetAttribute:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    Source:
      property: override-fields
    StyledPropertiesChanged:
      struct: Event
    WaitForChild:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    archivable:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    childAdded:
      struct: Event
      deprecated:
        message: this property is deprecated.
        replace: []
    children:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    className:
      property: read-only
      deprecated:
        message: this property is deprecated.
        replace: []
    clone:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    destroy:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    findFirstChild:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    getChildren:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    isA:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    isDescendantOf:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    remove:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
  Terrain:
    '*':
      struct: Instance
    AddTag:
      args:
      - required: false
        type: any
      method: true
    AncestryChanged:
      struct: Event
    Anchored:
      property: override-fields
    AngularAccelerationToTorque:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    ApplyAngularImpulse:
      args:
      - required: false
        type: any
      method: true
    ApplyImpulse:
      args:
      - required: false
        type: any
      method: true
    ApplyImpulseAtPosition:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    Archivable:
      property: override-fields
    AssemblyAngularVelocity:
      any: true
    AssemblyCenterOfMass:
      any: true
    AssemblyLinearVelocity:
      any: true
    AssemblyMass:
      property: read-only
    AssemblyRootPart:
      struct: BasePart
    AttributeChanged:
      struct: Event
    AudioCanCollide:
      property: override-fields
    AutowedgeCell:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    AutowedgeCells:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    BackParamA:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    BackParamB:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    BackSurface:
      property: override-fields
    BackSurfaceInput:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    BottomParamA:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    BottomParamB:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    BottomSurface:
      property: override-fields
    BottomSurfaceInput:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    BreakJoints:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    BrickColor:
      property: override-fields
    CFrame:
      any: true
    CanCollide:
      property: override-fields
    CanCollideWith:
      args:
      - required: false
        type: any
      method: true
    CanQuery:
      property: override-fields
    CanSetNetworkOwnership:
      args: []
      method: true
    CanSmoothVoxelsBeUpgraded:
      args: []
      method: true
    CanTouch:
      property: override-fields
    CastShadow:
      property: override-fields
    CellCenterToWorld:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    CellCornerToWorld:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    CenterOfMass:
      any: true
    Changed:
      struct: Event
    ChildAdded:
      struct: Event
    ChildRemoved:
      struct: Event
    ClassName:
      property: read-only
    Clear:
      args: []
      method: true
    ClearAllChildren:
      args: []
      method: true
    Clone:
      args: []
      method: true
    CollisionGroup:
      property: override-fields
    CollisionGroupId:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    Color:
      property: override-fields
    ConvertToSmooth:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    CopyRegion:
      args:
      - required: false
        type: any
      method: true
    CountCells:
      args: []
      method: true
    CurrentPhysicalProperties:
      property: read-only
    CustomPhysicalProperties:
      property: override-fields
    Decoration:
      property: override-fields
    DescendantAdded:
      struct: Event
    DescendantRemoving:
      struct: Event
    Destroy:
      args: []
      method: true
    Destroying:
      struct: Event
    Elasticity:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    EnableFluidForces:
      property: override-fields
    ExtentsCFrame:
      any: true
    ExtentsSize:
      any: true
    FillBall:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    FillBlock:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    FillCylinder:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    FillRegion:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    FillWedge:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    FindFirstAncestor:
      args:
      - required: false
        type: any
      method: true
    FindFirstAncestorOfClass:
      args:
      - required: false
        type: any
      method: true
    FindFirstAncestorWhichIsA:
      args:
      - required: false
        type: any
      method: true
    FindFirstChild:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    FindFirstChildOfClass:
      args:
      - required: false
        type: any
      method: true
    FindFirstChildWhichIsA:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    FindFirstDescendant:
      args:
      - required: false
        type: any
      method: true
    Friction:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    FrontParamA:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    FrontParamB:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    FrontSurface:
      property: override-fields
    FrontSurfaceInput:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    GetActor:
      args: []
      method: true
    GetAttribute:
      args:
      - required: false
        type: any
      method: true
    GetAttributeChangedSignal:
      args:
      - required: false
        type: any
      method: true
    GetAttributes:
      args: []
      method: true
    GetCell:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    GetChildren:
      args: []
      method: true
    GetClosestPointOnSurface:
      args:
      - required: false
        type: any
      method: true
    GetConnectedParts:
      args:
      - required: false
        type: any
      method: true
    GetDebugId:
      args:
      - required: false
        type: any
      method: true
    GetDescendants:
      args: []
      method: true
    GetFullName:
      args: []
      method: true
    GetJoints:
      args: []
      method: true
    GetMass:
      args: []
      method: true
    GetMaterialColor:
      args:
      - required: false
        type: any
      method: true
    GetNetworkOwner:
      args: []
      method: true
    GetNetworkOwnershipAuto:
      args: []
      method: true
    GetNoCollisionConstraints:
      args: []
      method: true
    GetPivot:
      args: []
      method: true
    GetPropertyChangedSignal:
      args:
      - required: false
        type: any
      method: true
    GetRenderCFrame:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    GetRootPart:
      args: []
      method: true
    GetStyled:
      args:
      - required: false
        type: any
      method: true
    GetStyledPropertyChangedSignal:
      args:
      - required: false
        type: any
      method: true
    GetTags:
      args: []
      method: true
    GetTerrainWireframe:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    GetTouchingParts:
      args: []
      method: true
    GetVelocityAtPosition:
      args:
      - required: false
        type: any
      method: true
    GetWaterCell:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    GrassLength:
      property: override-fields
    HasTag:
      args:
      - required: false
        type: any
      method: true
    IntersectAsync:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    IsA:
      args:
      - required: false
        type: any
      method: true
    IsAncestorOf:
      args:
      - required: false
        type: any
      method: true
    IsDescendantOf:
      args:
      - required: false
        type: any
      method: true
    IsGrounded:
      args: []
      method: true
    IsPropertyModified:
      args:
      - required: false
        type: any
      method: true
    IsSmooth:
      property: read-only
      deprecated:
        message: this property is deprecated.
        replace: []
    LeftParamA:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    LeftParamB:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    LeftSurface:
      property: override-fields
    LeftSurfaceInput:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    LocalSimulationTouched:
      struct: Event
      deprecated:
        message: this property is deprecated.
        replace: []
    LocalTransparencyModifier:
      property: override-fields
    Locked:
      property: override-fields
    MakeJoints:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    Mass:
      property: read-only
    Massless:
      property: override-fields
    Material:
      property: override-fields
    MaterialColors:
      property: override-fields
    MaterialVariant:
      property: override-fields
    MaxExtents:
      property: read-only
    Name:
      property: override-fields
    Orientation:
      any: true
    Origin:
      any: true
    OutfitChanged:
      struct: Event
      deprecated:
        message: this property is deprecated.
        replace: []
    Parent:
      struct: Instance
    PasteRegion:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    Pivot Offset:
      any: true
    PivotOffset:
      any: true
    PivotTo:
      args:
      - required: false
        type: any
      method: true
    Position:
      any: true
    ReadVoxelChannels:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    ReadVoxels:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    ReceiveAge:
      property: read-only
    Reflectance:
      property: override-fields
    Remove:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    RemoveTag:
      args:
      - required: false
        type: any
      method: true
    ReplaceMaterial:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    ReplaceMaterialInTransform:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    ReplaceMaterialInTransformSubregion:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    ResetPropertyToDefault:
      args:
      - required: false
        type: any
      method: true
    Resize:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    ResizeIncrement:
      property: read-only
    ResizeableFaces:
      property: read-only
    RightParamA:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    RightParamB:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    RightSurface:
      property: override-fields
    RightSurfaceInput:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    RootPriority:
      property: override-fields
    RotVelocity:
      any: true
      deprecated:
        message: this property is deprecated.
        replace: []
    Rotation:
      any: true
    SetAttribute:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    SetCell:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    SetCells:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    SetMaterialColor:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    SetMaterialInTransform:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    SetMaterialInTransformSubregion:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    SetNetworkOwner:
      args:
      - required: false
        type: any
      method: true
    SetNetworkOwnershipAuto:
      args: []
      method: true
    SetWaterCell:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    Size:
      any: true
    SmoothRegion:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    SpecificGravity:
      property: read-only
      deprecated:
        message: this property is deprecated.
        replace: []
    StoppedTouching:
      struct: Event
      deprecated:
        message: this property is deprecated.
        replace: []
    StyledPropertiesChanged:
      struct: Event
    SubtractAsync:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    TopParamA:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    TopParamB:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    TopSurface:
      property: override-fields
    TopSurfaceInput:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    TorqueToAngularAcceleration:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    TouchEnded:
      struct: Event
    Touched:
      struct: Event
    Transparency:
      property: override-fields
    UnionAsync:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    Velocity:
      any: true
      deprecated:
        message: this property is deprecated.
        replace: []
    WaitForChild:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    WaterColor:
      property: override-fields
    WaterReflectance:
      property: override-fields
    WaterTransparency:
      property: override-fields
    WaterWaveSize:
      property: override-fields
    WaterWaveSpeed:
      property: override-fields
    WorldToCell:
      args:
      - required: false
        type: any
      method: true
    WorldToCellPreferEmpty:
      args:
      - required: false
        type: any
      method: true
    WorldToCellPreferSolid:
      args:
      - required: false
        type: any
      method: true
    WriteVoxelChannels:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    WriteVoxels:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    archivable:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    breakJoints:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    brickColor:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    childAdded:
      struct: Event
      deprecated:
        message: this property is deprecated.
        replace: []
    children:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    className:
      property: read-only
      deprecated:
        message: this property is deprecated.
        replace: []
    clone:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    destroy:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    findFirstChild:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    getChildren:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    getMass:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    isA:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    isDescendantOf:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    makeJoints:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    remove:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    resize:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
  Workspace:
    '*':
      struct: Instance
    AddPersistentPlayer:
      args:
      - required: false
        type: any
      method: true
    AddTag:
      args:
      - required: false
        type: any
      method: true
    AirDensity:
      property: override-fields
    AllowThirdPartySales:
      property: override-fields
    AncestryChanged:
      struct: Event
    Archivable:
      property: override-fields
    ArePartsTouchingOthers:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    AttributeChanged:
      struct: Event
    AvatarUnificationMode:
      property: override-fields
    Blockcast:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    BreakJoints:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    BulkMoveTo:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    CacheCurrentTerrain:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    CalculateJumpDistance:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    CalculateJumpHeight:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    CalculateJumpPower:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    Changed:
      struct: Event
    ChildAdded:
      struct: Event
    ChildRemoved:
      struct: Event
    ClassName:
      property: read-only
    ClearAllChildren:
      args: []
      method: true
    ClearCachedTerrain:
      args:
      - required: false
        type: any
      method: true
    ClientAnimatorThrottling:
      property: override-fields
    Clone:
      args: []
      method: true
    CurrentCamera:
      struct: Camera
    DescendantAdded:
      struct: Event
    DescendantRemoving:
      struct: Event
    Destroy:
      args: []
      method: true
    Destroying:
      struct: Event
    DistributedGameTime:
      property: override-fields
    ExperimentalSolverIsEnabled:
      args: []
      method: true
    FindFirstAncestor:
      args:
      - required: false
        type: any
      method: true
    FindFirstAncestorOfClass:
      args:
      - required: false
        type: any
      method: true
    FindFirstAncestorWhichIsA:
      args:
      - required: false
        type: any
      method: true
    FindFirstChild:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    FindFirstChildOfClass:
      args:
      - required: false
        type: any
      method: true
    FindFirstChildWhichIsA:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    FindFirstDescendant:
      args:
      - required: false
        type: any
      method: true
    FindPartOnRay:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    FindPartOnRayWithIgnoreList:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    FindPartOnRayWithWhitelist:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    FindPartsInRegion3:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    FindPartsInRegion3WithIgnoreList:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    FindPartsInRegion3WithWhiteList:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    FluidForces:
      property: override-fields
    GetActor:
      args: []
      method: true
    GetAttribute:
      args:
      - required: false
        type: any
      method: true
    GetAttributeChangedSignal:
      args:
      - required: false
        type: any
      method: true
    GetAttributes:
      args: []
      method: true
    GetBoundingBox:
      args: []
      method: true
    GetChildren:
      args: []
      method: true
    GetDebugId:
      args:
      - required: false
        type: any
      method: true
    GetDescendants:
      args: []
      method: true
    GetExtentsSize:
      args: []
      method: true
    GetFullName:
      args: []
      method: true
    GetModelCFrame:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    GetModelSize:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    GetNumAwakeParts:
      args: []
      method: true
    GetPartBoundsInBox:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    GetPartBoundsInRadius:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    GetPartsInPart:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    GetPersistentPlayers:
      args: []
      method: true
    GetPhysicsThrottling:
      args: []
      method: true
    GetPivot:
      args: []
      method: true
    GetPrimaryPartCFrame:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    GetPropertyChangedSignal:
      args:
      - required: false
        type: any
      method: true
    GetRealPhysicsFPS:
      args: []
      method: true
    GetScale:
      args: []
      method: true
    GetServerTimeNow:
      args: []
      method: true
    GetStyled:
      args:
      - required: false
        type: any
      method: true
    GetStyledPropertyChangedSignal:
      args:
      - required: false
        type: any
      method: true
    GetTags:
      args: []
      method: true
    GlobalWind:
      any: true
    Gravity:
      property: override-fields
    HasTag:
      args:
      - required: false
        type: any
      method: true
    IKControlConstraintSupport:
      property: override-fields
    IKMoveTo:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    InsertPoint:
      any: true
    IsA:
      args:
      - required: false
        type: any
      method: true
    IsAncestorOf:
      args:
      - required: false
        type: any
      method: true
    IsDescendantOf:
      args:
      - required: false
        type: any
      method: true
    IsPropertyModified:
      args:
      - required: false
        type: any
      method: true
    IsRegion3Empty:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    IsRegion3EmptyWithIgnoreList:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    JoinToOutsiders:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    MakeJoints:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    MeshPartHeadsAndAccessories:
      property: override-fields
    ModelStreamingBehavior:
      property: override-fields
    ModelStreamingMode:
      property: override-fields
    MoveTo:
      args:
      - required: false
        type: any
      method: true
    MoverConstraintRootBehavior:
      property: override-fields
    Name:
      property: override-fields
    Origin:
      any: true
    PGSIsEnabled:
      args: []
      method: true
    Parent:
      struct: Instance
    PathfindingUseImprovedSearch:
      property: override-fields
    PersistentLoaded:
      struct: Event
    PhysicsImprovedSleep:
      property: override-fields
    PhysicsSteppingMethod:
      property: override-fields
    Pivot Offset:
      any: true
    PivotTo:
      args:
      - required: false
        type: any
      method: true
    PlayerCharacterDestroyBehavior:
      property: override-fields
    PrimalPhysicsSolver:
      property: override-fields
    PrimaryPart:
      struct: BasePart
    Raycast:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    RaycastCachedTerrain:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    RejectCharacterDeletions:
      property: override-fields
    Remove:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    RemovePersistentPlayer:
      args:
      - required: false
        type: any
      method: true
    RemoveTag:
      args:
      - required: false
        type: any
      method: true
    RenderingCacheOptimizations:
      property: override-fields
    ReplicateInstanceDestroySetting:
      property: override-fields
    ResetOrientationToIdentity:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    ResetPropertyToDefault:
      args:
      - required: false
        type: any
      method: true
    Retargeting:
      property: override-fields
    SandboxedInstanceMode:
      property: override-fields
    Scale:
      property: override-fields
    ScaleTo:
      args:
      - required: false
        type: any
      method: true
    SetAttribute:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    SetAvatarUnificationMode:
      args:
      - required: false
        type: any
      method: true
    SetIdentityOrientation:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    SetInsertPoint:
      args:
      - required: false
        type: any
      method: true
    SetMeshPartHeadsAndAccessories:
      args:
      - required: false
        type: any
      method: true
    SetPhysicsThrottleEnabled:
      args:
      - required: false
        type: any
      method: true
    SetPrimaryPartCFrame:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    Shapecast:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    SignalBehavior:
      property: override-fields
    Spherecast:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
    StepPhysics:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    StreamOutBehavior:
      property: override-fields
    StreamingIntegrityMode:
      property: override-fields
    StreamingMinRadius:
      property: override-fields
    StreamingTargetRadius:
      property: override-fields
    StyledPropertiesChanged:
      struct: Event
    Terrain:
      struct: Terrain
    TouchEventsUseCollisionGroups:
      property: override-fields
    TouchesUseCollisionGroups:
      property: override-fields
    TranslateBy:
      args:
      - required: false
        type: any
      method: true
    UnjoinFromOutsiders:
      args:
      - required: false
        type: any
      method: true
    WaitForChild:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
    WorldPivot:
      any: true
    ZoomToExtents:
      args: []
      method: true
    archivable:
      property: override-fields
      deprecated:
        message: this property is deprecated.
        replace: []
    breakJoints:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    childAdded:
      struct: Event
      deprecated:
        message: this property is deprecated.
        replace: []
    children:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    className:
      property: read-only
      deprecated:
        message: this property is deprecated.
        replace: []
    clone:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    destroy:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    findFirstChild:
      args:
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    findPartOnRay:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    findPartsInRegion3:
      args:
      - required: false
        type: any
      - required: false
        type: any
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    getChildren:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    isA:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    isDescendantOf:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    makeJoints:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    move:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    moveTo:
      args:
      - required: false
        type: any
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []
    remove:
      args: []
      method: true
      deprecated:
        message: this property is deprecated.
        replace: []

last_updated: **********
last_selene_version: 0.28.0
roblox_classes:
  Accessory:
    superclass: Accoutrement
    events: []
    properties:
    - AccessoryType
  AccessoryDescription:
    superclass: Instance
    events: []
    properties:
    - AccessoryType
    - AssetId
    - Instance
    - IsLayered
    - Order
    - Position
    - Puffiness
    - Rotation
    - Scale
  AccountService:
    superclass: Instance
    events: []
    properties: []
  Accoutrement:
    superclass: Instance
    events: []
    properties:
    - AttachmentForward
    - AttachmentPoint
    - AttachmentPos
    - AttachmentRight
    - AttachmentUp
  AchievementService:
    superclass: Instance
    events: []
    properties: []
  ActivityHistoryEventService:
    superclass: Instance
    events:
    - WriteActivityHistoryEventFromStudio
    properties: []
  Actor:
    superclass: Model
    events: []
    properties: []
  AdGui:
    superclass: SurfaceGuiBase
    events:
    - adGuiStateChanged
    properties:
    - AdShape
    - EnableVideoAds
    - FallbackImage
    - Status
  AdPortal:
    superclass: Instance
    events: []
    properties:
    - PortalInvalidReason
    - PortalVersion
    - Status
  AdService:
    superclass: Instance
    events:
    - AdTeleportEnded
    - AdTeleportInitiated
    - PortalPrompt
    - ShowDynamicEudsaDisclosure
    - ShowReportAdPopup
    - VideoAdClosed
    - adGuiRegisterUI
    properties: []
  AdvancedDragger:
    superclass: Instance
    events: []
    properties: []
  AirController:
    superclass: ControllerBase
    events: []
    properties:
    - BalanceMaxTorque
    - BalanceSpeed
    - LinearImpulse
    - MaintainAngularMomentum
    - MaintainLinearMomentum
    - MoveMaxForce
    - TurnMaxTorque
    - TurnSpeedFactor
  AlignOrientation:
    superclass: Constraint
    events: []
    properties:
    - AlignType
    - CFrame
    - LookAtPosition
    - MaxAngularVelocity
    - MaxTorque
    - Mode
    - PrimaryAxis
    - PrimaryAxisOnly
    - ReactionTorqueEnabled
    - Responsiveness
    - RigidityEnabled
    - SecondaryAxis
  AlignPosition:
    superclass: Constraint
    events: []
    properties:
    - ApplyAtCenterOfMass
    - ForceLimitMode
    - ForceRelativeTo
    - MaxAxesForce
    - MaxForce
    - MaxVelocity
    - Mode
    - Position
    - ReactionForceEnabled
    - Responsiveness
    - RigidityEnabled
  AnalysticsSettings:
    superclass: GenericSettings
    events: []
    properties: []
  AnalyticsService:
    superclass: Instance
    events: []
    properties:
    - ApiKey
  AngularVelocity:
    superclass: Constraint
    events: []
    properties:
    - AngularVelocity
    - MaxTorque
    - ReactionTorqueEnabled
    - RelativeTo
  Animation:
    superclass: Instance
    events: []
    properties:
    - AnimationId
  AnimationClip:
    superclass: Instance
    events: []
    properties:
    - Guid
    - Loop
    - Priority
  AnimationClipProvider:
    superclass: Instance
    events: []
    properties: []
  AnimationConstraint:
    superclass: Constraint
    events: []
    properties:
    - C0
    - C1
    - IsKinematic
    - MaxForce
    - MaxTorque
    - Part0
    - Part1
    - Transform
  AnimationController:
    superclass: Instance
    events:
    - AnimationPlayed
    properties: []
  AnimationFromVideoCreatorService:
    superclass: Instance
    events: []
    properties: []
  AnimationFromVideoCreatorStudioService:
    superclass: Instance
    events: []
    properties: []
  AnimationImportData:
    superclass: BaseImportData
    events: []
    properties: []
  AnimationRigData:
    superclass: Instance
    events: []
    properties: []
  AnimationStreamTrack:
    superclass: Instance
    events:
    - Stopped
    properties:
    - Animation
    - FACSDataLod
    - IsPlaying
    - Priority
    - WeightCurrent
    - WeightTarget
  AnimationTrack:
    superclass: Instance
    events:
    - DidLoop
    - Ended
    - KeyframeReached
    - Stopped
    properties:
    - Animation
    - IsPlaying
    - Length
    - Looped
    - Priority
    - Speed
    - TimePosition
    - WeightCurrent
    - WeightTarget
  Animator:
    superclass: Instance
    events:
    - AnimationPlayed
    - AnimationPlayedCoreScript
    - AnimationStreamTrackPlayed
    properties:
    - EvaluationThrottled
    - PreferLodEnabled
    - RootMotion
    - RootMotionWeight
  Annotation:
    superclass: Instance
    events:
    - RequestCompleted
    - RequestInitiated
    properties:
    - AuthorColor3
    - AuthorId
    - Contents
    - CreationTimeUnix
    - LastModifiedTimeUnix
    - LoadingReplies
    - ReplyCount
    - Resolved
    - TaggedUsers
  AnnotationsService:
    superclass: Instance
    events:
    - AnnotationAdded
    - AnnotationDeleted
    - AnnotationEdited
    - AnnotationResolved
    - DEPRECATED_AnnotationEdited
    - DEPRECATED_ServerLoadAnnotationReplies
    - ServerLoadAnnotationReplies
    - ServerLoadResolvedAnnotations
    properties:
    - AnnotationsVisible
    - Hovered
    - Mode
    - Selected
  AppLifecycleObserverService:
    superclass: Instance
    events:
    - OnBecomeActive
    - OnDetach
    - OnHide
    - OnResignActive
    - OnStart
    - OnUnhide
    properties: []
  AppStorageService:
    superclass: LocalStorageService
    events: []
    properties: []
  AppUpdateService:
    superclass: Instance
    events: []
    properties: []
  ArcHandles:
    superclass: HandlesBase
    events:
    - MouseButton1Down
    - MouseButton1Up
    - MouseDrag
    - MouseEnter
    - MouseLeave
    properties:
    - Axes
  AssetCounterService:
    superclass: Instance
    events: []
    properties: []
  AssetDeliveryProxy:
    superclass: Instance
    events: []
    properties:
    - Interface
    - Port
    - StartServer
  AssetImportService:
    superclass: Instance
    events: []
    properties: []
  AssetImportSession:
    superclass: ImportSession
    events: []
    properties: []
  AssetManagerService:
    superclass: Instance
    events:
    - AssetImportedSignal
    - ImportSessionFinished
    - ImportSessionStarted
    properties: []
  AssetPatchSettings:
    superclass: Instance
    events: []
    properties:
    - ContentId
    - OutputPath
    - PatchId
  AssetService:
    superclass: Instance
    events:
    - AudioMetadataFailedResponse
    - AudioMetadataRequest
    - AudioMetadataResponse
    - OpenCreateResultModal
    - OpenPublishResultModal
    properties: []
  AssetSoundEffect:
    superclass: CustomSoundEffect
    events: []
    properties: []
  Atmosphere:
    superclass: Instance
    events: []
    properties:
    - Color
    - Decay
    - Density
    - Glare
    - Haze
    - Offset
  AtmosphereSensor:
    superclass: SensorBase
    events: []
    properties:
    - AirDensity
    - RelativeWindVelocity
  Attachment:
    superclass: Instance
    events: []
    properties:
    - Axis
    - CFrame
    - Orientation
    - Position
    - Rotation
    - SecondaryAxis
    - Visible
    - WorldAxis
    - WorldCFrame
    - WorldOrientation
    - WorldPosition
    - WorldRotation
    - WorldSecondaryAxis
  AudioAnalyzer:
    superclass: Instance
    events:
    - WiringChanged
    properties:
    - PeakLevel
    - RmsLevel
    - SpectrumEnabled
    - WindowSize
  AudioChannelMixer:
    superclass: Instance
    events:
    - WiringChanged
    properties:
    - Layout
  AudioChannelSplitter:
    superclass: Instance
    events:
    - WiringChanged
    properties:
    - Layout
  AudioChorus:
    superclass: Instance
    events:
    - WiringChanged
    properties:
    - Bypass
    - Depth
    - Mix
    - Rate
  AudioCompressor:
    superclass: Instance
    events:
    - WiringChanged
    properties:
    - Attack
    - Bypass
    - Editor
    - MakeupGain
    - Ratio
    - Release
    - Threshold
  AudioDeviceInput:
    superclass: Instance
    events:
    - WiringChanged
    properties:
    - AccessType
    - Active
    - IsReady
    - Muted
    - MutedByLocalUser
    - Player
    - Volume
  AudioDeviceOutput:
    superclass: Instance
    events:
    - WiringChanged
    properties:
    - Player
  AudioDistortion:
    superclass: Instance
    events:
    - WiringChanged
    properties:
    - Bypass
    - Level
  AudioEcho:
    superclass: Instance
    events:
    - WiringChanged
    properties:
    - Bypass
    - DelayTime
    - DryLevel
    - Feedback
    - RampTime
    - WetLevel
  AudioEmitter:
    superclass: Instance
    events:
    - WiringChanged
    properties:
    - AngleAttenuation
    - AudioInteractionGroup
    - DistanceAttenuation
    - SimulationFidelity
  AudioEqualizer:
    superclass: Instance
    events:
    - WiringChanged
    properties:
    - Bypass
    - Editor
    - HighGain
    - LowGain
    - MidGain
    - MidRange
  AudioFader:
    superclass: Instance
    events:
    - WiringChanged
    properties:
    - Bypass
    - Volume
  AudioFilter:
    superclass: Instance
    events:
    - WiringChanged
    properties:
    - Bypass
    - Editor
    - FilterType
    - Frequency
    - Gain
    - Q
  AudioFlanger:
    superclass: Instance
    events:
    - WiringChanged
    properties:
    - Bypass
    - Depth
    - Mix
    - Rate
  AudioFocusService:
    superclass: Instance
    events:
    - OnContextRegistered
    - OnContextUnregistered
    - OnDeafenVoiceAudio
    - OnUndeafenVoiceAudio
    properties: []
  AudioLimiter:
    superclass: Instance
    events:
    - WiringChanged
    properties:
    - Bypass
    - Editor
    - MaxLevel
    - Release
  AudioListener:
    superclass: Instance
    events:
    - WiringChanged
    properties:
    - AngleAttenuation
    - AudioInteractionGroup
    - DistanceAttenuation
    - SimulationFidelity
  AudioPages:
    superclass: Pages
    events: []
    properties: []
  AudioPitchShifter:
    superclass: Instance
    events:
    - WiringChanged
    properties:
    - Bypass
    - Pitch
    - WindowSize
  AudioPlayer:
    superclass: Instance
    events:
    - Ended
    - Looped
    - WiringChanged
    properties:
    - Asset
    - AssetId
    - AutoLoad
    - IsPlaying
    - IsReady
    - LoopRegion
    - Looping
    - PlaybackRegion
    - PlaybackSpeed
    - TimeLength
    - TimePosition
    - Volume
  AudioReverb:
    superclass: Instance
    events:
    - WiringChanged
    properties:
    - Bypass
    - DecayRatio
    - DecayTime
    - Density
    - Diffusion
    - DryLevel
    - EarlyDelayTime
    - HighCutFrequency
    - LateDelayTime
    - LowShelfFrequency
    - LowShelfGain
    - ReferenceFrequency
    - WetLevel
  AudioSearchParams:
    superclass: Instance
    events: []
    properties:
    - Album
    - Artist
    - AudioSubType
    - AudioSubtype
    - MaxDuration
    - MinDuration
    - SearchKeyword
    - Tag
    - Title
  AudioTextToSpeech:
    superclass: Instance
    events:
    - Ended
    - Looped
    - WiringChanged
    properties:
    - IsLoaded
    - IsPlaying
    - Looping
    - Pitch
    - PlaybackSpeed
    - Speed
    - Text
    - TimeLength
    - TimePosition
    - VoiceId
    - Volume
  AvatarChatService:
    superclass: Instance
    events: []
    properties:
    - ClientFeatures
    - ClientFeaturesInitialized
    - ServerFeatures
  AvatarCreationService:
    superclass: Instance
    events:
    - AvatarModerationCompleted
    - UgcValidationFailure
    - UgcValidationSuccess
    properties: []
  AvatarEditorService:
    superclass: Instance
    events:
    - OpenAllowInventoryReadAccess
    - OpenPromptCreateOufit
    - OpenPromptDeleteOutfit
    - OpenPromptRenameOutfit
    - OpenPromptSaveAvatar
    - OpenPromptSetFavorite
    - OpenPromptUpdateOutfit
    - PromptAllowInventoryReadAccessCompleted
    - PromptCreateOutfitCompleted
    - PromptDeleteOutfitCompleted
    - PromptRenameOutfitCompleted
    - PromptSaveAvatarCompleted
    - PromptSaveAvatarThumbnailCustomizationCompleted
    - PromptSetFavoriteCompleted
    - PromptUpdateOutfitCompleted
    properties: []
  AvatarImportService:
    superclass: Instance
    events: []
    properties: []
  AvatarPreloader:
    superclass: Instance
    events: []
    properties: []
  Backpack:
    superclass: Instance
    events: []
    properties: []
  BackpackItem:
    superclass: Model
    events: []
    properties:
    - TextureId
  BadgeService:
    superclass: Instance
    events:
    - BadgeAwarded
    - OnBadgeAwarded
    properties: []
  BallSocketConstraint:
    superclass: Constraint
    events: []
    properties:
    - LimitsEnabled
    - MaxFrictionTorque
    - Radius
    - Restitution
    - TwistLimitsEnabled
    - TwistLowerAngle
    - TwistUpperAngle
    - UpperAngle
  BanHistoryPages:
    superclass: Pages
    events: []
    properties: []
  BaseImportData:
    superclass: Instance
    events:
    - StatusRemoved
    - StatusReported
    properties:
    - Id
    - ImportName
    - ShouldImport
  BasePart:
    superclass: PVInstance
    events:
    - LocalSimulationTouched
    - OutfitChanged
    - StoppedTouching
    - TouchEnded
    - Touched
    properties:
    - Anchored
    - AssemblyAngularVelocity
    - AssemblyCenterOfMass
    - AssemblyLinearVelocity
    - AssemblyMass
    - AssemblyRootPart
    - AudioCanCollide
    - BackParamA
    - BackParamB
    - BackSurface
    - BackSurfaceInput
    - BottomParamA
    - BottomParamB
    - BottomSurface
    - BottomSurfaceInput
    - BrickColor
    - CFrame
    - CanCollide
    - CanQuery
    - CanTouch
    - CastShadow
    - CenterOfMass
    - CollisionGroup
    - CollisionGroupId
    - Color
    - CurrentPhysicalProperties
    - CustomPhysicalProperties
    - Elasticity
    - EnableFluidForces
    - ExtentsCFrame
    - ExtentsSize
    - Friction
    - FrontParamA
    - FrontParamB
    - FrontSurface
    - FrontSurfaceInput
    - LeftParamA
    - LeftParamB
    - LeftSurface
    - LeftSurfaceInput
    - LocalTransparencyModifier
    - Locked
    - Mass
    - Massless
    - Material
    - MaterialVariant
    - Orientation
    - PivotOffset
    - Position
    - ReceiveAge
    - Reflectance
    - ResizeIncrement
    - ResizeableFaces
    - RightParamA
    - RightParamB
    - RightSurface
    - RightSurfaceInput
    - RootPriority
    - RotVelocity
    - Rotation
    - Size
    - SpecificGravity
    - TopParamA
    - TopParamB
    - TopSurface
    - TopSurfaceInput
    - Transparency
    - Velocity
    - brickColor
  BasePlayerGui:
    superclass: Instance
    events: []
    properties: []
  BaseRemoteEvent:
    superclass: Instance
    events: []
    properties: []
  BaseScript:
    superclass: LuaSourceContainer
    events: []
    properties:
    - Disabled
    - Enabled
    - LinkedSource
    - RunContext
  BaseWrap:
    superclass: Instance
    events:
    - VerticesModified
    properties:
    - CageMeshContent
    - CageMeshId
    - CageOrigin
    - CageOriginWorld
    - HSRAssetId
    - ImportOrigin
    - ImportOriginWorld
  Beam:
    superclass: Instance
    events: []
    properties:
    - Attachment0
    - Attachment1
    - Brightness
    - Color
    - CurveSize0
    - CurveSize1
    - Enabled
    - FaceCamera
    - LightEmission
    - LightInfluence
    - LocalTransparencyModifier
    - Segments
    - Texture
    - TextureLength
    - TextureMode
    - TextureSpeed
    - Transparency
    - Width0
    - Width1
    - ZOffset
  BevelMesh:
    superclass: DataModelMesh
    events: []
    properties: []
  BillboardGui:
    superclass: LayerCollector
    events: []
    properties:
    - Active
    - Adornee
    - AlwaysOnTop
    - Brightness
    - ClipsDescendants
    - CurrentDistance
    - DistanceLowerLimit
    - DistanceStep
    - DistanceUpperLimit
    - ExtentsOffset
    - ExtentsOffsetWorldSpace
    - LightInfluence
    - MaxDistance
    - PlayerToHideFrom
    - Size
    - SizeOffset
    - StudsOffset
    - StudsOffsetWorldSpace
  BinaryStringValue:
    superclass: ValueBase
    events:
    - Changed
    properties: []
  BindableEvent:
    superclass: Instance
    events:
    - Event
    properties: []
  BindableFunction:
    superclass: Instance
    events: []
    properties: []
  BlockMesh:
    superclass: BevelMesh
    events: []
    properties: []
  BloomEffect:
    superclass: PostEffect
    events: []
    properties:
    - Intensity
    - Size
    - Threshold
  BlurEffect:
    superclass: PostEffect
    events: []
    properties:
    - Size
  BodyAngularVelocity:
    superclass: BodyMover
    events: []
    properties:
    - AngularVelocity
    - MaxTorque
    - P
    - angularvelocity
    - maxTorque
  BodyColors:
    superclass: CharacterAppearance
    events: []
    properties:
    - HeadColor
    - HeadColor3
    - LeftArmColor
    - LeftArmColor3
    - LeftLegColor
    - LeftLegColor3
    - RightArmColor
    - RightArmColor3
    - RightLegColor
    - RightLegColor3
    - TorsoColor
    - TorsoColor3
  BodyForce:
    superclass: BodyMover
    events: []
    properties:
    - Force
    - force
  BodyGyro:
    superclass: BodyMover
    events: []
    properties:
    - CFrame
    - D
    - MaxTorque
    - P
    - cframe
    - maxTorque
  BodyMover:
    superclass: Instance
    events: []
    properties: []
  BodyPartDescription:
    superclass: Instance
    events: []
    properties:
    - AssetId
    - BodyPart
    - Color
    - Instance
  BodyPosition:
    superclass: BodyMover
    events:
    - ReachedTarget
    properties:
    - D
    - MaxForce
    - P
    - Position
    - maxForce
    - position
  BodyThrust:
    superclass: BodyMover
    events: []
    properties:
    - Force
    - Location
    - force
    - location
  BodyVelocity:
    superclass: BodyMover
    events: []
    properties:
    - MaxForce
    - P
    - Velocity
    - maxForce
    - velocity
  Bone:
    superclass: Attachment
    events: []
    properties:
    - Transform
    - TransformedCFrame
    - TransformedWorldCFrame
  BoolValue:
    superclass: ValueBase
    events:
    - Changed
    - changed
    properties:
    - Value
  BoxHandleAdornment:
    superclass: HandleAdornment
    events: []
    properties:
    - Size
  Breakpoint:
    superclass: Instance
    events: []
    properties:
    - Condition
    - ContinueExecution
    - Enabled
    - Id
    - Line
    - LogMessage
    - MetaBreakpointId
    - RemoveOnHit
    - Script
    - Valid
    - Verified
  BrickColorValue:
    superclass: ValueBase
    events:
    - Changed
    - changed
    properties:
    - Value
  BrowserService:
    superclass: Instance
    events:
    - AuthCookieCopiedToEngine
    - BrowserWindowClosed
    - BrowserWindowWillNavigate
    - JavaScriptCallback
    properties: []
  BubbleChatConfiguration:
    superclass: TextChatConfigurations
    events: []
    properties:
    - AdorneeName
    - BackgroundColor3
    - BackgroundTransparency
    - BubbleDuration
    - BubblesSpacing
    - Enabled
    - Font
    - FontFace
    - LocalPlayerStudsOffset
    - MaxBubbles
    - MaxDistance
    - MinimizeDistance
    - TailVisible
    - TextColor3
    - TextSize
    - VerticalStudsOffset
  BubbleChatMessageProperties:
    superclass: TextChatMessageProperties
    events: []
    properties:
    - BackgroundColor3
    - BackgroundTransparency
    - FontFace
    - TailVisible
    - TextColor3
    - TextSize
  BugReporterService:
    superclass: Instance
    events:
    - BugReportRequested
    properties: []
  BulkImportService:
    superclass: Instance
    events:
    - AssetImported
    - BulkImportFinished
    - BulkImportStarted
    properties: []
  BuoyancySensor:
    superclass: SensorBase
    events: []
    properties:
    - FullySubmerged
    - TouchingSurface
  CFrameValue:
    superclass: ValueBase
    events:
    - Changed
    - changed
    properties:
    - Value
  CSGDictionaryService:
    superclass: FlyweightService
    events: []
    properties: []
  CacheableContentProvider:
    superclass: Instance
    events: []
    properties: []
  CalloutService:
    superclass: Instance
    events: []
    properties: []
  Camera:
    superclass: PVInstance
    events:
    - FirstPersonTransition
    - InterpolationFinished
    properties:
    - CFrame
    - CameraSubject
    - CameraType
    - CoordinateFrame
    - DiagonalFieldOfView
    - FieldOfView
    - FieldOfViewMode
    - Focus
    - HeadLocked
    - HeadScale
    - MaxAxisFieldOfView
    - NearPlaneZ
    - VRTiltAndRollEnabled
    - ViewportSize
    - focus
  CanvasGroup:
    superclass: GuiObject
    events: []
    properties:
    - GroupColor3
    - GroupTransparency
    - ResolutionScale
  Capture:
    superclass: Object
    events: []
    properties: []
  CaptureService:
    superclass: Instance
    events:
    - CaptureBegan
    - CaptureEnded
    - CaptureSaved
    - CaptureSavedInternal
    - OpenSaveCapturesPrompt
    - OpenShareCapturePrompt
    - UserCaptureSaved
    properties: []
  CatalogPages:
    superclass: Pages
    events: []
    properties: []
  ChangeHistoryService:
    superclass: Instance
    events:
    - OnRecordingFinished
    - OnRecordingStarted
    - OnRedo
    - OnUndo
    properties: []
  ChannelSelectorSoundEffect:
    superclass: CustomSoundEffect
    events: []
    properties:
    - Channel
  ChannelTabsConfiguration:
    superclass: TextChatConfigurations
    events: []
    properties:
    - AbsolutePosition
    - AbsoluteSize
    - BackgroundColor3
    - BackgroundTransparency
    - Enabled
    - FontFace
    - HoverBackgroundColor3
    - SelectedTabTextColor3
    - TextColor3
    - TextSize
    - TextStrokeColor3
    - TextStrokeTransparency
  CharacterAppearance:
    superclass: Instance
    events: []
    properties: []
  CharacterMesh:
    superclass: CharacterAppearance
    events: []
    properties:
    - BaseTextureId
    - BodyPart
    - MeshId
    - OverlayTextureId
  Chat:
    superclass: Instance
    events:
    - BubbleChatSettingsChanged
    - Chatted
    - TimeoutChatAttempt
    properties:
    - BubbleChatEnabled
    - IsAutoMigrated
    - LoadDefaultChat
  ChatInputBarConfiguration:
    superclass: TextChatConfigurations
    events: []
    properties:
    - AbsolutePosition
    - AbsolutePositionWrite
    - AbsoluteSize
    - AbsoluteSizeWrite
    - AutocompleteEnabled
    - BackgroundColor3
    - BackgroundTransparency
    - Enabled
    - FontFace
    - IsFocused
    - IsFocusedWrite
    - KeyboardKeyCode
    - PlaceholderColor3
    - TargetTextChannel
    - TextBox
    - TextColor3
    - TextSize
    - TextStrokeColor3
    - TextStrokeTransparency
  ChatWindowConfiguration:
    superclass: TextChatConfigurations
    events: []
    properties:
    - AbsolutePosition
    - AbsolutePositionWrite
    - AbsoluteSize
    - AbsoluteSizeWrite
    - BackgroundColor3
    - BackgroundTransparency
    - Enabled
    - FontFace
    - HeightScale
    - HorizontalAlignment
    - TextColor3
    - TextSize
    - TextStrokeColor3
    - TextStrokeTransparency
    - VerticalAlignment
    - WidthScale
  ChatWindowMessageProperties:
    superclass: TextChatMessageProperties
    events: []
    properties:
    - FontFace
    - PrefixTextProperties
    - TextColor3
    - TextSize
    - TextStrokeColor3
    - TextStrokeTransparency
  ChatbotUIService:
    superclass: Instance
    events:
    - ActionActivatedSignal
    - FindVariationsSignal
    - SetMaterialSettingsSignal
    - SettingChangedSignal
    - ShiftToAssetIdSignal
    - ShiftVariationSignal
    properties: []
  ChorusSoundEffect:
    superclass: SoundEffect
    events: []
    properties:
    - Depth
    - Mix
    - Rate
  ClickDetector:
    superclass: Instance
    events:
    - MouseClick
    - MouseHoverEnter
    - MouseHoverLeave
    - RightMouseClick
    - mouseClick
    properties:
    - CursorIcon
    - MaxActivationDistance
  ClientReplicator:
    superclass: NetworkReplicator
    events:
    - RCCProfilerDataComplete
    - StatsReceived
    properties: []
  ClimbController:
    superclass: ControllerBase
    events: []
    properties:
    - AccelerationTime
    - BalanceMaxTorque
    - BalanceSpeed
    - MoveMaxForce
  Clothing:
    superclass: CharacterAppearance
    events: []
    properties:
    - Color3
  CloudCRUDService:
    superclass: Instance
    events: []
    properties: []
  CloudLocalizationTable:
    superclass: LocalizationTable
    events: []
    properties: []
  Clouds:
    superclass: Instance
    events: []
    properties:
    - Color
    - Cover
    - Density
    - Enabled
  ClusterPacketCache:
    superclass: Instance
    events: []
    properties: []
  Collaborator:
    superclass: Instance
    events: []
    properties:
    - CFrame
    - CollaboratorColor
    - CollaboratorColor3
    - CurDocGUID
    - CurScriptLineNumber
    - IsIdle
    - Status
    - UserId
    - Username
  CollaboratorsService:
    superclass: Instance
    events:
    - CollaboratorIdleUpdate
    - CollaboratorInstanceCreatedSignal
    - CollaboratorInstanceDestroyedSignal
    - CollaboratorStatusUpdatedSignal
    properties: []
  CollectionService:
    superclass: Instance
    events:
    - ItemAdded
    - ItemRemoved
    - TagAdded
    - TagRemoved
    properties: []
  Color3Value:
    superclass: ValueBase
    events:
    - Changed
    - changed
    properties:
    - Value
  ColorCorrectionEffect:
    superclass: PostEffect
    events: []
    properties:
    - Brightness
    - Contrast
    - Saturation
    - TintColor
  ColorGradingEffect:
    superclass: PostEffect
    events: []
    properties:
    - TonemapperPreset
  CommandInstance:
    superclass: Instance
    events: []
    properties:
    - AllowGUIAccessPoints
    - Checked
    - DefaultShortcut
    - DisplayName
    - Enabled
    - Icon
    - Name
    - Permission
    - StatusTip
  CommandService:
    superclass: Instance
    events:
    - CommandExecuting
    properties: []
  CommerceService:
    superclass: Instance
    events:
    - BenefitStatusReceived
    - FetchReceipt
    - InExperienceBrowserRequested
    - PromptCommerceProductPurchaseFinished
    - PromptCommerceProductPurchaseRequested
    - PurchaseBrowserClosed
    properties: []
  CompressorSoundEffect:
    superclass: SoundEffect
    events: []
    properties:
    - Attack
    - GainMakeup
    - Ratio
    - Release
    - SideChain
    - Threshold
  ConeHandleAdornment:
    superclass: HandleAdornment
    events: []
    properties:
    - Height
    - Radius
  ConfigService:
    superclass: Instance
    events: []
    properties: []
  ConfigSnapshot:
    superclass: Object
    events:
    - UpdateAvailable
    properties:
    - Error
    - Outdated
  Configuration:
    superclass: Instance
    events: []
    properties: []
  ConfigureServerService:
    superclass: Instance
    events: []
    properties: []
  ConnectivityService:
    superclass: Instance
    events: []
    properties:
    - NetworkStatus
  Constraint:
    superclass: Instance
    events: []
    properties:
    - Active
    - Attachment0
    - Attachment1
    - Color
    - Enabled
    - Visible
  ContentProvider:
    superclass: Instance
    events:
    - AssetFetchFailed
    properties:
    - BaseUrl
    - RequestQueueSize
  ContextActionService:
    superclass: Instance
    events:
    - BoundActionAdded
    - BoundActionChanged
    - BoundActionRemoved
    - GetActionButtonEvent
    - LocalToolEquipped
    - LocalToolUnequipped
    properties: []
  Controller:
    superclass: Instance
    events:
    - ButtonChanged
    properties: []
  ControllerBase:
    superclass: Instance
    events: []
    properties:
    - Active
    - BalanceRigidityEnabled
    - MoveSpeedFactor
  ControllerManager:
    superclass: Instance
    events: []
    properties:
    - ActiveController
    - BaseMoveSpeed
    - BaseTurnSpeed
    - ClimbSensor
    - FacingDirection
    - GroundSensor
    - MovingDirection
    - RootPart
    - UpDirection
  ControllerPartSensor:
    superclass: ControllerSensor
    events: []
    properties:
    - HitFrame
    - HitNormal
    - SearchDistance
    - SensedPart
    - SensorMode
  ControllerSensor:
    superclass: SensorBase
    events: []
    properties: []
  ControllerService:
    superclass: Instance
    events: []
    properties: []
  ConversationalAIAcceptanceService:
    superclass: Instance
    events: []
    properties: []
  CookiesService:
    superclass: Instance
    events: []
    properties: []
  CoreGui:
    superclass: BasePlayerGui
    events:
    - UserGuiRenderingChanged
    properties:
    - SelectionImageObject
    - Version
  CorePackages:
    superclass: Instance
    events: []
    properties: []
  CoreScript:
    superclass: BaseScript
    events: []
    properties: []
  CoreScriptDebuggingManagerHelper:
    superclass: Instance
    events: []
    properties: []
  CoreScriptSyncService:
    superclass: Instance
    events: []
    properties: []
  CornerWedgePart:
    superclass: BasePart
    events: []
    properties: []
  CreationDBService:
    superclass: Instance
    events: []
    properties: []
  CreatorStoreService:
    superclass: Instance
    events: []
    properties: []
  CrossDMScriptChangeListener:
    superclass: Instance
    events:
    - GuidLineContentsChanged
    - GuidNameChanged
    properties: []
  CurveAnimation:
    superclass: AnimationClip
    events: []
    properties: []
  CustomEvent:
    superclass: Instance
    events:
    - ReceiverConnected
    - ReceiverDisconnected
    properties: []
  CustomEventReceiver:
    superclass: Instance
    events:
    - EventConnected
    - EventDisconnected
    - SourceValueChanged
    properties:
    - Source
  CustomLog:
    superclass: Instance
    events: []
    properties: []
  CustomSoundEffect:
    superclass: SoundEffect
    events: []
    properties: []
  CylinderHandleAdornment:
    superclass: HandleAdornment
    events: []
    properties:
    - Angle
    - Height
    - InnerRadius
    - Radius
  CylinderMesh:
    superclass: BevelMesh
    events: []
    properties: []
  CylindricalConstraint:
    superclass: SlidingBallConstraint
    events: []
    properties:
    - AngularActuatorType
    - AngularLimitsEnabled
    - AngularResponsiveness
    - AngularRestitution
    - AngularSpeed
    - AngularVelocity
    - CurrentAngle
    - InclinationAngle
    - LowerAngle
    - MotorMaxAngularAcceleration
    - MotorMaxTorque
    - RotationAxisVisible
    - ServoMaxTorque
    - SoftlockAngularServoUponReachingTarget
    - TargetAngle
    - UpperAngle
    - WorldRotationAxis
  DataModel:
    superclass: ServiceProvider
    events:
    - AllowedGearTypeChanged
    - GraphicsQualityChangeRequest
    - ItemChanged
    - Loaded
    - ScreenshotReady
    - ScreenshotSavedToAlbum
    - UniverseMetadataLoaded
    properties:
    - CreatorId
    - CreatorType
    - GameId
    - GearGenreSetting
    - Genre
    - IsSFFlagsLoaded
    - JobId
    - MatchmakingType
    - PlaceId
    - PlaceVersion
    - PrivateServerId
    - PrivateServerOwnerId
    - VIPServerId
    - VIPServerOwnerId
    - Workspace
    - lighting
    - workspace
  DataModelMesh:
    superclass: Instance
    events: []
    properties:
    - Offset
    - Scale
    - VertexColor
  DataModelPatchService:
    superclass: Instance
    events: []
    properties: []
  DataModelSession:
    superclass: Instance
    events:
    - CurrentDataModelTypeAboutToChange
    - CurrentDataModelTypeChanged
    properties:
    - CurrentDataModelType
    - SessionId
  DataStore:
    superclass: GlobalDataStore
    events: []
    properties: []
  DataStoreGetOptions:
    superclass: Instance
    events: []
    properties:
    - UseCache
  DataStoreIncrementOptions:
    superclass: Instance
    events: []
    properties: []
  DataStoreInfo:
    superclass: Instance
    events: []
    properties:
    - CreatedTime
    - DataStoreName
    - UpdatedTime
  DataStoreKey:
    superclass: Instance
    events: []
    properties:
    - KeyName
  DataStoreKeyInfo:
    superclass: Instance
    events: []
    properties:
    - CreatedTime
    - UpdatedTime
    - Version
  DataStoreKeyPages:
    superclass: Pages
    events: []
    properties:
    - Cursor
  DataStoreListingPages:
    superclass: Pages
    events: []
    properties:
    - Cursor
  DataStoreObjectVersionInfo:
    superclass: Instance
    events: []
    properties:
    - CreatedTime
    - IsDeleted
    - Version
  DataStoreOptions:
    superclass: Instance
    events: []
    properties:
    - AllScopes
  DataStorePages:
    superclass: Pages
    events: []
    properties: []
  DataStoreService:
    superclass: Instance
    events: []
    properties:
    - AutomaticRetry
    - LegacyNamingScheme
  DataStoreSetOptions:
    superclass: Instance
    events: []
    properties: []
  DataStoreVersionPages:
    superclass: Pages
    events: []
    properties: []
  Debris:
    superclass: Instance
    events: []
    properties:
    - MaxItems
  DebugSettings:
    superclass: Instance
    events: []
    properties:
    - DataModel
    - InstanceCount
    - IsScriptStackTracingEnabled
    - JobCount
    - PlayerCount
    - ReportSoundWarnings
    - RobloxVersion
    - TickCountPreciseOverride
  DebuggablePluginWatcher:
    superclass: Instance
    events: []
    properties: []
  DebuggerBreakpoint:
    superclass: Instance
    events: []
    properties:
    - Condition
    - ContinueExecution
    - IsEnabled
    - Line
    - LogExpression
    - isContextDependentBreakpoint
  DebuggerConnection:
    superclass: Instance
    events:
    - BreakpointAdded
    - BreakpointChanged
    - BreakpointRemoved
    - Paused
    - Resumed
    properties:
    - ErrorMessage
    - HasError
    - Id
    - IsPaused
  DebuggerConnectionManager:
    superclass: Instance
    events:
    - ConnectionEnded
    - ConnectionStarted
    - FocusChanged
    properties:
    - Timeout
  DebuggerLuaResponse:
    superclass: Instance
    events: []
    properties:
    - IsError
    - IsSuccess
    - Message
    - RequestId
    - Status
  DebuggerManager:
    superclass: Instance
    events:
    - DebuggerAdded
    - DebuggerRemoved
    properties:
    - DebuggingEnabled
  DebuggerUIService:
    superclass: Instance
    events:
    - ExpressionAdded
    - ExpressionsCleared
    properties: []
  DebuggerVariable:
    superclass: Instance
    events: []
    properties:
    - Name
    - Populated
    - Type
    - Value
    - VariableId
    - VariablesCount
  DebuggerWatch:
    superclass: Instance
    events: []
    properties:
    - Expression
  Decal:
    superclass: FaceInstance
    events: []
    properties:
    - Color3
    - LocalTransparencyModifier
    - Shiny
    - Specular
    - Texture
    - TextureContent
    - Transparency
    - ZIndex
  DepthOfFieldEffect:
    superclass: PostEffect
    events: []
    properties:
    - FarIntensity
    - FocusDistance
    - InFocusRadius
    - NearIntensity
  DeviceIdService:
    superclass: Instance
    events: []
    properties: []
  Dialog:
    superclass: Instance
    events:
    - DialogChoiceSelected
    properties:
    - BehaviorType
    - ConversationDistance
    - GoodbyeChoiceActive
    - GoodbyeDialog
    - InUse
    - InitialPrompt
    - Purpose
    - Tone
    - TriggerDistance
    - TriggerOffset
  DialogChoice:
    superclass: Instance
    events: []
    properties:
    - GoodbyeChoiceActive
    - GoodbyeDialog
    - ResponseDialog
    - UserDialog
  DistortionSoundEffect:
    superclass: SoundEffect
    events: []
    properties:
    - Level
  DockWidgetPluginGui:
    superclass: PluginGui
    events: []
    properties:
    - HostWidgetWasRestored
  DoubleConstrainedValue:
    superclass: ValueBase
    events:
    - Changed
    - changed
    properties:
    - ConstrainedValue
    - MaxValue
    - MinValue
    - Value
  DraftsService:
    superclass: Instance
    events:
    - CommitStatusChanged
    - DraftAdded
    - DraftRemoved
    - DraftStatusChanged
    - EditorsListChanged
    - UpdateStatusChanged
    properties: []
  DragDetector:
    superclass: ClickDetector
    events:
    - DragContinue
    - DragEnd
    - DragStart
    properties:
    - ActivatedCursorIcon
    - ApplyAtCenterOfMass
    - Axis
    - DragFrame
    - DragStyle
    - Enabled
    - GamepadModeSwitchKeyCode
    - KeyboardModeSwitchKeyCode
    - MaxDragAngle
    - MaxDragTranslation
    - MaxForce
    - MaxTorque
    - MinDragAngle
    - MinDragTranslation
    - Orientation
    - PermissionPolicy
    - ReferenceInstance
    - ResponseStyle
    - Responsiveness
    - RunLocally
    - SecondaryAxis
    - TrackballRadialPullFactor
    - TrackballRollFactor
    - VRSwitchKeyCode
    - WorldAxis
    - WorldSecondaryAxis
  Dragger:
    superclass: Instance
    events: []
    properties: []
  DraggerService:
    superclass: Instance
    events: []
    properties:
    - AlignDraggedObjects
    - AngleSnapEnabled
    - AngleSnapIncrement
    - AnimateHover
    - CollisionsEnabled
    - DraggerCoordinateSpace
    - DraggerMovementMode
    - GeometrySnapColor
    - HoverAnimateFrequency
    - HoverLineThickness
    - HoverThickness
    - JointsEnabled
    - LinearSnapEnabled
    - LinearSnapIncrement
    - PartSnapEnabled
    - PivotSnapToGeometry
    - ShowHover
    - ShowPivotIndicator
  DynamicRotate:
    superclass: JointInstance
    events: []
    properties:
    - BaseAngle
  EchoSoundEffect:
    superclass: SoundEffect
    events: []
    properties:
    - Delay
    - DryLevel
    - Feedback
    - WetLevel
  EditableImage:
    superclass: Object
    events: []
    properties:
    - Size
  EditableMesh:
    superclass: Object
    events: []
    properties:
    - FixedSize
    - SkinningEnabled
  EditableService:
    superclass: Instance
    events: []
    properties: []
  EmotesPages:
    superclass: InventoryPages
    events: []
    properties: []
  EqualizerSoundEffect:
    superclass: SoundEffect
    events: []
    properties:
    - HighGain
    - LowGain
    - MidGain
  EulerRotationCurve:
    superclass: Instance
    events: []
    properties:
    - RotationOrder
  EventIngestService:
    superclass: Instance
    events: []
    properties: []
  ExampleService:
    superclass: Instance
    events:
    - OnPolo
    properties: []
  ExperienceAuthService:
    superclass: Instance
    events:
    - OpenAuthPrompt
    properties: []
  ExperienceInviteOptions:
    superclass: Instance
    events: []
    properties:
    - InviteMessageId
    - InviteUser
    - LaunchData
    - PromptMessage
  ExperienceNotificationService:
    superclass: Instance
    events:
    - OptInPromptClosed
    - PromptOptInRequested
    properties: []
  ExperienceService:
    superclass: Instance
    events:
    - OnCrossExperienceStarted
    - OnCrossExperienceStopped
    - OnNewJoinAttempt
    properties: []
  ExperienceStateCaptureService:
    superclass: Instance
    events:
    - ItemSelectedInCaptureMode
    properties:
    - HiddenSelectionEnabled
    - IsInBackground
    - IsInCaptureMode
  ExplorerFilter:
    superclass: Instance
    events: []
    properties: []
  ExplorerFilterAutocompleter:
    superclass: Instance
    events: []
    properties:
    - ReplaceRange
    - RequiresOutsideContext
  ExplorerServiceVisibilityService:
    superclass: Instance
    events: []
    properties: []
  Explosion:
    superclass: Instance
    events:
    - Hit
    properties:
    - BlastPressure
    - BlastRadius
    - DestroyJointRadiusPercent
    - ExplosionType
    - LocalTransparencyModifier
    - Position
    - TimeScale
    - Visible
  FaceAnimatorService:
    superclass: Instance
    events:
    - TrackerError
    - TrackerPrompt
    properties:
    - AudioAnimationEnabled
    - FaceTrackingStatusEnum
    - FlipHeadOrientation
    - VideoAnimationEnabled
  FaceControls:
    superclass: Instance
    events: []
    properties:
    - ChinRaiser
    - ChinRaiserUpperLip
    - Corrugator
    - EyesLookDown
    - EyesLookLeft
    - EyesLookRight
    - EyesLookUp
    - FlatPucker
    - Funneler
    - JawDrop
    - JawLeft
    - JawRight
    - LeftBrowLowerer
    - LeftCheekPuff
    - LeftCheekRaiser
    - LeftDimpler
    - LeftEyeClosed
    - LeftEyeUpperLidRaiser
    - LeftInnerBrowRaiser
    - LeftLipCornerDown
    - LeftLipCornerPuller
    - LeftLipStretcher
    - LeftLowerLipDepressor
    - LeftNoseWrinkler
    - LeftOuterBrowRaiser
    - LeftUpperLipRaiser
    - LipPresser
    - LipsTogether
    - LowerLipSuck
    - MouthLeft
    - MouthRight
    - Pucker
    - RightBrowLowerer
    - RightCheekPuff
    - RightCheekRaiser
    - RightDimpler
    - RightEyeClosed
    - RightEyeUpperLidRaiser
    - RightInnerBrowRaiser
    - RightLipCornerDown
    - RightLipCornerPuller
    - RightLipStretcher
    - RightLowerLipDepressor
    - RightNoseWrinkler
    - RightOuterBrowRaiser
    - RightUpperLipRaiser
    - TongueDown
    - TongueOut
    - TongueUp
    - UpperLipSuck
  FaceInstance:
    superclass: Instance
    events: []
    properties:
    - Face
  FacialAnimationRecordingService:
    superclass: Instance
    events: []
    properties:
    - BiometricDataConsent
  FacialAnimationStreamingServiceStats:
    superclass: Instance
    events: []
    properties: []
  FacialAnimationStreamingServiceV2:
    superclass: Instance
    events: []
    properties:
    - ServiceState
  FacialAnimationStreamingSubsessionStats:
    superclass: Instance
    events: []
    properties: []
  FacsImportData:
    superclass: BaseImportData
    events: []
    properties: []
  Feature:
    superclass: Instance
    events: []
    properties:
    - FaceId
    - InOut
    - LeftRight
    - TopBottom
  FeatureRestrictionManager:
    superclass: Instance
    events:
    - TimeoutChatAttempt
    properties: []
  File:
    superclass: Instance
    events: []
    properties:
    - Size
  FileMesh:
    superclass: DataModelMesh
    events: []
    properties:
    - MeshId
    - TextureId
  Fire:
    superclass: Instance
    events: []
    properties:
    - Color
    - Enabled
    - Heat
    - LocalTransparencyModifier
    - SecondaryColor
    - Size
    - TimeScale
    - size
  Flag:
    superclass: Tool
    events: []
    properties:
    - TeamColor
  FlagStand:
    superclass: Part
    events:
    - FlagCaptured
    properties:
    - TeamColor
  FlagStandService:
    superclass: Instance
    events: []
    properties: []
  FlangeSoundEffect:
    superclass: SoundEffect
    events: []
    properties:
    - Depth
    - Mix
    - Rate
  FloatCurve:
    superclass: Instance
    events: []
    properties:
    - Length
  FloorWire:
    superclass: GuiBase3d
    events: []
    properties:
    - CycleOffset
    - From
    - StudsBetweenTextures
    - Texture
    - TextureSize
    - To
    - Velocity
    - WireRadius
  FluidForceSensor:
    superclass: SensorBase
    events: []
    properties:
    - CenterOfPressure
    - Force
    - Torque
  FlyweightService:
    superclass: Instance
    events: []
    properties: []
  Folder:
    superclass: Instance
    events: []
    properties: []
  ForceField:
    superclass: Instance
    events: []
    properties:
    - Visible
  FormFactorPart:
    superclass: BasePart
    events: []
    properties:
    - FormFactor
    - formFactor
  Frame:
    superclass: GuiObject
    events: []
    properties:
    - Style
  FriendPages:
    superclass: Pages
    events: []
    properties: []
  FriendService:
    superclass: Instance
    events:
    - FriendsUpdated
    properties: []
  FunctionalTest:
    superclass: Instance
    events: []
    properties:
    - Description
  GamePassService:
    superclass: Instance
    events: []
    properties: []
  GameSettings:
    superclass: Instance
    events:
    - VideoRecordingChangeRequest
    properties:
    - VideoCaptureEnabled
    - VideoRecording
  GamepadService:
    superclass: Instance
    events:
    - GamepadThumbstick1Changed
    properties:
    - GamepadCursorEnabled
  GenerationService:
    superclass: Instance
    events: []
    properties: []
  GenericChallengeService:
    superclass: Instance
    events:
    - ChallengeAbandonedEvent
    - ChallengeCompletedEvent
    - ChallengeInvalidatedEvent
    - ChallengeLoadedEvent
    - ChallengeRequiredEvent
    properties: []
  GenericSettings:
    superclass: ServiceProvider
    events: []
    properties: []
  Geometry:
    superclass: Instance
    events: []
    properties: []
  GeometryService:
    superclass: Instance
    events: []
    properties: []
  GetTextBoundsParams:
    superclass: Instance
    events: []
    properties:
    - Font
    - RichText
    - Size
    - Text
    - Width
  GlobalDataStore:
    superclass: Instance
    events: []
    properties: []
  GlobalSettings:
    superclass: GenericSettings
    events: []
    properties: []
  Glue:
    superclass: JointInstance
    events: []
    properties:
    - F0
    - F1
    - F2
    - F3
  GoogleAnalyticsConfiguration:
    superclass: Instance
    events: []
    properties: []
  GroundController:
    superclass: ControllerBase
    events: []
    properties:
    - AccelerationLean
    - AccelerationTime
    - BalanceMaxTorque
    - BalanceSpeed
    - DecelerationTime
    - Friction
    - FrictionWeight
    - GroundOffset
    - StandForce
    - StandSpeed
    - TurnSpeedFactor
  GroupImportData:
    superclass: BaseImportData
    events: []
    properties:
    - Anchored
    - ImportAsModelAsset
    - InsertInWorkspace
  GroupService:
    superclass: Instance
    events: []
    properties: []
  GuiBase:
    superclass: Instance
    events: []
    properties: []
  GuiBase2d:
    superclass: GuiBase
    events:
    - SelectionChanged
    properties:
    - AbsolutePosition
    - AbsoluteRotation
    - AbsoluteSize
    - AutoLocalize
    - ClippedRect
    - IsNotOccluded
    - Localize
    - RawRect2D
    - RootLocalizationTable
    - SelectionBehaviorDown
    - SelectionBehaviorLeft
    - SelectionBehaviorRight
    - SelectionBehaviorUp
    - SelectionGroup
    - TotalGroupScale
  GuiBase3d:
    superclass: GuiBase
    events: []
    properties:
    - Color
    - Color3
    - Transparency
    - Visible
  GuiButton:
    superclass: GuiObject
    events:
    - Activated
    - MouseButton1Click
    - MouseButton1Down
    - MouseButton1Up
    - MouseButton2Click
    - MouseButton2Down
    - MouseButton2Up
    properties:
    - AutoButtonColor
    - HoverHapticEffect
    - Modal
    - PressHapticEffect
    - Selected
    - Style
  GuiLabel:
    superclass: GuiObject
    events: []
    properties: []
  GuiMain:
    superclass: ScreenGui
    events: []
    properties: []
  GuiObject:
    superclass: GuiBase2d
    events:
    - DragBegin
    - DragStopped
    - InputBegan
    - InputChanged
    - InputEnded
    - MouseEnter
    - MouseLeave
    - MouseMoved
    - MouseWheelBackward
    - MouseWheelForward
    - SelectionGained
    - SelectionLost
    - TouchLongPress
    - TouchPan
    - TouchPinch
    - TouchRotate
    - TouchSwipe
    - TouchTap
    properties:
    - Active
    - AnchorPoint
    - AutomaticSize
    - BackgroundColor
    - BackgroundColor3
    - BackgroundTransparency
    - BorderColor
    - BorderColor3
    - BorderMode
    - BorderSizePixel
    - ClipsDescendants
    - Draggable
    - GuiState
    - Interactable
    - LayoutOrder
    - NextSelectionDown
    - NextSelectionLeft
    - NextSelectionRight
    - NextSelectionUp
    - Position
    - Rotation
    - Selectable
    - SelectionImageObject
    - SelectionOrder
    - SelectionRect2D
    - Size
    - SizeConstraint
    - Transparency
    - Visible
    - ZIndex
  GuiService:
    superclass: Instance
    events:
    - BrowserWindowClosed
    - CloseInspectMenuRequest
    - CoreGuiRenderOverflowed
    - EmotesMenuOpenChanged
    - ErrorMessageChanged
    - GuiVisibilityChangedSignal
    - InspectMenuEnabledChangedSignal
    - InspectPlayerFromHumanoidDescriptionRequest
    - InspectPlayerFromUserIdWithCtxRequest
    - KeyPressed
    - MenuClosed
    - MenuOpened
    - NativeClose
    - NetworkPausedEnabledChanged
    - Open9SliceEditor
    - OpenStyleEditor
    - PurchasePromptShown
    - SafeZoneOffsetsChanged
    - ShowLeaveConfirmation
    - SpecialKeyPressed
    - UiMessageChanged
    properties:
    - AutoSelectGuiEnabled
    - CoreEffectFolder
    - CoreGuiFolder
    - CoreGuiNavigationEnabled
    - GuiNavigationEnabled
    - IsModalDialog
    - IsWindows
    - MenuIsOpen
    - PreferredTextSize
    - PreferredTransparency
    - ReducedMotionEnabled
    - SelectedCoreObject
    - SelectedObject
    - TopbarInset
    - TouchControlsEnabled
  GuidRegistryService:
    superclass: Instance
    events: []
    properties: []
  HSRDataContentProvider:
    superclass: CacheableContentProvider
    events: []
    properties: []
  HandleAdornment:
    superclass: PVAdornment
    events:
    - MouseButton1Down
    - MouseButton1Up
    - MouseEnter
    - MouseLeave
    properties:
    - AdornCullingMode
    - AlwaysOnTop
    - CFrame
    - SizeRelativeOffset
    - ZIndex
  Handles:
    superclass: HandlesBase
    events:
    - MouseButton1Down
    - MouseButton1Up
    - MouseDrag
    - MouseEnter
    - MouseLeave
    properties:
    - Faces
    - Style
  HandlesBase:
    superclass: PartAdornment
    events: []
    properties: []
  HapticEffect:
    superclass: Instance
    events: []
    properties:
    - Looped
    - Position
    - Radius
    - Type
    - Waveform
  HapticService:
    superclass: Instance
    events: []
    properties: []
  Hat:
    superclass: Accoutrement
    events: []
    properties: []
  HeapProfilerService:
    superclass: Instance
    events:
    - OnNewData
    properties: []
  HeatmapService:
    superclass: Instance
    events: []
    properties: []
  HeightmapImporterService:
    superclass: Instance
    events:
    - ColormapHasUnknownPixels
    - ProgressUpdate
    properties: []
  HiddenSurfaceRemovalAsset:
    superclass: Instance
    events: []
    properties: []
  Highlight:
    superclass: Instance
    events: []
    properties:
    - Adornee
    - DepthMode
    - Enabled
    - FillColor
    - FillTransparency
    - LineThickness
    - OutlineColor
    - OutlineTransparency
    - ReservedId
  HingeConstraint:
    superclass: Constraint
    events: []
    properties:
    - ActuatorType
    - AngularResponsiveness
    - AngularSpeed
    - AngularVelocity
    - CurrentAngle
    - LimitsEnabled
    - LowerAngle
    - MotorMaxAcceleration
    - MotorMaxTorque
    - Radius
    - Restitution
    - ServoMaxTorque
    - SoftlockServoUponReachingTarget
    - TargetAngle
    - UpperAngle
  Hint:
    superclass: Message
    events: []
    properties: []
  Hole:
    superclass: Feature
    events: []
    properties: []
  Hopper:
    superclass: Instance
    events: []
    properties: []
  HopperBin:
    superclass: BackpackItem
    events:
    - Deselected
    - Selected
    properties:
    - Active
    - BinType
  HttpRbxApiService:
    superclass: Instance
    events: []
    properties: []
  HttpRequest:
    superclass: Instance
    events: []
    properties: []
  HttpService:
    superclass: Instance
    events: []
    properties:
    - HttpEnabled
  Humanoid:
    superclass: Instance
    events:
    - AnimationPlayed
    - ApplyDescriptionFinished
    - Climbing
    - ClusterCompositionFinished
    - CustomStatusAdded
    - CustomStatusRemoved
    - Died
    - EmoteTriggered
    - FallingDown
    - FreeFalling
    - GettingUp
    - HealthChanged
    - Jumping
    - MoveToFinished
    - PlatformStanding
    - Ragdoll
    - Running
    - Seated
    - StateChanged
    - StateEnabledChanged
    - StatusAdded
    - StatusRemoved
    - Strafing
    - Swimming
    - Touched
    properties:
    - AutoJumpEnabled
    - AutoRotate
    - AutomaticScalingEnabled
    - BreakJointsOnDeath
    - CameraOffset
    - CollisionType
    - DisplayDistanceType
    - DisplayName
    - EvaluateStateMachine
    - FloorMaterial
    - Health
    - HealthDisplayDistance
    - HealthDisplayType
    - HipHeight
    - InternalDisplayName
    - Jump
    - JumpHeight
    - JumpPower
    - LeftLeg
    - MaxHealth
    - MaxSlopeAngle
    - MoveDirection
    - NameDisplayDistance
    - NameOcclusion
    - PlatformStand
    - RequiresNeck
    - RigType
    - RightLeg
    - RootPart
    - SeatPart
    - Sit
    - TargetPoint
    - Torso
    - UseJumpPower
    - WalkSpeed
    - WalkToPart
    - WalkToPoint
    - maxHealth
  HumanoidController:
    superclass: Controller
    events: []
    properties: []
  HumanoidDescription:
    superclass: Instance
    events:
    - EmotesChanged
    - EquippedEmotesChanged
    properties:
    - AccessoryBlob
    - BackAccessory
    - BodyTypeScale
    - ClimbAnimation
    - DepthScale
    - Face
    - FaceAccessory
    - FallAnimation
    - FrontAccessory
    - GraphicTShirt
    - HairAccessory
    - HatAccessory
    - Head
    - HeadColor
    - HeadScale
    - HeightScale
    - IdleAnimation
    - JumpAnimation
    - LeftArm
    - LeftArmColor
    - LeftLeg
    - LeftLegColor
    - MoodAnimation
    - NeckAccessory
    - NumberEmotesLoaded
    - Pants
    - ProportionScale
    - ResetIncludesBodyParts
    - RightArm
    - RightArmColor
    - RightLeg
    - RightLegColor
    - RunAnimation
    - Shirt
    - ShouldersAccessory
    - SwimAnimation
    - Torso
    - TorsoColor
    - WaistAccessory
    - WalkAnimation
    - WidthScale
  HumanoidRigDescription:
    superclass: Instance
    events: []
    properties:
    - Chest
    - ChestRangeMax
    - ChestRangeMin
    - ChestSize
    - ChestTposeAdjustment
    - Head
    - HeadRangeMax
    - HeadRangeMin
    - HeadSize
    - HeadTposeAdjustment
    - LeftAnkle
    - LeftAnkleRangeMax
    - LeftAnkleRangeMin
    - LeftAnkleSize
    - LeftAnkleTposeAdjustment
    - LeftClavicle
    - LeftClavicleRangeMax
    - LeftClavicleRangeMin
    - LeftClavicleSize
    - LeftClavicleTposeAdjustment
    - LeftElbow
    - LeftElbowRangeMax
    - LeftElbowRangeMin
    - LeftElbowSize
    - LeftElbowTposeAdjustment
    - LeftHip
    - LeftHipRangeMax
    - LeftHipRangeMin
    - LeftHipSize
    - LeftHipTposeAdjustment
    - LeftKnee
    - LeftKneeRangeMax
    - LeftKneeRangeMin
    - LeftKneeSize
    - LeftKneeTposeAdjustment
    - LeftShoulder
    - LeftShoulderRangeMax
    - LeftShoulderRangeMin
    - LeftShoulderSize
    - LeftShoulderTposeAdjustment
    - LeftToes
    - LeftToesRangeMax
    - LeftToesRangeMin
    - LeftToesSize
    - LeftToesTposeAdjustment
    - LeftWrist
    - LeftWristRangeMax
    - LeftWristRangeMin
    - LeftWristSize
    - LeftWristTposeAdjustment
    - Neck
    - NeckRangeMax
    - NeckRangeMin
    - NeckSize
    - NeckTposeAdjustment
    - Pelvis
    - PelvisRangeMax
    - PelvisRangeMin
    - PelvisSize
    - PelvisTposeAdjustment
    - RightAnkle
    - RightAnkleRangeMax
    - RightAnkleRangeMin
    - RightAnkleSize
    - RightAnkleTposeAdjustment
    - RightClavicle
    - RightClavicleRangeMax
    - RightClavicleRangeMin
    - RightClavicleSize
    - RightClavicleTposeAdjustment
    - RightElbow
    - RightElbowRangeMax
    - RightElbowRangeMin
    - RightElbowSize
    - RightElbowTposeAdjustment
    - RightHip
    - RightHipRangeMax
    - RightHipRangeMin
    - RightHipSize
    - RightHipTposeAdjustment
    - RightKnee
    - RightKneeRangeMax
    - RightKneeRangeMin
    - RightKneeSize
    - RightKneeTposeAdjustment
    - RightShoulder
    - RightShoulderRangeMax
    - RightShoulderRangeMin
    - RightShoulderSize
    - RightShoulderTposeAdjustment
    - RightToes
    - RightToesRangeMax
    - RightToesRangeMin
    - RightToesSize
    - RightToesTposeAdjustment
    - RightWrist
    - RightWristRangeMax
    - RightWristRangeMin
    - RightWristSize
    - RightWristTposeAdjustment
    - Root
    - RootRangeMax
    - RootRangeMin
    - RootSize
    - RootTposeAdjustment
    - Waist
    - WaistRangeMax
    - WaistRangeMin
    - WaistSize
    - WaistTposeAdjustment
  IKControl:
    superclass: Instance
    events: []
    properties:
    - ChainRoot
    - Enabled
    - EndEffector
    - EndEffectorOffset
    - Offset
    - Pole
    - Priority
    - SmoothTime
    - Target
    - Type
    - Weight
  ILegacyStudioBridge:
    superclass: Instance
    events: []
    properties: []
  IXPService:
    superclass: Instance
    events:
    - OnBrowserTrackerLayerLoadingStatusChanged
    - OnUserLayerLoadingStatusChanged
    properties: []
  ImageButton:
    superclass: GuiButton
    events: []
    properties:
    - ContentImageSize
    - HoverImage
    - Image
    - ImageColor3
    - ImageContent
    - ImageRectOffset
    - ImageRectSize
    - ImageTransparency
    - IsLoaded
    - PressedImage
    - ResampleMode
    - ScaleType
    - SliceCenter
    - SliceScale
    - TileSize
  ImageHandleAdornment:
    superclass: HandleAdornment
    events: []
    properties:
    - Image
    - Size
  ImageLabel:
    superclass: GuiLabel
    events: []
    properties:
    - ContentImageSize
    - Image
    - ImageColor3
    - ImageContent
    - ImageRectOffset
    - ImageRectSize
    - ImageTransparency
    - IsLoaded
    - ResampleMode
    - ScaleType
    - SliceCenter
    - SliceScale
    - TileSize
  ImportSession:
    superclass: Instance
    events:
    - UploadComplete
    - UploadProgress
    properties: []
  IncrementalPatchBuilder:
    superclass: Instance
    events: []
    properties:
    - AddPathsToBundle
    - BuildDebouncePeriod
    - HighCompression
    - SerializePatch
    - ZstdCompression
  InputAction:
    superclass: Instance
    events:
    - Pressed
    - Released
    - StateChanged
    properties:
    - Enabled
    - Type
  InputBinding:
    superclass: Instance
    events: []
    properties:
    - Down
    - KeyCode
    - Left
    - PressedThreshold
    - ReleasedThreshold
    - Right
    - Scale
    - UIButton
    - Up
    - Vector2Scale
  InputContext:
    superclass: Instance
    events: []
    properties:
    - Enabled
    - Priority
    - Sink
  InputObject:
    superclass: Instance
    events: []
    properties:
    - Delta
    - KeyCode
    - Position
    - UserInputState
    - UserInputType
  InsertService:
    superclass: Instance
    events: []
    properties:
    - AllowClientInsertModels
    - AllowInsertFreeModels
  Instance:
    superclass: Object
    events:
    - AncestryChanged
    - AttributeChanged
    - ChildAdded
    - ChildRemoved
    - DescendantAdded
    - DescendantRemoving
    - Destroying
    - StyledPropertiesChanged
    - childAdded
    properties:
    - Archivable
    - Capabilities
    - DataCost
    - Name
    - Parent
    - RobloxLocked
    - Sandboxed
    - SourceAssetId
    - UniqueId
    - archivable
  InstanceAdornment:
    superclass: GuiBase3d
    events: []
    properties:
    - Adornee
  IntConstrainedValue:
    superclass: ValueBase
    events:
    - Changed
    - changed
    properties:
    - ConstrainedValue
    - MaxValue
    - MinValue
    - Value
  IntValue:
    superclass: ValueBase
    events:
    - Changed
    - changed
    properties:
    - Value
  IntersectOperation:
    superclass: PartOperation
    events: []
    properties: []
  InventoryPages:
    superclass: Pages
    events: []
    properties: []
  JointImportData:
    superclass: BaseImportData
    events: []
    properties: []
  JointInstance:
    superclass: Instance
    events: []
    properties:
    - Active
    - C0
    - C1
    - Enabled
    - Part0
    - Part1
    - part1
  JointsService:
    superclass: Instance
    events: []
    properties: []
  KeyboardService:
    superclass: Instance
    events: []
    properties: []
  Keyframe:
    superclass: Instance
    events: []
    properties:
    - Time
  KeyframeMarker:
    superclass: Instance
    events: []
    properties:
    - Value
  KeyframeSequence:
    superclass: AnimationClip
    events: []
    properties:
    - AuthoredHipHeight
  KeyframeSequenceProvider:
    superclass: Instance
    events: []
    properties: []
  LSPFileSyncService:
    superclass: Instance
    events: []
    properties: []
  LanguageService:
    superclass: Instance
    events: []
    properties: []
  LayerCollector:
    superclass: GuiBase2d
    events: []
    properties:
    - Enabled
    - ResetOnSpawn
    - ZIndexBehavior
  LegacyStudioBridge:
    superclass: ILegacyStudioBridge
    events: []
    properties: []
  Light:
    superclass: Instance
    events: []
    properties:
    - Brightness
    - Color
    - Enabled
    - Shadows
  Lighting:
    superclass: Instance
    events:
    - LightingChanged
    properties:
    - Ambient
    - Brightness
    - ClockTime
    - ColorShift_Bottom
    - ColorShift_Top
    - EnvironmentDiffuseScale
    - EnvironmentSpecularScale
    - ExposureCompensation
    - FogColor
    - FogEnd
    - FogStart
    - GeographicLatitude
    - GlobalShadows
    - LightingStyle
    - OutdoorAmbient
    - Outlines
    - PrioritizeLightingQuality
    - ShadowColor
    - ShadowSoftness
    - Technology
    - TimeOfDay
  LineForce:
    superclass: Constraint
    events: []
    properties:
    - ApplyAtCenterOfMass
    - InverseSquareLaw
    - Magnitude
    - MaxForce
    - ReactionForceEnabled
  LineHandleAdornment:
    superclass: HandleAdornment
    events: []
    properties:
    - Length
    - Thickness
  LinearVelocity:
    superclass: Constraint
    events: []
    properties:
    - ForceLimitMode
    - ForceLimitsEnabled
    - LineDirection
    - LineVelocity
    - MaxAxesForce
    - MaxForce
    - MaxPlanarAxesForce
    - PlaneVelocity
    - PrimaryTangentAxis
    - RelativeTo
    - SecondaryTangentAxis
    - VectorVelocity
    - VelocityConstraintMode
  LinkingService:
    superclass: Instance
    events:
    - OnLuaUrl
    properties: []
  LiveScriptingService:
    superclass: Instance
    events: []
    properties: []
  LiveSyncService:
    superclass: Instance
    events:
    - SyncStatusChanged
    properties:
    - HasSyncedInstances
  LocalDebuggerConnection:
    superclass: DebuggerConnection
    events: []
    properties: []
  LocalScript:
    superclass: Script
    events: []
    properties: []
  LocalStorageService:
    superclass: Instance
    events:
    - ItemWasSet
    - StoreWasCleared
    properties: []
  LocalizationService:
    superclass: Instance
    events:
    - AutoTranslateWillRun
    properties:
    - ForcePlayModeGameLocaleId
    - ForcePlayModeRobloxLocaleId
    - IsTextScraperRunning
    - RobloxForcePlayModeGameLocaleId
    - RobloxForcePlayModeRobloxLocaleId
    - RobloxLocaleId
    - SystemLocaleId
  LocalizationTable:
    superclass: Instance
    events: []
    properties:
    - DevelopmentLanguage
    - Root
    - SourceLocaleId
  LodDataEntity:
    superclass: Instance
    events: []
    properties:
    - EntityLodEnabled
  LodDataService:
    superclass: Instance
    events: []
    properties: []
  LogReporterService:
    superclass: Instance
    events: []
    properties: []
  LogService:
    superclass: Instance
    events:
    - HttpResultOut
    - MessageOut
    - OnHttpResultApproved
    - ServerHttpResultOut
    - ServerMessageOut
    properties: []
  LoginService:
    superclass: Instance
    events:
    - LoginFailed
    - LoginSucceeded
    properties: []
  LuaSettings:
    superclass: Instance
    events: []
    properties: []
  LuaSourceContainer:
    superclass: Instance
    events: []
    properties: []
  LuaWebService:
    superclass: Instance
    events: []
    properties: []
  LuauScriptAnalyzerService:
    superclass: Instance
    events: []
    properties: []
  MLModelDeliveryService:
    superclass: Instance
    events: []
    properties: []
  ManualGlue:
    superclass: ManualSurfaceJointInstance
    events: []
    properties: []
  ManualSurfaceJointInstance:
    superclass: JointInstance
    events: []
    properties: []
  ManualWeld:
    superclass: ManualSurfaceJointInstance
    events: []
    properties: []
  MarkerCurve:
    superclass: Instance
    events: []
    properties:
    - Length
  MarketplaceService:
    superclass: Instance
    events:
    - ClientLuaDialogRequested
    - ClientPurchaseSuccess
    - NativePurchaseFinished
    - NativePurchaseFinishedWithLocalPlayer
    - PrepareCollectiblesPurchaseRequested
    - PromptBulkPurchaseFinished
    - PromptBulkPurchaseRequested
    - PromptBundlePurchaseFinished
    - PromptBundlePurchaseRequested
    - PromptCancelSubscriptionRequested
    - PromptCollectibleBundlePurchaseRequested
    - PromptCollectiblesPurchaseRequested
    - PromptGamePassPurchaseFinished
    - PromptGamePassPurchaseRequested
    - PromptPremiumPurchaseFinished
    - PromptPremiumPurchaseRequested
    - PromptProductPurchaseFinished
    - PromptProductPurchaseRequested
    - PromptPurchaseFinished
    - PromptPurchaseRequested
    - PromptPurchaseRequestedV2
    - PromptRobloxPurchaseRequested
    - PromptSubscriptionPurchaseFinished
    - PromptSubscriptionPurchaseRequested
    - ServerPurchaseVerification
    - ThirdPartyPurchaseFinished
    - UserSubscriptionStatusChanged
    properties: []
  MatchmakingService:
    superclass: Instance
    events: []
    properties: []
  MaterialGenerationService:
    superclass: Instance
    events: []
    properties: []
  MaterialGenerationSession:
    superclass: Instance
    events: []
    properties: []
  MaterialImportData:
    superclass: BaseImportData
    events: []
    properties:
    - DiffuseFilePath
    - IsPbr
    - MetalnessFilePath
    - NormalFilePath
    - RoughnessFilePath
  MaterialService:
    superclass: Instance
    events:
    - MaterialFillToolEnabledChanged
    - OverrideStatusChanged
    properties:
    - AsphaltName
    - BasaltName
    - BrickName
    - CardboardName
    - CarpetName
    - CeramicTilesName
    - ClayRoofTilesName
    - CobblestoneName
    - ConcreteName
    - CorrodedMetalName
    - CrackedLavaName
    - DiamondPlateName
    - FabricName
    - FoilName
    - GlacierName
    - GraniteName
    - GrassName
    - GroundName
    - IceName
    - LeafyGrassName
    - LeatherName
    - LimestoneName
    - MarbleName
    - MetalName
    - MudName
    - PavementName
    - PebbleName
    - PlasterName
    - PlasticName
    - RockName
    - RoofShinglesName
    - RubberName
    - SaltName
    - SandName
    - SandstoneName
    - SlateName
    - SmoothPlasticName
    - SnowName
    - Use2022Materials
    - WoodName
    - WoodPlanksName
  MaterialVariant:
    superclass: Instance
    events: []
    properties:
    - BaseMaterial
    - ColorMap
    - CustomPhysicalProperties
    - MaterialPattern
    - MetalnessMap
    - NormalMap
    - RoughnessMap
    - StudsPerTile
  MemStorageConnection:
    superclass: Instance
    events: []
    properties: []
  MemStorageService:
    superclass: Instance
    events: []
    properties: []
  MemoryStoreHashMap:
    superclass: Instance
    events: []
    properties: []
  MemoryStoreHashMapPages:
    superclass: Pages
    events: []
    properties: []
  MemoryStoreQueue:
    superclass: Instance
    events: []
    properties: []
  MemoryStoreService:
    superclass: Instance
    events: []
    properties: []
  MemoryStoreSortedMap:
    superclass: Instance
    events: []
    properties: []
  MeshContentProvider:
    superclass: CacheableContentProvider
    events: []
    properties: []
  MeshImportData:
    superclass: BaseImportData
    events: []
    properties:
    - Anchored
    - CageManifold
    - CageMeshIntersectedPreview
    - CageMeshNotIntersected
    - CageNoOverlappingVertices
    - CageNonManifoldPreview
    - CageOverlappingVerticesPreview
    - CageUVMatched
    - CageUVMisMatchedPreview
    - Dimensions
    - DoubleSided
    - IgnoreVertexColors
    - IrrelevantCageModifiedPreview
    - MeshHoleDetectedPreview
    - MeshNoHoleDetected
    - NoIrrelevantCageModified
    - NoOuterCageFarExtendedFromMesh
    - OuterCageFarExtendedFromMeshPreview
    - PolygonCount
    - UseImportedPivot
  MeshPart:
    superclass: TriangleMeshPart
    events: []
    properties:
    - DoubleSided
    - HasJointOffset
    - HasSkinnedMesh
    - JointOffset
    - MeshContent
    - MeshId
    - RenderFidelity
    - TextureContent
    - TextureID
  Message:
    superclass: Instance
    events: []
    properties:
    - Text
  MessageBusConnection:
    superclass: Instance
    events: []
    properties: []
  MessageBusService:
    superclass: Instance
    events: []
    properties: []
  MessagingService:
    superclass: Instance
    events: []
    properties: []
  MetaBreakpoint:
    superclass: Instance
    events: []
    properties:
    - Condition
    - ContinueExecution
    - Enabled
    - Id
    - IsLogpoint
    - Line
    - LogMessage
    - RemoveOnHit
    - Script
    - Valid
  MetaBreakpointContext:
    superclass: Instance
    events: []
    properties: []
  MetaBreakpointManager:
    superclass: Instance
    events:
    - MetaBreakpointAdded
    - MetaBreakpointChanged
    - MetaBreakpointRemoved
    - MetaBreakpointSetChanged
    properties: []
  Model:
    superclass: PVInstance
    events: []
    properties:
    - LevelOfDetail
    - ModelStreamingMode
    - PrimaryPart
    - Scale
    - WorldPivot
  ModuleScript:
    superclass: LuaSourceContainer
    events: []
    properties:
    - LinkedSource
    - Source
  Motor:
    superclass: JointInstance
    events: []
    properties:
    - CurrentAngle
    - DesiredAngle
    - MaxVelocity
  Motor6D:
    superclass: Motor
    events: []
    properties:
    - ChildName
    - ParentName
    - Transform
  MotorFeature:
    superclass: Feature
    events: []
    properties: []
  Mouse:
    superclass: Instance
    events:
    - Button1Down
    - Button1Up
    - Button2Down
    - Button2Up
    - Idle
    - KeyDown
    - KeyUp
    - Move
    - WheelBackward
    - WheelForward
    - keyDown
    properties:
    - Hit
    - Icon
    - Origin
    - Target
    - TargetFilter
    - TargetSurface
    - UnitRay
    - ViewSizeX
    - ViewSizeY
    - X
    - Y
    - hit
    - target
  MouseService:
    superclass: Instance
    events:
    - MouseEnterStudioViewport
    - MouseLeaveStudioViewport
    properties: []
  MultipleDocumentInterfaceInstance:
    superclass: Instance
    events:
    - DataModelSessionEnded
    - DataModelSessionStarted
    properties:
    - FocusedDataModelSession
  NegateOperation:
    superclass: PartOperation
    events: []
    properties: []
  NetworkClient:
    superclass: NetworkPeer
    events:
    - ConnectionAccepted
    - ConnectionFailed
    properties: []
  NetworkMarker:
    superclass: Instance
    events:
    - Received
    properties: []
  NetworkPeer:
    superclass: Instance
    events: []
    properties: []
  NetworkReplicator:
    superclass: Instance
    events: []
    properties: []
  NetworkServer:
    superclass: NetworkPeer
    events: []
    properties: []
  NetworkSettings:
    superclass: Instance
    events: []
    properties:
    - EmulatedTotalMemoryInMB
    - FreeMemoryMBytes
    - HttpProxyEnabled
    - HttpProxyURL
    - IncomingReplicationLag
    - PrintJoinSizeBreakdown
    - PrintPhysicsErrors
    - PrintStreamInstanceQuota
    - RandomizeJoinInstanceOrder
    - RenderStreamedRegions
    - ShowActiveAnimationAsset
  NoCollisionConstraint:
    superclass: Instance
    events: []
    properties:
    - Enabled
    - Part0
    - Part1
  Noise:
    superclass: Instance
    events: []
    properties:
    - NoiseType
    - Seed
  NonReplicatedCSGDictionaryService:
    superclass: FlyweightService
    events: []
    properties: []
  NotificationService:
    superclass: Instance
    events:
    - Roblox17sConnectionChanged
    - Roblox17sEventReceived
    - RobloxConnectionChanged
    - RobloxEventReceived
    properties:
    - IsConnected
    - IsLuaChatEnabled
    - IsLuaGameDetailsEnabled
    - SelectedTheme
  NumberPose:
    superclass: PoseBase
    events: []
    properties:
    - Value
  NumberValue:
    superclass: ValueBase
    events:
    - Changed
    - changed
    properties:
    - Value
  Object:
    superclass: <<<ROOT>>>
    events:
    - Changed
    properties:
    - ClassName
    - className
  ObjectValue:
    superclass: ValueBase
    events:
    - Changed
    - changed
    properties:
    - Value
  OmniRecommendationsService:
    superclass: Instance
    events: []
    properties: []
  OpenCloudApiV1:
    superclass: Instance
    events: []
    properties: []
  OpenCloudService:
    superclass: Instance
    events: []
    properties: []
  OperationGraph:
    superclass: Instance
    events: []
    properties: []
  OrderedDataStore:
    superclass: GlobalDataStore
    events: []
    properties: []
  OutfitPages:
    superclass: Pages
    events: []
    properties: []
  PVAdornment:
    superclass: GuiBase3d
    events: []
    properties:
    - Adornee
  PVInstance:
    superclass: Instance
    events: []
    properties:
    - Origin
    - Pivot Offset
  PackageLink:
    superclass: Instance
    events: []
    properties:
    - AutoUpdate
    - Creator
    - DefaultName
    - HasNewVersion
    - ModifiedState
    - PackageAssetName
    - PackageId
    - PermissionLevel
    - SerializedDefaultAttributes
    - Status
    - VersionNumber
  PackageService:
    superclass: Instance
    events: []
    properties: []
  PackageUIService:
    superclass: Instance
    events:
    - OnConvertToPackageResult
    - OnOpenConvertToPackagePlugin
    properties: []
  Pages:
    superclass: Instance
    events: []
    properties:
    - IsFinished
  Pants:
    superclass: Clothing
    events: []
    properties:
    - PantsTemplate
  ParabolaAdornment:
    superclass: PVAdornment
    events: []
    properties:
    - A
    - B
    - C
    - Range
    - Thickness
  Part:
    superclass: FormFactorPart
    events: []
    properties:
    - Shape
  PartAdornment:
    superclass: GuiBase3d
    events: []
    properties:
    - Adornee
  PartOperation:
    superclass: TriangleMeshPart
    events: []
    properties:
    - RenderFidelity
    - SmoothingAngle
    - TriangleCount
    - UsePartColor
  PartOperationAsset:
    superclass: Instance
    events: []
    properties: []
  ParticleEmitter:
    superclass: Instance
    events: []
    properties:
    - Acceleration
    - Brightness
    - Color
    - Drag
    - EmissionDirection
    - Enabled
    - FlipbookFramerate
    - FlipbookIncompatible
    - FlipbookLayout
    - FlipbookMode
    - FlipbookStartRandom
    - Lifetime
    - LightEmission
    - LightInfluence
    - LocalTransparencyModifier
    - LockedToPart
    - Orientation
    - Rate
    - RotSpeed
    - Rotation
    - Shape
    - ShapeInOut
    - ShapePartial
    - ShapeStyle
    - Size
    - Speed
    - SpreadAngle
    - Squash
    - Texture
    - TimeScale
    - Transparency
    - VelocityInheritance
    - VelocitySpread
    - WindAffectsDrag
    - ZOffset
  PatchBundlerFileWatch:
    superclass: Instance
    events: []
    properties: []
  PatchMapping:
    superclass: Instance
    events: []
    properties:
    - FlattenTree
    - PatchId
    - TargetPath
  Path:
    superclass: Instance
    events:
    - Blocked
    - Unblocked
    properties:
    - Status
  Path2D:
    superclass: GuiBase
    events:
    - ControlPointChanged
    properties:
    - Closed
    - Color3
    - SelectedControlPoint
    - SelectedControlPointData
    - Thickness
    - Transparency
    - Visible
    - ZIndex
  PathfindingLink:
    superclass: Instance
    events: []
    properties:
    - Attachment0
    - Attachment1
    - IsBidirectional
    - Label
  PathfindingModifier:
    superclass: Instance
    events: []
    properties:
    - Label
    - PassThrough
  PathfindingService:
    superclass: Instance
    events: []
    properties:
    - EmptyCutoff
  PausedState:
    superclass: Instance
    events: []
    properties:
    - AllThreadsPaused
    - Reason
    - ThreadId
  PausedStateBreakpoint:
    superclass: PausedState
    events: []
    properties:
    - Breakpoint
  PausedStateException:
    superclass: PausedState
    events: []
    properties:
    - ExceptionText
  PerformanceControlService:
    superclass: Instance
    events: []
    properties: []
  PermissionsService:
    superclass: Instance
    events: []
    properties: []
  PhysicsService:
    superclass: Instance
    events: []
    properties: []
  PhysicsSettings:
    superclass: Instance
    events: []
    properties:
    - AllowSleep
    - AreAnchorsShown
    - AreAssembliesShown
    - AreAssemblyCentersOfMassShown
    - AreAwakePartsHighlighted
    - AreBodyTypesShown
    - AreCollisionCostsShown
    - AreConstraintForcesShownForSelectedOrHoveredInstances
    - AreConstraintTorquesShownForSelectedOrHoveredInstances
    - AreContactForcesShownForSelectedOrHoveredAssemblies
    - AreContactIslandsShown
    - AreContactPointsShown
    - AreGravityForcesShownForSelectedOrHoveredAssemblies
    - AreJointCoordinatesShown
    - AreMagnitudesShownForDrawnForcesAndTorques
    - AreMechanismsShown
    - AreModelCoordsShown
    - AreNonAnchorsShown
    - AreOwnersShown
    - ArePartCoordsShown
    - AreRegionsShown
    - AreSolverIslandsShown
    - AreTerrainReplicationRegionsShown
    - AreTimestepsShown
    - AreUnalignedPartsShown
    - AreWorldCoordsShown
    - DisableCSGv2
    - DisableCSGv3ForPlugins
    - DrawConstraintsNetForce
    - DrawContactsNetForce
    - DrawTotalNetForce
    - EnableForceVisualizationSmoothing
    - FluidForceDrawScale
    - ForceCSGv2
    - ForceDrawScale
    - ForceVisualizationSmoothingSteps
    - IsInterpolationThrottleShown
    - IsReceiveAgeShown
    - IsTreeShown
    - PhysicsEnvironmentalThrottle
    - ShowDecompositionGeometry
    - ShowFluidForcesForSelectedOrHoveredMechanisms
    - ShowInstanceNamesForDrawnForcesAndTorques
    - SolverConvergenceMetricType
    - SolverConvergenceVisualizationMode
    - ThrottleAdjustTime
    - TorqueDrawScale
    - UseCSGv2
  PitchShiftSoundEffect:
    superclass: SoundEffect
    events: []
    properties:
    - Octave
  PlaceStatsService:
    superclass: Instance
    events: []
    properties: []
  PlacesService:
    superclass: Instance
    events: []
    properties: []
  Plane:
    superclass: PlaneConstraint
    events: []
    properties: []
  PlaneConstraint:
    superclass: Constraint
    events: []
    properties: []
  Platform:
    superclass: Part
    events: []
    properties: []
  PlatformCloudStorageService:
    superclass: Instance
    events: []
    properties: []
  PlatformFriendsService:
    superclass: Instance
    events: []
    properties: []
  Player:
    superclass: Instance
    events:
    - CharacterAdded
    - CharacterAppearanceLoaded
    - CharacterRemoving
    - Chatted
    - FriendStatusChanged
    - Idled
    - OnTeleport
    - SimulationRadiusChanged
    properties:
    - AccountAge
    - AppearanceDidLoad
    - AutoJumpEnabled
    - CameraMaxZoomDistance
    - CameraMinZoomDistance
    - CameraMode
    - CanLoadCharacterAppearance
    - Character
    - CharacterAppearance
    - CharacterAppearanceId
    - ChatMode
    - DataComplexity
    - DataComplexityLimit
    - DataReady
    - DevCameraOcclusionMode
    - DevComputerCameraMode
    - DevComputerMovementMode
    - DevEnableMouseLock
    - DevTouchCameraMode
    - DevTouchMovementMode
    - DisplayName
    - FollowUserId
    - GameplayPaused
    - Guest
    - HasVerifiedBadge
    - HealthDisplayDistance
    - LocaleId
    - MaximumSimulationRadius
    - MembershipType
    - NameDisplayDistance
    - Neutral
    - OsPlatform
    - PartyId
    - PlatformName
    - ReplicationFocus
    - RespawnLocation
    - SimulationRadius
    - StepIdOffset
    - Team
    - TeamColor
    - Teleported
    - TeleportedIn
    - ThirdPartyTextChatRestrictionStatus
    - UnfilteredChat
    - UserId
    - VRDevice
    - VREnabled
    - userId
  PlayerData:
    superclass: Instance
    events: []
    properties: []
  PlayerDataRecord:
    superclass: Instance
    events:
    - Changed
    - Flushed
    - Loaded
    properties:
    - CreatedTime
    - DefaultRecordName
    - Dirty
    - Error
    - FlushedTime
    - LoadedTime
    - ModifiedTime
    - NewRecord
    - Readable
    - RecordName
    - Writable
  PlayerDataRecordConfig:
    superclass: Instance
    events: []
    properties:
    - RecordName
  PlayerDataService:
    superclass: Instance
    events: []
    properties:
    - LoadFailureBehavior
  PlayerEmulatorService:
    superclass: Instance
    events: []
    properties:
    - CustomPoliciesEnabled
    - EmulatedCountryCode
    - EmulatedGameLocale
    - PlayerEmulationEnabled
    - PseudolocalizationEnabled
    - SerializedEmulatedPolicyInfo
    - TextElongationFactor
  PlayerGui:
    superclass: BasePlayerGui
    events:
    - TopbarTransparencyChangedSignal
    properties:
    - CurrentScreenOrientation
    - ScreenOrientation
    - SelectionImageObject
  PlayerHydrationService:
    superclass: Instance
    events: []
    properties: []
  PlayerMouse:
    superclass: Mouse
    events: []
    properties: []
  PlayerScripts:
    superclass: Instance
    events:
    - ComputerCameraMovementModeRegistered
    - ComputerMovementModeRegistered
    - TouchCameraMovementModeRegistered
    - TouchMovementModeRegistered
    properties: []
  PlayerViewService:
    superclass: Instance
    events: []
    properties: []
  Players:
    superclass: Instance
    events:
    - FriendRequestEvent
    - GameAnnounce
    - PlayerAdded
    - PlayerChatted
    - PlayerConnecting
    - PlayerDisconnecting
    - PlayerMembershipChanged
    - PlayerRejoining
    - PlayerRemoving
    - UserSubscriptionStatusChanged
    properties:
    - BanningEnabled
    - BubbleChat
    - CharacterAutoLoads
    - ClassicChat
    - LocalPlayer
    - MaxPlayers
    - MaxPlayersInternal
    - NumPlayers
    - PreferredPlayers
    - PreferredPlayersInternal
    - RespawnTime
    - UseStrafingAnimations
    - localPlayer
    - numPlayers
  Plugin:
    superclass: Instance
    events:
    - Deactivation
    - Ready
    - Unloading
    properties:
    - CollisionEnabled
    - DisableUIDragDetectorDrags
    - GridSize
    - HostDataModelType
    - HostDataModelTypeIsCurrent
    - IsDebuggable
    - MultipleDocumentInterfaceInstance
    - UsesAssetInsertionDrag
  PluginAction:
    superclass: Instance
    events:
    - Triggered
    properties:
    - ActionId
    - AllowBinding
    - Checked
    - DefaultShortcut
    - Enabled
    - StatusTip
    - Text
  PluginCapabilities:
    superclass: Instance
    events: []
    properties:
    - Manifest
  PluginDebugService:
    superclass: Instance
    events: []
    properties: []
  PluginDragEvent:
    superclass: Instance
    events: []
    properties:
    - Data
    - MimeType
    - Position
    - Sender
  PluginGui:
    superclass: LayerCollector
    events:
    - InputBegan
    - InputChanged
    - InputEnded
    - MouseEnter
    - MouseLeave
    - PluginDragDropped
    - PluginDragEntered
    - PluginDragLeft
    - PluginDragMoved
    - WindowFocusReleased
    - WindowFocused
    properties:
    - Title
  PluginGuiService:
    superclass: Instance
    events: []
    properties: []
  PluginManagementService:
    superclass: Instance
    events: []
    properties: []
  PluginManager:
    superclass: Instance
    events: []
    properties: []
  PluginManagerInterface:
    superclass: Instance
    events: []
    properties: []
  PluginMenu:
    superclass: Instance
    events: []
    properties:
    - Icon
    - Title
  PluginMouse:
    superclass: Mouse
    events:
    - DragEnter
    properties: []
  PluginPolicyService:
    superclass: Instance
    events: []
    properties: []
  PluginToolbar:
    superclass: Instance
    events: []
    properties: []
  PluginToolbarButton:
    superclass: Instance
    events:
    - Click
    - DropdownClick
    properties:
    - ClickableWhenViewportHidden
    - Enabled
    - Icon
  PointLight:
    superclass: Light
    events: []
    properties:
    - Range
  PointsService:
    superclass: Instance
    events:
    - PointsAwarded
    properties: []
  PolicyService:
    superclass: Instance
    events: []
    properties:
    - IsLuobuServer
    - LuobuWhitelisted
  Pose:
    superclass: PoseBase
    events: []
    properties:
    - CFrame
    - MaskWeight
  PoseBase:
    superclass: Instance
    events: []
    properties:
    - EasingDirection
    - EasingStyle
    - Weight
  PostEffect:
    superclass: Instance
    events: []
    properties:
    - Enabled
  PrismaticConstraint:
    superclass: SlidingBallConstraint
    events: []
    properties: []
  ProcessInstancePhysicsService:
    superclass: Instance
    events: []
    properties: []
  ProximityPrompt:
    superclass: Instance
    events:
    - PromptButtonHoldBegan
    - PromptButtonHoldEnded
    - PromptHidden
    - PromptShown
    - TriggerEnded
    - Triggered
    properties:
    - ActionText
    - AutoLocalize
    - ClickablePrompt
    - Enabled
    - Exclusivity
    - GamepadKeyCode
    - HoldDuration
    - KeyboardKeyCode
    - MaxActivationDistance
    - ObjectText
    - RequiresLineOfSight
    - RootLocalizationTable
    - Style
    - UIOffset
  ProximityPromptService:
    superclass: Instance
    events:
    - PromptButtonHoldBegan
    - PromptButtonHoldEnded
    - PromptHidden
    - PromptShown
    - PromptTriggerEnded
    - PromptTriggered
    properties:
    - Enabled
    - MaxPromptsVisible
  PublishService:
    superclass: Instance
    events: []
    properties: []
  QWidgetPluginGui:
    superclass: PluginGui
    events: []
    properties: []
  RTAnimationTracker:
    superclass: Instance
    events:
    - TrackerError
    - TrackerPrompt
    properties:
    - Active
    - EnableFallbackAudioInput
    - SessionName
    - TrackerMode
    - TrackerType
  RayValue:
    superclass: ValueBase
    events:
    - Changed
    - changed
    properties:
    - Value
  RbxAnalyticsService:
    superclass: Instance
    events: []
    properties: []
  ReflectionMetadata:
    superclass: Instance
    events: []
    properties: []
  ReflectionMetadataCallbacks:
    superclass: Instance
    events: []
    properties: []
  ReflectionMetadataClass:
    superclass: ReflectionMetadataItem
    events: []
    properties:
    - ExplorerImageIndex
    - ExplorerOrder
    - Insertable
    - PreferredParent
    - ServiceVisibility
  ReflectionMetadataClasses:
    superclass: Instance
    events: []
    properties: []
  ReflectionMetadataEnum:
    superclass: ReflectionMetadataItem
    events: []
    properties: []
  ReflectionMetadataEnumItem:
    superclass: ReflectionMetadataItem
    events: []
    properties: []
  ReflectionMetadataEnums:
    superclass: Instance
    events: []
    properties: []
  ReflectionMetadataEvents:
    superclass: Instance
    events: []
    properties: []
  ReflectionMetadataFunctions:
    superclass: Instance
    events: []
    properties: []
  ReflectionMetadataItem:
    superclass: Instance
    events: []
    properties:
    - Browsable
    - ClassCategory
    - ClientOnly
    - Constraint
    - Deprecated
    - EditingDisabled
    - EditorType
    - FFlag
    - IsBackend
    - PropertyOrder
    - ScriptContext
    - ServerOnly
    - SliderScaling
    - UIMaximum
    - UIMinimum
    - UINumTicks
  ReflectionMetadataMember:
    superclass: ReflectionMetadataItem
    events: []
    properties: []
  ReflectionMetadataProperties:
    superclass: Instance
    events: []
    properties: []
  ReflectionMetadataYieldFunctions:
    superclass: Instance
    events: []
    properties: []
  ReflectionService:
    superclass: Instance
    events: []
    properties: []
  RelativeGui:
    superclass: GuiObject
    events: []
    properties: []
  RemoteCursorService:
    superclass: Instance
    events: []
    properties: []
  RemoteDebuggerServer:
    superclass: Instance
    events: []
    properties: []
  RemoteEvent:
    superclass: BaseRemoteEvent
    events:
    - OnClientEvent
    - OnServerEvent
    properties: []
  RemoteFunction:
    superclass: Instance
    events: []
    properties: []
  RenderSettings:
    superclass: Instance
    events: []
    properties:
    - AutoFRMLevel
    - EagerBulkExecution
    - EditQualityLevel
    - Enable VR Mode
    - EnableFRM
    - ExportMergeByMaterial
    - FrameRateManager
    - GraphicsMode
    - MeshCacheSize
    - MeshPartDetailLevel
    - QualityLevel
    - ReloadAssets
    - RenderCSGTrianglesDebug
    - ShowBoundingBoxes
    - ViewMode
  RenderingTest:
    superclass: Instance
    events: []
    properties:
    - CFrame
    - ComparisonDiffThreshold
    - ComparisonMethod
    - ComparisonPsnrThreshold
    - Description
    - FieldOfView
    - Orientation
    - PerfTest
    - Position
    - QualityAuto
    - QualityLevel
    - RenderingTestFrameCount
    - ShouldSkip
    - Ticket
    - Timeout
  ReplicatedFirst:
    superclass: Instance
    events:
    - DefaultLoadingGuiRemoved
    - FinishedReplicating
    - RemoveDefaultLoadingGuiSignal
    properties: []
  ReplicatedStorage:
    superclass: Instance
    events: []
    properties: []
  ReverbSoundEffect:
    superclass: SoundEffect
    events: []
    properties:
    - DecayTime
    - Density
    - Diffusion
    - DryLevel
    - WetLevel
  RibbonNotificationService:
    superclass: Instance
    events:
    - AllNotificationsReadFromRibbon
    - NewNotificationFromRibbon
    - NotificationReadFromRibbon
    - ToggleNotificationTray
    properties: []
  RigidConstraint:
    superclass: Constraint
    events: []
    properties: []
  RobloxPluginGuiService:
    superclass: Instance
    events: []
    properties: []
  RobloxReplicatedStorage:
    superclass: Instance
    events: []
    properties: []
  RobloxSerializableInstance:
    superclass: Instance
    events: []
    properties:
    - Data
  RobloxServerStorage:
    superclass: Instance
    events: []
    properties: []
  RocketPropulsion:
    superclass: BodyMover
    events:
    - ReachedTarget
    properties:
    - CartoonFactor
    - MaxSpeed
    - MaxThrust
    - MaxTorque
    - Target
    - TargetOffset
    - TargetRadius
    - ThrustD
    - ThrustP
    - TurnD
    - TurnP
  RodConstraint:
    superclass: Constraint
    events: []
    properties:
    - CurrentDistance
    - Length
    - LimitAngle0
    - LimitAngle1
    - LimitsEnabled
    - Thickness
  RomarkRbxAnalyticsService:
    superclass: Instance
    events: []
    properties: []
  RomarkService:
    superclass: Instance
    events: []
    properties: []
  RootImportData:
    superclass: BaseImportData
    events: []
    properties:
    - AddModelToInventory
    - Anchored
    - AnimationIdForRestPose
    - ExistingPackageId
    - FileDimensions
    - ImportAsModelAsset
    - ImportAsPackage
    - InsertInWorkspace
    - InsertWithScenePosition
    - InvertNegativeFaces
    - KeepZeroInfluenceBones
    - MergeMeshes
    - PolygonCount
    - PreferredUploadId
    - RestPose
    - RigScale
    - RigType
    - RigVisualization
    - ScaleUnit
    - UseSceneOriginAsCFrame
    - UseSceneOriginAsPivot
    - UsesCages
    - ValidateUgcBody
    - WorldForward
    - WorldUp
  RopeConstraint:
    superclass: Constraint
    events: []
    properties:
    - CurrentDistance
    - Length
    - Restitution
    - Thickness
    - WinchEnabled
    - WinchForce
    - WinchResponsiveness
    - WinchSpeed
    - WinchTarget
  Rotate:
    superclass: JointInstance
    events: []
    properties: []
  RotateP:
    superclass: DynamicRotate
    events: []
    properties: []
  RotateV:
    superclass: DynamicRotate
    events: []
    properties: []
  RotationCurve:
    superclass: Instance
    events: []
    properties:
    - Length
  RtMessagingService:
    superclass: Instance
    events: []
    properties: []
  RunService:
    superclass: Instance
    events:
    - Heartbeat
    - PostSimulation
    - PreAnimation
    - PreRender
    - PreSimulation
    - RenderStepped
    - RobloxGuiFocusedChanged
    - Stepped
    properties:
    - ClientGitHash
    - RunState
  RunningAverageItemDouble:
    superclass: StatsItem
    events: []
    properties: []
  RunningAverageItemInt:
    superclass: StatsItem
    events: []
    properties: []
  RunningAverageTimeIntervalItem:
    superclass: StatsItem
    events: []
    properties: []
  RuntimeScriptService:
    superclass: Instance
    events: []
    properties: []
  SafetyService:
    superclass: Instance
    events:
    - ScreenshotContentReady
    - ScreenshotUploaded
    properties:
    - IsCaptureModeForReport
  ScreenGui:
    superclass: LayerCollector
    events: []
    properties:
    - ClipToDeviceSafeArea
    - DisplayOrder
    - IgnoreGuiInset
    - OnTopOfCoreBlur
    - SafeAreaCompatibility
    - ScreenInsets
  ScreenshotCapture:
    superclass: Capture
    events: []
    properties: []
  ScreenshotHud:
    superclass: Instance
    events: []
    properties:
    - CameraButtonIcon
    - CameraButtonPosition
    - CloseButtonPosition
    - CloseWhenScreenshotTaken
    - ExperienceNameOverlayEnabled
    - HideCoreGuiForCaptures
    - HidePlayerGuiForCaptures
    - OverlayFont
    - UsernameOverlayEnabled
    - Visible
  Script:
    superclass: BaseScript
    events: []
    properties:
    - Source
  ScriptBuilder:
    superclass: Instance
    events: []
    properties: []
  ScriptChangeService:
    superclass: Instance
    events:
    - ScriptAdded
    - ScriptBeingRemoved
    - ScriptChanged
    - ScriptFullNameChanged
    - ScriptSourceChanged
    properties: []
  ScriptCloneWatcher:
    superclass: Instance
    events: []
    properties: []
  ScriptCloneWatcherHelper:
    superclass: Instance
    events: []
    properties: []
  ScriptCommitService:
    superclass: Instance
    events: []
    properties: []
  ScriptContext:
    superclass: Instance
    events:
    - Error
    - ErrorDetailed
    properties:
    - ScriptsDisabled
  ScriptDebugger:
    superclass: Instance
    events:
    - BreakpointAdded
    - BreakpointRemoved
    - EncounteredBreak
    - Resuming
    - WatchAdded
    - WatchRemoved
    properties:
    - CurrentLine
    - IsDebugging
    - IsPaused
    - Script
  ScriptDocument:
    superclass: Instance
    events:
    - SelectionChanged
    - ViewportChanged
    properties: []
  ScriptEditorService:
    superclass: Instance
    events:
    - TextDocumentDidChange
    - TextDocumentDidClose
    - TextDocumentDidOpen
    properties: []
  ScriptProfilerService:
    superclass: Instance
    events:
    - OnNewData
    properties: []
  ScriptRegistrationService:
    superclass: Instance
    events: []
    properties: []
  ScriptRuntime:
    superclass: Instance
    events: []
    properties: []
  ScriptService:
    superclass: Instance
    events: []
    properties: []
  ScrollingFrame:
    superclass: GuiObject
    events: []
    properties:
    - AbsoluteCanvasSize
    - AbsoluteWindowSize
    - AutomaticCanvasSize
    - BottomImage
    - CanvasPosition
    - CanvasSize
    - DraggingScrollBar
    - ElasticBehavior
    - HorizontalBarRect
    - HorizontalScrollBarInset
    - MaxCanvasPosition
    - MidImage
    - ScrollBarImageColor3
    - ScrollBarImageTransparency
    - ScrollBarThickness
    - ScrollRate
    - ScrollVelocity
    - ScrollingDirection
    - ScrollingEnabled
    - SmoothScroll
    - TopImage
    - VerticalBarRect
    - VerticalScrollBarInset
    - VerticalScrollBarPosition
  Seat:
    superclass: Part
    events: []
    properties:
    - Disabled
    - Occupant
  Selection:
    superclass: Instance
    events:
    - SelectionChanged
    - SelectionChangedThisFrame
    properties:
    - ActiveInstance
    - RenderMode
    - SelectionBoxThickness
    - SelectionLineThickness
    - SelectionThickness
    - ShowActiveInstanceHighlight
  SelectionBox:
    superclass: InstanceAdornment
    events: []
    properties:
    - LineThickness
    - StudioSelectionBox
    - SurfaceColor
    - SurfaceColor3
    - SurfaceTransparency
  SelectionHighlightManager:
    superclass: Instance
    events: []
    properties: []
  SelectionLasso:
    superclass: GuiBase3d
    events: []
    properties:
    - Humanoid
  SelectionPartLasso:
    superclass: SelectionLasso
    events: []
    properties:
    - Part
  SelectionPointLasso:
    superclass: SelectionLasso
    events: []
    properties:
    - Point
  SelectionSphere:
    superclass: PVAdornment
    events: []
    properties:
    - SurfaceColor
    - SurfaceColor3
    - SurfaceTransparency
  SensorBase:
    superclass: Instance
    events:
    - OnSensorOutputChanged
    properties:
    - UpdateType
  SerializationService:
    superclass: Instance
    events: []
    properties: []
  ServerReplicator:
    superclass: NetworkReplicator
    events: []
    properties: []
  ServerScriptService:
    superclass: Instance
    events: []
    properties:
    - LoadStringEnabled
  ServerStorage:
    superclass: Instance
    events: []
    properties: []
  ServiceProvider:
    superclass: Instance
    events:
    - Close
    - CloseLate
    - ServiceAdded
    - ServiceRemoving
    properties: []
  ServiceVisibilityService:
    superclass: Instance
    events:
    - ServiceVisibilityChanged
    properties:
    - HiddenServices
    - VisibleServices
  SessionService:
    superclass: Instance
    events: []
    properties: []
  SharedTableRegistry:
    superclass: Instance
    events: []
    properties: []
  Shirt:
    superclass: Clothing
    events: []
    properties:
    - ShirtTemplate
  ShirtGraphic:
    superclass: CharacterAppearance
    events: []
    properties:
    - Color3
    - Graphic
  SkateboardController:
    superclass: Controller
    events:
    - AxisChanged
    properties:
    - Steer
    - Throttle
  SkateboardPlatform:
    superclass: Part
    events:
    - Equipped
    - MoveStateChanged
    - Unequipped
    - equipped
    - unequipped
    properties:
    - Controller
    - ControllingHumanoid
    - Steer
    - StickyWheels
    - Throttle
  Skin:
    superclass: CharacterAppearance
    events: []
    properties:
    - SkinColor
  Sky:
    superclass: Instance
    events: []
    properties:
    - CelestialBodiesShown
    - MoonAngularSize
    - MoonTextureId
    - SkyboxBk
    - SkyboxDn
    - SkyboxFt
    - SkyboxLf
    - SkyboxRt
    - SkyboxUp
    - StarCount
    - SunAngularSize
    - SunTextureId
  SlidingBallConstraint:
    superclass: Constraint
    events: []
    properties:
    - ActuatorType
    - CurrentPosition
    - LimitsEnabled
    - LinearResponsiveness
    - LowerLimit
    - MotorMaxAcceleration
    - MotorMaxForce
    - Restitution
    - ServoMaxForce
    - Size
    - SoftlockServoUponReachingTarget
    - Speed
    - TargetPosition
    - UpperLimit
    - Velocity
  Smoke:
    superclass: Instance
    events: []
    properties:
    - Color
    - Enabled
    - LocalTransparencyModifier
    - Opacity
    - RiseVelocity
    - Size
    - TimeScale
  SmoothVoxelsUpgraderService:
    superclass: Instance
    events:
    - Status
    properties: []
  Snap:
    superclass: JointInstance
    events: []
    properties: []
  SnippetService:
    superclass: Instance
    events: []
    properties: []
  SocialService:
    superclass: Instance
    events:
    - CallInviteStateChanged
    - GameInvitePromptClosed
    - PhoneBookPromptClosed
    - PlayerPartyDataChanged
    - PromptInviteRequested
    - PromptIrisInviteRequested
    - SelfViewHidden
    - SelfViewVisible
    properties: []
  SolidModelContentProvider:
    superclass: CacheableContentProvider
    events: []
    properties: []
  Sound:
    superclass: Instance
    events:
    - DidLoop
    - Ended
    - Loaded
    - Paused
    - Played
    - Resumed
    - Stopped
    properties:
    - ChannelCount
    - EmitterSize
    - IsLoaded
    - IsPaused
    - IsPlaying
    - IsSpatial
    - LoopRegion
    - Looped
    - MaxDistance
    - MinDistance
    - Pitch
    - PlayOnRemove
    - PlaybackLoudness
    - PlaybackRegion
    - PlaybackRegionsEnabled
    - PlaybackSpeed
    - Playing
    - RollOffGain
    - RollOffMaxDistance
    - RollOffMinDistance
    - RollOffMode
    - SoundGroup
    - SoundId
    - TimeLength
    - TimePosition
    - UsageContextPermission
    - Volume
    - isPlaying
  SoundEffect:
    superclass: Instance
    events: []
    properties:
    - Enabled
    - Priority
  SoundGroup:
    superclass: Instance
    events: []
    properties:
    - Volume
  SoundService:
    superclass: Instance
    events:
    - AudioInstanceAdded
    - DeviceListChanged
    - OpenAttenuationCurveEditorSignal
    - OpenAudioCompressorEditorSignal
    - OpenAudioEqualizerEditorSignal
    - OpenDirectionalCurveEditorSignal
    properties:
    - AmbientReverb
    - AudioApiByDefault
    - CharacterSoundsUseNewApi
    - DefaultListenerLocation
    - DistanceFactor
    - DopplerScale
    - IsNewExpForAudioApiByDefault
    - RespectFilteringEnabled
    - RolloffScale
    - VolumetricAudio
  Sparkles:
    superclass: Instance
    events: []
    properties:
    - Color
    - Enabled
    - LocalTransparencyModifier
    - SparkleColor
    - TimeScale
  SpawnLocation:
    superclass: Part
    events: []
    properties:
    - AllowTeamChangeOnTouch
    - Duration
    - Enabled
    - Neutral
    - TeamColor
  SpawnerService:
    superclass: Instance
    events: []
    properties: []
  SpecialMesh:
    superclass: FileMesh
    events: []
    properties:
    - MeshType
  SphereHandleAdornment:
    superclass: HandleAdornment
    events: []
    properties:
    - Radius
  SpotLight:
    superclass: Light
    events: []
    properties:
    - Angle
    - Face
    - Range
  SpringConstraint:
    superclass: Constraint
    events: []
    properties:
    - Coils
    - CurrentLength
    - Damping
    - FreeLength
    - LimitsEnabled
    - MaxForce
    - MaxLength
    - MinLength
    - Radius
    - Stiffness
    - Thickness
  StackFrame:
    superclass: Instance
    events: []
    properties:
    - FrameId
    - FrameName
    - FrameType
    - Globals
    - Line
    - Locals
    - Populated
    - Script
    - Upvalues
  StandalonePluginScripts:
    superclass: Instance
    events: []
    properties: []
  StandardPages:
    superclass: Pages
    events: []
    properties: []
  StartPageService:
    superclass: Instance
    events:
    - ImageImportedSignal
    - LocalGamesFromRegistryUpdatedSignal
    - RecentApiGamesFromRegistryUpdatedSignal
    properties: []
  StarterCharacterScripts:
    superclass: StarterPlayerScripts
    events: []
    properties: []
  StarterGear:
    superclass: Instance
    events: []
    properties: []
  StarterGui:
    superclass: BasePlayerGui
    events:
    - CoreGuiChangedSignal
    properties:
    - ProcessUserInput
    - ResetPlayerGuiOnSpawn
    - RtlTextSupport
    - ScreenOrientation
    - ShowDevelopmentGui
    - StudioDefaultStyleSheet
    - StudioInsertWidgetLayerCollectorAutoLinkStyleSheet
    - VirtualCursorMode
  StarterPack:
    superclass: Instance
    events: []
    properties: []
  StarterPlayer:
    superclass: Instance
    events: []
    properties:
    - AllowCustomAnimations
    - AutoJumpEnabled
    - AvatarJointUpgrade
    - CameraMaxZoomDistance
    - CameraMinZoomDistance
    - CameraMode
    - CharacterJumpHeight
    - CharacterJumpPower
    - CharacterMaxSlopeAngle
    - CharacterUseJumpPower
    - CharacterWalkSpeed
    - DevCameraOcclusionMode
    - DevComputerCameraMovementMode
    - DevComputerMovementMode
    - DevTouchCameraMovementMode
    - DevTouchMovementMode
    - EnableDynamicHeads
    - EnableMouseLockOption
    - GameSettingsAssetIDFace
    - GameSettingsAssetIDHead
    - GameSettingsAssetIDLeftArm
    - GameSettingsAssetIDLeftLeg
    - GameSettingsAssetIDPants
    - GameSettingsAssetIDRightArm
    - GameSettingsAssetIDRightLeg
    - GameSettingsAssetIDShirt
    - GameSettingsAssetIDTeeShirt
    - GameSettingsAssetIDTorso
    - GameSettingsAvatar
    - GameSettingsR15Collision
    - GameSettingsScaleRangeBodyType
    - GameSettingsScaleRangeHead
    - GameSettingsScaleRangeHeight
    - GameSettingsScaleRangeProportion
    - GameSettingsScaleRangeWidth
    - HealthDisplayDistance
    - LoadCharacterAppearance
    - 'LoadCharacterLayeredClothing '
    - LuaCharacterController
    - NameDisplayDistance
    - UserEmotesEnabled
  StarterPlayerScripts:
    superclass: Instance
    events: []
    properties: []
  StartupMessageService:
    superclass: Instance
    events: []
    properties: []
  Stats:
    superclass: Instance
    events: []
    properties:
    - ContactsCount
    - DataReceiveKbps
    - DataSendKbps
    - FrameTime
    - HeartbeatTime
    - HeartbeatTimeMs
    - InstanceCount
    - MovingPrimitivesCount
    - PhysicsReceiveKbps
    - PhysicsSendKbps
    - PhysicsStepTime
    - PhysicsStepTimeMs
    - PrimitivesCount
    - RenderCPUFrameTime
    - RenderGPUFrameTime
    - SceneDrawcallCount
    - SceneTriangleCount
    - ShadowsDrawcallCount
    - ShadowsTriangleCount
    - UI2DDrawcallCount
    - UI2DTriangleCount
    - UI3DDrawcallCount
    - UI3DTriangleCount
  StatsItem:
    superclass: Instance
    events: []
    properties:
    - DisplayName
  Status:
    superclass: Model
    events: []
    properties: []
  StopWatchReporter:
    superclass: Instance
    events: []
    properties: []
  StreamingService:
    superclass: Instance
    events:
    - ConversationCleared
    - RequestEnded
    - RequestError
    - RequestStarted
    - SequentialCommandsFinished
    - Stream
    properties: []
  StringValue:
    superclass: ValueBase
    events:
    - Changed
    - changed
    properties:
    - Value
  Studio:
    superclass: Instance
    events:
    - ThemeChanged
    properties:
    - '"TODO" Color'
    - '"function" Color'
    - '"local" Color'
    - '"nil" Color'
    - '"self" Color'
    - ActionOnAutoResumeSync
    - ActionOnStopSync
    - Active Color
    - Active Hover Over Color
    - Always Save Script Changes
    - Animate Hover Over
    - Auto Clean Empty Line
    - Auto Closing Brackets
    - Auto Closing Quotes
    - Auto Delete Closing Brackets and Quotes
    - Auto Indent Rule
    - Auto-Recovery Enabled
    - Auto-Recovery Interval (Minutes)
    - AutoResumeSyncOnPlaceOpen
    - AutocompleteAcceptanceBehavior
    - Automatically trigger AI Code Completion
    - Background Color
    - Basic Objects Display Mode
    - Bool Color
    - Bracket Color
    - Built-in Function Color
    - Camera Mouse Wheel Speed
    - Camera Pan Speed
    - Camera Shift Speed
    - Camera Speed
    - Camera Speed Adjust Binding
    - Camera Zoom to Mouse Position
    - CameraAdaptiveSpeed
    - CameraTweenFocus
    - Clear Output On Start
    - CommandBarLocalState
    - Comment Color
    - Current Line Highlight Color
    - Debugger Current Line Color
    - Debugger Error Line Color
    - DefaultScriptFileDir
    - DeprecatedObjectsShown
    - DisplayLanguage
    - Doc View Code Background Color
    - DraggerActiveColor
    - DraggerMajorGridIncrement
    - DraggerMaxSoftSnaps
    - DraggerPassiveColor
    - DraggerShowHoverRuler
    - DraggerShowMeasurement
    - DraggerShowTargetSnap
    - DraggerSoftSnapMarginFactor
    - DraggerSummonMarginFactor
    - DraggerTiltRotateDuration
    - Enable Autocomplete
    - Enable Autocomplete Doc View
    - Enable CoreScript Debugger
    - Enable Http Sandboxing
    - Enable Internal Beta Features
    - Enable Internal Features
    - Enable Script Analysis
    - Enable Scrollbar Markers
    - Enable Signature Help
    - Enable Signature Help Doc View
    - Enable Temporary Tabs
    - Enable Temporary Tabs In Explorer
    - Enable Type Hover
    - EnableCodeAssist
    - EnableIndentationRulers
    - EnableOnTypeAutocomplete
    - Error Color
    - Find Selection Background Color
    - Font
    - Format On Paste
    - Format On Type
    - FreeCameraSpeedScroll
    - Function Name Color
    - Highlight Current Line
    - Highlight Occurances
    - HintColor
    - Hover Animate Speed
    - Hover Box Thickness
    - Hover Line Thickness
    - Hover Over Color
    - IconOverrideDir
    - Indent Using Spaces
    - IndentationRulerColor
    - InformationColor
    - Keyword Color
    - LargeFileLineCountThreshold
    - LargeFileThreshold
    - Line Thickness
    - LoadAllBuiltinPluginsInRunModes
    - LoadUserPluginsInRunModes
    - LocalAssetsFolder
    - LuaDebuggerEnabled
    - LuaDebuggerEnabledAtStartup
    - Luau Keyword Color
    - Main Volume
    - Matching Word Background Color
    - Maximum Output Lines
    - Menu Item Background Color
    - Method Color
    - Number Color
    - Only Play Audio from Window in Focus
    - Operator Color
    - Output Font
    - Output Layout Mode
    - PermissionLevelShown
    - Physical Draggers Select Scope By Default
    - Pivot Snap To Geometry Color
    - PluginDebuggingEnabled
    - PluginsDir
    - Primary Text Color
    - Property Color
    - ReloadBuiltinPluginsOnChange
    - ReloadLocalPluginsOnChange
    - Respect Studio shortcuts when game has focus
    - Ruler Color
    - Rulers
    - RuntimeUndoBehavior
    - Script Editor Color Preset
    - Script Editor Scrollbar Background Color
    - Script Editor Scrollbar Handle Color
    - ScriptEditorMenuBorderColor
    - ScriptEditorShouldShowPluginMethods
    - ScriptTimeoutLength
    - Scroll Past Last Line
    - Secondary Text Color
    - Select Color
    - Select/Hover Color
    - Selected Menu Item Background Color
    - Selected Text Color
    - Selection Background Color
    - Selection Box Thickness
    - Selection Color
    - Selection Line Thickness
    - Set Pivot of Imported Parts
    - Show Core GUI in Explorer while Playing
    - Show Diagnostics Bar
    - Show FileSyncService
    - Show Hidden Objects in Explorer
    - Show Hover Over
    - Show Light Guides
    - Show Navigation Labels
    - Show Navigation Mesh
    - Show Pathfinding Links
    - Show Plugin GUI Service in Explorer
    - Show Singly Selected Attachment Parent Frame
    - Show Whitespace
    - Show plus button on hover in Explorer
    - ShowCorePackagesInExplorer
    - Skip Closing Brackets and Quotes
    - String Color
    - Tab Width
    - Text Color
    - Text Wrapping
    - Theme
    - UI Theme
    - Use Bounding Box Move Handles
    - Warning Color
    - Whitespace Color
  StudioAssetService:
    superclass: Instance
    events:
    - OnConvertToPackageResult
    - OnPublishPackageResult
    - OnSaveToRoblox
    - OnUGCSubmitCompleted
    properties: []
  StudioAttachment:
    superclass: Instance
    events: []
    properties:
    - AutoHideParent
    - IsArrowVisible
    - Offset
    - SourceAnchorPoint
    - TargetAnchorPoint
  StudioCallout:
    superclass: Instance
    events: []
    properties:
    - AnchorPoint
    - IsArrowVisible
    - IsNextVisible
    - RowName
    - Text
    - Title
  StudioCameraService:
    superclass: Instance
    events: []
    properties: []
  StudioData:
    superclass: Instance
    events: []
    properties:
    - EnableScriptCollabByDefaultOnLoad
  StudioDeviceEmulatorService:
    superclass: Instance
    events:
    - CurrentDeviceIdChanged
    - OrientationChanged
    - TouchInBoundsChanged
    - TouchPositionsChanged
    properties:
    - HasMultiTouchStarted
    - IsMultiTouchEmulationOn
    - IsMultiTouchEnabled
    - PivotPosition
  StudioObjectBase:
    superclass: Instance
    events: []
    properties: []
  StudioPublishService:
    superclass: Instance
    events:
    - GameNameUpdated
    - GamePublishCancelled
    - GamePublishFinished
    - OnPublishAttempt
    - OnSaveOrPublishPlaceToRoblox
    properties:
    - PublishLocked
  StudioScriptDebugEventListener:
    superclass: Instance
    events: []
    properties: []
  StudioSdkService:
    superclass: Instance
    events: []
    properties: []
  StudioService:
    superclass: Instance
    events:
    - OnImportFromRoblox
    - OnOpenGameSettings
    - OnOpenManagePackagePlugin
    - OnPluginInstalledFromToolbox
    - OnPluginInstalledFromWeb
    - OnPublishAsPlugin
    - OnSaveToRoblox
    - PromptTransformPluginCheckEnable
    - SaveLocallyAsComplete
    properties:
    - ActiveScript
    - AlignDraggedObjects
    - DraggerSolveConstraints
    - DrawConstraintsOnTop
    - GridSize
    - HoverInstance
    - InstalledPluginData
    - PivotSnapToGeometry
    - RotateIncrement
    - Secrets
    - ShowConstraintDetails
    - ShowWeldDetails
    - StudioLocaleId
    - UseLocalSpace
  StudioTheme:
    superclass: Instance
    events: []
    properties: []
  StudioUserService:
    superclass: Instance
    events: []
    properties:
    - IsLoggedIn
  StudioWidget:
    superclass: StudioObjectBase
    events: []
    properties: []
  StudioWidgetsService:
    superclass: Instance
    events: []
    properties: []
  StyleBase:
    superclass: Instance
    events:
    - StyleRulesChanged
    properties: []
  StyleDerive:
    superclass: Instance
    events: []
    properties:
    - StyleSheet
  StyleLink:
    superclass: Instance
    events: []
    properties:
    - StyleSheet
  StyleRule:
    superclass: StyleBase
    events:
    - StyleRulePropertyChanged
    properties:
    - Priority
    - Selector
    - SelectorError
  StyleSheet:
    superclass: StyleBase
    events: []
    properties: []
  StylingService:
    superclass: Instance
    events: []
    properties: []
  SunRaysEffect:
    superclass: PostEffect
    events: []
    properties:
    - Intensity
    - Spread
  SurfaceAppearance:
    superclass: Instance
    events: []
    properties:
    - AlphaMode
    - Color
    - ColorMap
    - ColorMapContent
    - MetalnessMap
    - MetalnessMapContent
    - NormalMap
    - NormalMapContent
    - RoughnessMap
    - RoughnessMapContent
    - TexturePack
  SurfaceGui:
    superclass: SurfaceGuiBase
    events: []
    properties:
    - AlwaysOnTop
    - Brightness
    - CanvasSize
    - ClipsDescendants
    - HorizontalCurvature
    - LightInfluence
    - MaxDistance
    - PixelsPerStud
    - Shape
    - SizingMode
    - ToolPunchThroughDistance
    - ZOffset
  SurfaceGuiBase:
    superclass: LayerCollector
    events: []
    properties:
    - Active
    - Adornee
    - Face
  SurfaceLight:
    superclass: Light
    events: []
    properties:
    - Angle
    - Face
    - Range
  SurfaceSelection:
    superclass: PartAdornment
    events: []
    properties:
    - TargetSurface
  SwimController:
    superclass: ControllerBase
    events: []
    properties:
    - AccelerationTime
    - PitchMaxTorque
    - PitchSpeedFactor
    - RollMaxTorque
    - RollSpeedFactor
  SyncScriptBuilder:
    superclass: ScriptBuilder
    events: []
    properties:
    - CompileTarget
    - CoverageInfo
    - DebugInfo
    - PackAsSource
    - RawBytecode
  TaskScheduler:
    superclass: Instance
    events: []
    properties:
    - SchedulerDutyCycle
    - SchedulerRate
    - ThreadPoolConfig
    - ThreadPoolSize
  Team:
    superclass: Instance
    events:
    - PlayerAdded
    - PlayerRemoved
    properties:
    - AutoAssignable
    - AutoColorCharacters
    - ChildOrder
    - Score
    - TeamColor
  TeamCreateData:
    superclass: Instance
    events: []
    properties: []
  TeamCreatePublishService:
    superclass: Instance
    events:
    - TeamCreateErrorStatus
    properties: []
  TeamCreateService:
    superclass: Instance
    events: []
    properties: []
  Teams:
    superclass: Instance
    events: []
    properties: []
  TelemetryService:
    superclass: Instance
    events: []
    properties: []
  TeleportAsyncResult:
    superclass: Instance
    events: []
    properties:
    - PrivateServerId
    - ReservedServerAccessCode
  TeleportOptions:
    superclass: Instance
    events: []
    properties:
    - ReservedServerAccessCode
    - ServerInstanceId
    - ShouldReserveServer
  TeleportService:
    superclass: Instance
    events:
    - LocalPlayerArrivedFromTeleport
    - MenuTeleportAttempt
    - TeleportInitFailed
    properties:
    - CustomizedTeleportUI
  TemporaryCageMeshProvider:
    superclass: Instance
    events: []
    properties: []
  TemporaryScriptService:
    superclass: Instance
    events: []
    properties: []
  Terrain:
    superclass: BasePart
    events: []
    properties:
    - Decoration
    - GrassLength
    - IsSmooth
    - LastUsedModificationMethod
    - MaterialColors
    - MaxExtents
    - ShorelinesUpgraded
    - SmoothVoxelsUpgraded
    - WaterColor
    - WaterReflectance
    - WaterTransparency
    - WaterWaveSize
    - WaterWaveSpeed
  TerrainDetail:
    superclass: Instance
    events: []
    properties:
    - ColorMap
    - Face
    - MaterialPattern
    - MetalnessMap
    - NormalMap
    - RoughnessMap
    - StudsPerTile
  TerrainRegion:
    superclass: Instance
    events: []
    properties:
    - IsSmooth
    - SizeInCells
  TestService:
    superclass: Instance
    events:
    - ServerCollectConditionalResult
    - ServerCollectResult
    properties:
    - AutoRuns
    - Description
    - ErrorCount
    - ExecuteWithStudioRun
    - Is30FpsThrottleEnabled
    - IsPhysicsEnvironmentalThrottled
    - IsSleepAllowed
    - NumberOfPlayers
    - SimulateSecondsLag
    - TestCount
    - ThrottlePhysicsToRealtime
    - Timeout
    - WarnCount
  TextBox:
    superclass: GuiObject
    events:
    - FocusLost
    - Focused
    - ReturnPressedFromOnScreenKeyboard
    properties:
    - ClearTextOnFocus
    - ContentText
    - CursorPosition
    - Font
    - FontFace
    - FontSize
    - LineHeight
    - LocalizationMatchIdentifier
    - LocalizationMatchedSourceText
    - ManualFocusRelease
    - MaxVisibleGraphemes
    - MultiLine
    - OpenTypeFeatures
    - OpenTypeFeaturesError
    - OverlayNativeInput
    - PlaceholderColor3
    - PlaceholderText
    - ReturnKeyType
    - RichText
    - SelectionStart
    - ShouldEmitReturnEvents
    - ShouldEmitUpAndDownArrowEvents
    - ShowNativeInput
    - Text
    - TextBounds
    - TextColor
    - TextColor3
    - TextDirection
    - TextEditable
    - TextFits
    - TextInputType
    - TextScaled
    - TextSize
    - TextStrokeColor3
    - TextStrokeTransparency
    - TextTransparency
    - TextTruncate
    - TextWrap
    - TextWrapped
    - TextXAlignment
    - TextYAlignment
  TextBoxService:
    superclass: Instance
    events: []
    properties: []
  TextButton:
    superclass: GuiButton
    events: []
    properties:
    - ContentText
    - Font
    - FontFace
    - FontSize
    - LineHeight
    - LocalizationMatchIdentifier
    - LocalizationMatchedSourceText
    - LocalizedText
    - MaxVisibleGraphemes
    - OpenTypeFeatures
    - OpenTypeFeaturesError
    - RichText
    - Text
    - TextBounds
    - TextColor
    - TextColor3
    - TextDirection
    - TextFits
    - TextScaled
    - TextSize
    - TextStrokeColor3
    - TextStrokeTransparency
    - TextTransparency
    - TextTruncate
    - TextWrap
    - TextWrapped
    - TextXAlignment
    - TextYAlignment
  TextChannel:
    superclass: Instance
    events:
    - MessageReceived
    properties:
    - DirectChatRequester
  TextChatCommand:
    superclass: Instance
    events:
    - Triggered
    properties:
    - AutocompleteVisible
    - Enabled
    - PrimaryAlias
    - SecondaryAlias
  TextChatConfigurations:
    superclass: Instance
    events: []
    properties: []
  TextChatMessage:
    superclass: Instance
    events: []
    properties:
    - BubbleChatMessageProperties
    - ChatWindowMessageProperties
    - MessageId
    - Metadata
    - PrefixText
    - Status
    - Text
    - TextChannel
    - TextSource
    - Timestamp
    - Translation
  TextChatMessageProperties:
    superclass: Instance
    events: []
    properties:
    - PrefixText
    - Text
    - Translation
  TextChatService:
    superclass: Instance
    events:
    - BubbleDisplayed
    - MessageReceived
    - SendingMessage
    properties:
    - ChatTranslationEnabled
    - ChatTranslationFTUXShown
    - ChatTranslationToggleEnabled
    - ChatVersion
    - CreateDefaultCommands
    - CreateDefaultTextChannels
    - HasSeenDeprecationDialog
  TextFilterResult:
    superclass: Instance
    events: []
    properties: []
  TextFilterTranslatedResult:
    superclass: Instance
    events: []
    properties:
    - SourceLanguage
    - SourceText
  TextLabel:
    superclass: GuiLabel
    events: []
    properties:
    - ContentText
    - Font
    - FontFace
    - FontSize
    - LineHeight
    - LocalizationMatchIdentifier
    - LocalizationMatchedSourceText
    - LocalizedText
    - MaxVisibleGraphemes
    - OpenTypeFeatures
    - OpenTypeFeaturesError
    - RichText
    - Text
    - TextBounds
    - TextColor
    - TextColor3
    - TextDirection
    - TextFits
    - TextScaled
    - TextSize
    - TextStrokeColor3
    - TextStrokeTransparency
    - TextTransparency
    - TextTruncate
    - TextWrap
    - TextWrapped
    - TextXAlignment
    - TextYAlignment
  TextService:
    superclass: Instance
    events: []
    properties: []
  TextSource:
    superclass: Instance
    events: []
    properties:
    - CanSend
    - UserId
  Texture:
    superclass: Decal
    events: []
    properties:
    - OffsetStudsU
    - OffsetStudsV
    - StudsPerTileU
    - StudsPerTileV
  TextureGenerationPartGroup:
    superclass: Instance
    events: []
    properties: []
  TextureGenerationService:
    superclass: Instance
    events:
    - GenerationNotificationSignal
    - PreviewNotificationSignal
    properties: []
  TextureGenerationUnwrappingRequest:
    superclass: Instance
    events: []
    properties: []
  ThirdPartyUserService:
    superclass: Instance
    events:
    - ActiveUserSignedOut
    properties: []
  ThreadState:
    superclass: Instance
    events: []
    properties:
    - FrameCount
    - Populated
    - ThreadId
    - ThreadName
  TimerService:
    superclass: Instance
    events: []
    properties: []
  ToastNotificationService:
    superclass: Instance
    events: []
    properties: []
  Tool:
    superclass: BackpackItem
    events:
    - Activated
    - Deactivated
    - Equipped
    - Unequipped
    properties:
    - CanBeDropped
    - Enabled
    - Grip
    - GripForward
    - GripPos
    - GripRight
    - GripUp
    - ManualActivationOnly
    - RequiresHandle
    - ToolTip
  Torque:
    superclass: Constraint
    events: []
    properties:
    - RelativeTo
    - Torque
  TorsionSpringConstraint:
    superclass: Constraint
    events: []
    properties:
    - Coils
    - CurrentAngle
    - Damping
    - LimitEnabled
    - LimitsEnabled
    - MaxAngle
    - MaxTorque
    - Radius
    - Restitution
    - Stiffness
  TotalCountTimeIntervalItem:
    superclass: StatsItem
    events: []
    properties: []
  TouchInputService:
    superclass: Instance
    events: []
    properties: []
  TouchTransmitter:
    superclass: Instance
    events: []
    properties: []
  TracerService:
    superclass: Instance
    events: []
    properties: []
  TrackerLodController:
    superclass: Instance
    events:
    - UpdateState
    properties:
    - AudioMode
    - VideoExtrapolationMode
    - VideoLodMode
    - VideoMode
  TrackerStreamAnimation:
    superclass: Instance
    events: []
    properties: []
  Trail:
    superclass: Instance
    events: []
    properties:
    - Attachment0
    - Attachment1
    - Brightness
    - Color
    - Enabled
    - FaceCamera
    - Lifetime
    - LightEmission
    - LightInfluence
    - LocalTransparencyModifier
    - MaxLength
    - MinLength
    - Texture
    - TextureLength
    - TextureMode
    - Transparency
    - WidthScale
  Translator:
    superclass: Instance
    events: []
    properties:
    - LocaleId
  TremoloSoundEffect:
    superclass: SoundEffect
    events: []
    properties:
    - Depth
    - Duty
    - Frequency
  TriangleMeshPart:
    superclass: BasePart
    events: []
    properties:
    - CollisionFidelity
    - FluidFidelity
    - MeshSize
    - UnscaledCofm
    - UnscaledVolInertiaDiags
    - UnscaledVolInertiaOffDiags
    - UnscaledVolume
  TrussPart:
    superclass: BasePart
    events: []
    properties:
    - Style
  TutorialService:
    superclass: Instance
    events: []
    properties: []
  Tween:
    superclass: TweenBase
    events: []
    properties:
    - Instance
    - TweenInfo
  TweenBase:
    superclass: Instance
    events:
    - Completed
    properties:
    - PlaybackState
  TweenService:
    superclass: Instance
    events: []
    properties: []
  UGCAvatarService:
    superclass: Instance
    events: []
    properties: []
  UGCValidationService:
    superclass: Instance
    events: []
    properties: []
  UIAspectRatioConstraint:
    superclass: UIConstraint
    events: []
    properties:
    - AspectRatio
    - AspectType
    - DominantAxis
  UIBase:
    superclass: Instance
    events: []
    properties: []
  UIComponent:
    superclass: UIBase
    events: []
    properties: []
  UIConstraint:
    superclass: UIComponent
    events: []
    properties: []
  UICorner:
    superclass: UIComponent
    events: []
    properties:
    - CornerRadius
  UIDragDetector:
    superclass: UIComponent
    events:
    - DragContinue
    - DragEnd
    - DragStart
    properties:
    - ActivatedCursorIcon
    - BoundingBehavior
    - BoundingUI
    - CursorIcon
    - DragAxis
    - DragRelativity
    - DragRotation
    - DragSpace
    - DragStyle
    - DragUDim2
    - Enabled
    - MaxDragAngle
    - MaxDragTranslation
    - MinDragAngle
    - MinDragTranslation
    - ReferenceUIInstance
    - ResponseStyle
    - SelectionModeDragSpeed
    - SelectionModeRotateSpeed
    - UIDragSpeedAxisMapping
  UIDragDetectorService:
    superclass: Instance
    events: []
    properties: []
  UIFlexItem:
    superclass: UIComponent
    events: []
    properties:
    - FlexMode
    - GrowRatio
    - ItemLineAlignment
    - ShrinkRatio
  UIGradient:
    superclass: UIComponent
    events: []
    properties:
    - Color
    - Enabled
    - Offset
    - Rotation
    - Transparency
  UIGridLayout:
    superclass: UIGridStyleLayout
    events: []
    properties:
    - AbsoluteCellCount
    - AbsoluteCellSize
    - CellPadding
    - CellSize
    - FillDirectionMaxCells
    - StartCorner
  UIGridStyleLayout:
    superclass: UILayout
    events: []
    properties:
    - AbsoluteContentSize
    - FillDirection
    - HorizontalAlignment
    - SortOrder
    - VerticalAlignment
  UILayout:
    superclass: UIComponent
    events: []
    properties: []
  UIListLayout:
    superclass: UIGridStyleLayout
    events: []
    properties:
    - HorizontalFlex
    - ItemLineAlignment
    - Padding
    - VerticalFlex
    - Wraps
  UIPadding:
    superclass: UIComponent
    events: []
    properties:
    - PaddingBottom
    - PaddingLeft
    - PaddingRight
    - PaddingTop
  UIPageLayout:
    superclass: UIGridStyleLayout
    events:
    - PageEnter
    - PageLeave
    - Stopped
    properties:
    - Animated
    - Circular
    - CurrentPage
    - EasingDirection
    - EasingStyle
    - GamepadInputEnabled
    - Padding
    - ScrollWheelInputEnabled
    - TouchInputEnabled
    - TweenTime
  UIScale:
    superclass: UIComponent
    events: []
    properties:
    - Scale
  UISizeConstraint:
    superclass: UIConstraint
    events: []
    properties:
    - MaxSize
    - MinSize
  UIStroke:
    superclass: UIComponent
    events: []
    properties:
    - ApplyStrokeMode
    - Color
    - Enabled
    - LineJoinMode
    - Thickness
    - Transparency
  UITableLayout:
    superclass: UIGridStyleLayout
    events: []
    properties:
    - FillEmptySpaceColumns
    - FillEmptySpaceRows
    - MajorAxis
    - Padding
  UITextSizeConstraint:
    superclass: UIConstraint
    events: []
    properties:
    - MaxTextSize
    - MinTextSize
  UnionOperation:
    superclass: PartOperation
    events: []
    properties: []
  UniqueIdLookupService:
    superclass: Instance
    events: []
    properties: []
  UniversalConstraint:
    superclass: Constraint
    events: []
    properties:
    - LimitsEnabled
    - MaxAngle
    - Radius
    - Restitution
  UnreliableRemoteEvent:
    superclass: BaseRemoteEvent
    events:
    - OnClientEvent
    - OnServerEvent
    properties: []
  UnvalidatedAssetService:
    superclass: Instance
    events: []
    properties: []
  UserGameSettings:
    superclass: Instance
    events:
    - FullscreenChanged
    - PerformanceStatsVisibleChanged
    - StudioModeChanged
    properties:
    - AllTutorialsDisabled
    - CameraMode
    - CameraYInverted
    - ChatTranslationEnabled
    - ChatTranslationFTUXShown
    - ChatTranslationLocale
    - ChatTranslationToggleEnabled
    - ChatVisible
    - ComputerCameraMovementMode
    - ComputerMovementMode
    - ControlMode
    - DefaultCameraID
    - FramerateCap
    - Fullscreen
    - GamepadCameraSensitivity
    - GraphicsOptimizationMode
    - GraphicsQualityLevel
    - HapticStrength
    - HasEverUsedVR
    - IsUsingCameraYInverted
    - IsUsingGamepadCameraSensitivity
    - MasterVolume
    - MasterVolumeStudio
    - MaxQualityEnabled
    - MicroProfilerWebServerEnabled
    - MicroProfilerWebServerIP
    - MicroProfilerWebServerPort
    - MouseSensitivity
    - MouseSensitivityFirstPerson
    - MouseSensitivityThirdPerson
    - OnScreenProfilerEnabled
    - OnboardingsCompleted
    - PartyVoiceVolume
    - PerformanceStatsVisible
    - PlayerHeight
    - PreferredTextSize
    - PreferredTransparency
    - QualityResetLevel
    - RCCProfilerRecordFrameRate
    - RCCProfilerRecordTimeFrame
    - ReducedMotion
    - RotationType
    - SavedQualityLevel
    - StartMaximized
    - StartScreenPosition
    - StartScreenSize
    - TouchCameraMovementMode
    - TouchMovementMode
    - UiNavigationKeyBindEnabled
    - UsedCoreGuiIsVisibleToggle
    - UsedCustomGuiIsVisibleToggle
    - UsedHideHudShortcut
    - VRComfortSetting
    - VREnabled
    - VRRotationIntensity
    - VRSafetyBubbleMode
    - VRSmoothRotationEnabled
    - VRSmoothRotationEnabledCustomOption
    - VRThirdPersonFollowCamEnabled
    - VRThirdPersonFollowCamEnabledCustomOption
    - VignetteEnabled
    - VignetteEnabledCustomOption
  UserInputService:
    superclass: Instance
    events:
    - DeviceAccelerationChanged
    - DeviceGravityChanged
    - DeviceRotationChanged
    - GamepadConnected
    - GamepadDisconnected
    - InputBegan
    - InputChanged
    - InputEnded
    - JumpRequest
    - LastInputTypeChanged
    - PointerAction
    - StatusBarTapped
    - TextBoxFocusReleased
    - TextBoxFocused
    - TouchDrag
    - TouchEnded
    - TouchLongPress
    - TouchMoved
    - TouchPan
    - TouchPinch
    - TouchRotate
    - TouchStarted
    - TouchSwipe
    - TouchTap
    - TouchTapInWorld
    - UserCFrameChanged
    - WindowFocusReleased
    - WindowFocused
    properties:
    - AccelerometerEnabled
    - BottomBarSize
    - GamepadEnabled
    - GyroscopeEnabled
    - KeyboardEnabled
    - LegacyInputEventsEnabled
    - ModalEnabled
    - MouseBehavior
    - MouseDeltaSensitivity
    - MouseEnabled
    - MouseIcon
    - MouseIconEnabled
    - NavBarSize
    - OnScreenKeyboardAnimationDuration
    - OnScreenKeyboardPosition
    - OnScreenKeyboardSize
    - OnScreenKeyboardVisible
    - OverrideMouseIconBehavior
    - RightBarSize
    - StatusBarSize
    - TouchEnabled
    - UserHeadCFrame
    - VREnabled
  UserService:
    superclass: Instance
    events: []
    properties: []
  UserSettings:
    superclass: GenericSettings
    events: []
    properties: []
  UserStorageService:
    superclass: LocalStorageService
    events: []
    properties: []
  VRService:
    superclass: Instance
    events:
    - LaserPointerTriggered
    - NavigationRequested
    - TouchpadModeChanged
    - UserCFrameChanged
    - UserCFrameEnabled
    properties:
    - AutomaticScaling
    - AvatarGestures
    - ControllerModels
    - DidPointerHit
    - FadeOutViewOnCollision
    - GuiInputUserCFrame
    - LaserDistance
    - LaserPointer
    - PointerHitCFrame
    - QuestASWState
    - QuestDisplayRefreshRate
    - ThirdPersonFollowCamEnabled
    - VRDeviceAvailable
    - VRDeviceName
    - VREnabled
    - VRSessionState
  VRStatusService:
    superclass: Instance
    events: []
    properties: []
  ValueBase:
    superclass: Instance
    events: []
    properties: []
  Vector3Curve:
    superclass: Instance
    events: []
    properties: []
  Vector3Value:
    superclass: ValueBase
    events:
    - Changed
    - changed
    properties:
    - Value
  VectorForce:
    superclass: Constraint
    events: []
    properties:
    - ApplyAtCenterOfMass
    - Force
    - RelativeTo
  VehicleController:
    superclass: Controller
    events: []
    properties: []
  VehicleSeat:
    superclass: BasePart
    events: []
    properties:
    - AreHingesDetected
    - Disabled
    - HeadsUpDisplay
    - MaxSpeed
    - Occupant
    - Steer
    - SteerFloat
    - Throttle
    - ThrottleFloat
    - Torque
    - TurnSpeed
  VelocityMotor:
    superclass: JointInstance
    events: []
    properties:
    - CurrentAngle
    - DesiredAngle
    - Hole
    - MaxVelocity
  VersionControlService:
    superclass: Instance
    events: []
    properties:
    - ScriptCollabEnabled
  VideoCaptureService:
    superclass: Instance
    events:
    - DevicesChanged
    - Error
    - Started
    - Stopped
    properties:
    - Active
    - CameraID
  VideoDeviceInput:
    superclass: Instance
    events: []
    properties:
    - Active
    - CameraId
    - CaptureQuality
    - IsReady
  VideoDisplay:
    superclass: GuiObject
    events:
    - WiringChanged
    properties:
    - ResampleMode
    - ScaleType
    - TileSize
    - VideoColor3
    - VideoRectOffset
    - VideoRectSize
    - VideoTransparency
  VideoFrame:
    superclass: GuiObject
    events:
    - DidLoop
    - Ended
    - Loaded
    - Paused
    - Played
    properties:
    - IsLoaded
    - Looped
    - Playing
    - Resolution
    - TimeLength
    - TimePosition
    - Video
    - Volume
  VideoPlayer:
    superclass: Instance
    events:
    - DidEnd
    - DidLoop
    - PlayFailed
    - WiringChanged
    properties:
    - Asset
    - AutoLoadInStudio
    - AutoPlayInStudio
    - IsLoaded
    - IsPlaying
    - Looping
    - PlaybackSpeed
    - Resolution
    - TimeLength
    - TimePosition
    - Volume
  VideoService:
    superclass: Instance
    events: []
    properties: []
  ViewportFrame:
    superclass: GuiObject
    events: []
    properties:
    - Ambient
    - CurrentCamera
    - ImageColor3
    - ImageTransparency
    - IsMirrored
    - LightColor
    - LightDirection
  VirtualInputManager:
    superclass: Instance
    events:
    - PlaybackCompleted
    - RecordingCompleted
    properties:
    - AdditionalLuaState
  VirtualUser:
    superclass: Instance
    events: []
    properties: []
  VisibilityCheckDispatcher:
    superclass: Instance
    events: []
    properties: []
  Visit:
    superclass: Instance
    events: []
    properties: []
  VisualizationMode:
    superclass: Instance
    events: []
    properties:
    - Enabled
    - Title
    - ToolTip
  VisualizationModeCategory:
    superclass: Instance
    events: []
    properties:
    - Enabled
    - Title
  VisualizationModeService:
    superclass: Instance
    events: []
    properties: []
  VoiceChatInternal:
    superclass: Instance
    events:
    - LocalPlayerModerated
    - ParticipantsStateChanged
    - PlayerMicActivitySignalChange
    - StateChanged
    - TempSetMicMutedToggleMic
    properties:
    - VoiceChatState
  VoiceChatService:
    superclass: Instance
    events: []
    properties:
    - DefaultDistanceAttenuation
    - EnableDefaultVoice
    - UseAudioApi
    - UseNewAudioApi
    - UseNewControlPaths
    - UseNewJoinFlow
    - VoiceChatEnabledForPlaceOnRcc
    - VoiceChatEnabledForUniverseOnRcc
  WebSocketClient:
    superclass: Instance
    events:
    - Closed
    - MessageReceived
    - Opened
    properties:
    - ConnectionState
  WebSocketService:
    superclass: Instance
    events: []
    properties: []
  WebViewService:
    superclass: Instance
    events:
    - OnJavaScriptCall
    - OnWindowClosed
    properties: []
  WedgePart:
    superclass: FormFactorPart
    events: []
    properties: []
  Weld:
    superclass: JointInstance
    events: []
    properties: []
  WeldConstraint:
    superclass: Instance
    events: []
    properties:
    - Active
    - Enabled
    - Part0
    - Part1
  Wire:
    superclass: Instance
    events: []
    properties:
    - Connected
    - SourceInstance
    - SourceName
    - TargetInstance
    - TargetName
  WireframeHandleAdornment:
    superclass: HandleAdornment
    events: []
    properties:
    - Scale
  Workspace:
    superclass: WorldRoot
    events:
    - PersistentLoaded
    properties:
    - AirDensity
    - AllowThirdPartySales
    - AvatarUnificationMode
    - ClientAnimatorThrottling
    - CurrentCamera
    - DistributedGameTime
    - FallHeightEnabled
    - FallenPartsDestroyHeight
    - FilteringEnabled
    - FluidForces
    - GlobalWind
    - Gravity
    - IKControlConstraintSupport
    - InsertPoint
    - InterpolationThrottling
    - MeshPartHeadsAndAccessories
    - ModelStreamingBehavior
    - MoverConstraintRootBehavior
    - PathfindingUseImprovedSearch
    - PhysicsImprovedSleep
    - PhysicsSteppingMethod
    - PlayerCharacterDestroyBehavior
    - PrimalPhysicsSolver
    - RejectCharacterDeletions
    - RenderingCacheOptimizations
    - ReplicateInstanceDestroySetting
    - Retargeting
    - SandboxedInstanceMode
    - SignalBehavior
    - StreamOutBehavior
    - StreamingEnabled
    - StreamingIntegrityMode
    - StreamingMinRadius
    - StreamingTargetRadius
    - Terrain
    - TouchEventsUseCollisionGroups
    - TouchesUseCollisionGroups
  WorkspaceAnnotation:
    superclass: Annotation
    events: []
    properties:
    - Adornee
    - AdorneeOffset
  WorldModel:
    superclass: WorldRoot
    events: []
    properties: []
  WorldRoot:
    superclass: Model
    events: []
    properties: []
  WrapDeformer:
    superclass: BaseWrap
    events: []
    properties: []
  WrapLayer:
    superclass: BaseWrap
    events: []
    properties:
    - AutoSkin
    - BindOffset
    - Color
    - DebugMode
    - Enabled
    - Order
    - Puffiness
    - ReferenceMeshContent
    - ReferenceMeshId
    - ReferenceOrigin
    - ReferenceOriginWorld
    - ShrinkFactor
  WrapTarget:
    superclass: BaseWrap
    events: []
    properties:
    - Color
    - DebugMode
    - Stiffness
