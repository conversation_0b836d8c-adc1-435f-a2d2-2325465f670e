{
  "folders": [
    {
      "name": "Site-112 Main",
      "path": "."
    }
  ],
  "settings": {
    // Inherit all settings from .vscode/settings.json
    // This file can override specific settings for the workspace
    
    // Workspace-specific file associations
    "files.associations": {
      "*.luau": "luau",
      "*.lua": "luau"
    },
    
    // Workspace search settings
    "search.useIgnoreFiles": true,
    "search.useGlobalIgnoreFiles": true,
    
    // Terminal settings for this workspace
    "terminal.integrated.cwd": "${workspaceFolder}",
    
    // Workspace-specific spell check words
    "cSpell.words": [
      "MAFS",
      "TBRDS", 
      "CGS",
      "MCS",
      "Stam"
    ]
  },
  "extensions": {
    "recommendations": [
      "johnnymorganz.luau-lsp",
      "kampfkarren.selene-vscode",
      "eamodio.gitlens",
      "usernamehw.errorlens",
      "gruntfuggly.todo-tree",
      "streetsidesoftware.code-spell-checker",
      "oderwat.indent-rainbow"
    ]
  },
  "tasks": {
    "version": "2.0.0",
    "tasks": [
      {
        "label": "Lint All Luau Files",
        "type": "shell",
        "command": "selene",
        "args": ["src/"],
        "group": "build",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "shared"
        },
        "problemMatcher": {
          "owner": "selene",
          "fileLocation": "absolute",
          "pattern": {
            "regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$",
            "file": 1,
            "line": 2,
            "column": 3,
            "severity": 4,
            "message": 5
          }
        }
      }
    ]
  }
}
