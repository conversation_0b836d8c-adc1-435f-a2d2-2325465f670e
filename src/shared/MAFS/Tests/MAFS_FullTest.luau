--!nonstrict

--[[
    MAFS Test Suite

    Comprehensive tests for the Modular Audio FootStep System (MAFS)

    USAGE:
    - Run from server script to test server-side functionality
    - Run from client script to test client-side functionality
    - Use for debugging and validation during development

    *Dynamic Innovative Studio*
]]

local MAFSTests = {}

-- Services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Import MAFS modules
local MAFSAPI = require(ReplicatedStorage.MAFS.Shared.MAFS_API)
local MAFSConfig = require(ReplicatedStorage.Configurations.Systems.MAFS_Configuration)

	-- Test results tracking
	local testResults = {
		passed = 0,
		failed = 0,
		total = 0,
		failures = {},
	}

--[[
    Test utility functions
]]
	local function assert(condition, message)
		testResults.total = testResults.total + 1
		if condition then
			testResults.passed = testResults.passed + 1
			print("✓ PASS:", message)
			return true
		else
			testResults.failed = testResults.failed + 1
			table.insert(testResults.failures, message)
			print("✗ FAIL:", message)
			return false
		end
	end

	local function createTestPart(name, material, position)
		local part = Instance.new("Part")
		part.Name = name
		part.Material = material or Enum.Material.Plastic
		part.Size = Vector3.new(4, 1, 4)
		part.Position = position or Vector3.new(0, 0, 0)
		part.Anchored = true
		part.Parent = workspace
		return part
	end

	local function cleanup()
		-- Clean up test parts
		for _, child in pairs(workspace:GetChildren()) do
			if child.Name:match("^Test_") then
				child:Destroy()
			end
		end
	end

--[[
    Configuration Tests
]]
	function MAFSTests.TestConfiguration()
		print("=== Testing MAFS Configuration ===")

		-- Test configuration loading
		assert(MAFSConfig ~= nil, "MAFSConfig module loads successfully")
		assert(MAFSConfig.Settings ~= nil, "Settings table exists")
		assert(MAFSConfig.MaterialSounds ~= nil, "MaterialSounds table exists")
		assert(MAFSConfig.CustomMaterials ~= nil, "CustomMaterials table exists")

		-- Test configuration values
		assert(type(MAFSConfig.Settings.BroadcastRadius) == "number", "BroadcastRadius is a number")
		assert(MAFSConfig.Settings.BroadcastRadius > 0, "BroadcastRadius is positive")
		assert(type(MAFSConfig.Settings.ServerCooldown) == "number", "ServerCooldown is a number")
		assert(MAFSConfig.Settings.ServerCooldown > 0, "ServerCooldown is positive")

		-- Test material data structure
		local grassData = MAFSConfig.MaterialSounds[Enum.Material.Grass]
		assert(grassData ~= nil, "Grass material data exists")
		assert(type(grassData.SoundIds) == "table", "Grass SoundIds is a table")
		assert(#grassData.SoundIds > 0, "Grass has sound IDs")
		assert(type(grassData.Volume) == "number", "Grass volume is a number")

		-- Test custom materials
		local snowData = MAFSConfig.CustomMaterials["Snow"]
		assert(snowData ~= nil, "Snow custom material exists")
		assert(type(snowData.SoundIds) == "table", "Snow SoundIds is a table")
		assert(#snowData.SoundIds > 0, "Snow has sound IDs")
	end

--[[
    API Tests
]]
	function MAFSTests.TestAPI()
		print("=== Testing MAFS API ===")

		-- Test API module loading
		assert(MAFSAPI ~= nil, "MAFSAPI module loads successfully")

		-- Test available materials
		local materials = MAFSAPI.GetAvailableMaterials()
		assert(type(materials) == "table", "GetAvailableMaterials returns a table")
		assert(#materials > 0, "Available materials list is not empty")
		assert(table.find(materials, "Snow") ~= nil, "Snow is in available materials")

		-- Test material validation
		assert(MAFSAPI.IsValidMaterial("Snow") == true, "Snow is a valid material")
		assert(MAFSAPI.IsValidMaterial("InvalidMaterial") == false, "InvalidMaterial is not valid")
		assert(MAFSAPI.IsValidMaterial("") == false, "Empty string is not valid")
		assert(MAFSAPI.IsValidMaterial(nil) == false, "nil is not valid")

		-- Test system info
		local systemInfo = MAFSAPI.GetSystemInfo()
		assert(type(systemInfo) == "table", "GetSystemInfo returns a table")
		assert(type(systemInfo.Version) == "string", "System version is a string")
		assert(type(systemInfo.Settings) == "table", "System settings is a table")
		assert(type(systemInfo.AvailableMaterials) == "table", "Available materials is a table")
	end

--[[
    Material Configuration Tests
]]
	function MAFSTests.TestMaterialConfiguration()
		print("=== Testing Material Configuration ===")

		-- Create test parts
		local testPart1 = createTestPart("Test_Part1", Enum.Material.Grass, Vector3.new(10, 5, 10))
		local testPart2 = createTestPart("Test_Part2", Enum.Material.Sand, Vector3.new(20, 5, 20))
		local testModel = Instance.new("Model")
		testModel.Name = "Test_Model"
		testModel.Parent = workspace
		testPart1.Parent = testModel
		testPart2.Parent = testModel

		-- Test single part configuration
		local success = MAFSAPI.SetPartMaterial(testPart1, "Snow")
		assert(success == true, "SetPartMaterial returns true for valid input")

		local material = MAFSAPI.GetPartMaterial(testPart1)
		assert(material == "Snow", "GetPartMaterial returns correct material")

		-- Test invalid material
		success = MAFSAPI.SetPartMaterial(testPart1, "InvalidMaterial")
		assert(success == false, "SetPartMaterial returns false for invalid material")

		-- Test model configuration
		local configuredCount = MAFSAPI.SetModelMaterial(testModel, "Gravel")
		assert(configuredCount == 2, "SetModelMaterial configures correct number of parts")

		-- Verify both parts have the new material
		assert(MAFSAPI.GetPartMaterial(testPart1) == "Gravel", "Part 1 has model material")
		assert(MAFSAPI.GetPartMaterial(testPart2) == "Gravel", "Part 2 has model material")

		-- Test clearing material
		success = MAFSAPI.ClearPartMaterial(testPart1)
		assert(success == true, "ClearPartMaterial returns true")
		assert(MAFSAPI.GetPartMaterial(testPart1) == nil, "Part material is cleared")

		-- Test pattern matching
		local mudPart1 = createTestPart("Test_Mud_Floor1", Enum.Material.Plastic, Vector3.new(30, 5, 30))
		local mudPart2 = createTestPart("Test_Mud_Floor2", Enum.Material.Plastic, Vector3.new(40, 5, 40))
		local nonMudPart =
			createTestPart("Test_Stone_Floor", Enum.Material.Plastic, Vector3.new(50, 5, 50))

		configuredCount = MAFSAPI.SetPartsByPattern(workspace, "Test_Mud_", "Mud")
		assert(configuredCount == 2, "SetPartsByPattern configures correct number of parts")
		assert(MAFSAPI.GetPartMaterial(mudPart1) == "Mud", "Mud part 1 configured correctly")
		assert(MAFSAPI.GetPartMaterial(mudPart2) == "Mud", "Mud part 2 configured correctly")
		assert(MAFSAPI.GetPartMaterial(nonMudPart) == nil, "Non-mud part not affected")

		-- Test material-based configuration
		local rockPart1 = createTestPart("Test_Rock1", Enum.Material.Rock, Vector3.new(60, 5, 60))
		local rockPart2 = createTestPart("Test_Rock2", Enum.Material.Rock, Vector3.new(70, 5, 70))
		local plasticPart = createTestPart("Test_Plastic", Enum.Material.Plastic, Vector3.new(80, 5, 80))

		configuredCount = MAFSAPI.SetPartsByMaterial(workspace, Enum.Material.Rock, "Gravel")
		assert(configuredCount == 2, "SetPartsByMaterial configures correct number of parts")
		assert(MAFSAPI.GetPartMaterial(rockPart1) == "Gravel", "Rock part 1 configured correctly")
		assert(MAFSAPI.GetPartMaterial(rockPart2) == "Gravel", "Rock part 2 configured correctly")
		assert(MAFSAPI.GetPartMaterial(plasticPart) == nil, "Plastic part not affected")
	end

--[[
    Client-Side Tests (only run on client)
]]
	function MAFSTests.TestClientSide()
		if not RunService:IsClient() then
			print("=== Skipping Client-Side Tests (not on client) ===")
			return
		end

		print("=== Testing Client-Side Functionality ===")

		-- Test volume control
		local success = MAFSAPI.SetVolume(0.5)
		assert(success == true, "SetVolume succeeds on client")

		success = MAFSAPI.SetVolume(1.5) -- Should be clamped
		assert(success == true, "SetVolume handles values > 1")

		success = MAFSAPI.SetVolume(-0.5) -- Should be clamped
		assert(success == true, "SetVolume handles negative values")

		-- Test enable/disable
		success = MAFSAPI.SetEnabled(false)
		assert(success == true, "SetEnabled(false) succeeds")

		success = MAFSAPI.SetEnabled(true)
		assert(success == true, "SetEnabled(true) succeeds")
	end

--[[
    Debug and Performance Tests
]]
	function MAFSTests.TestDebugAndPerformance()
		print("=== Testing Debug and Performance ===")

		-- Test debug mode
		local originalDebugMode = MAFSAPI.IsDebugMode()

		MAFSAPI.SetDebugMode(true)
		assert(MAFSAPI.IsDebugMode() == true, "Debug mode can be enabled")

		MAFSAPI.SetDebugMode(false)
		assert(MAFSAPI.IsDebugMode() == false, "Debug mode can be disabled")

		-- Restore original debug mode
		MAFSAPI.SetDebugMode(originalDebugMode)

		-- Test performance metrics
		local metrics = MAFSAPI.GetPerformanceMetrics()
		if metrics then
			assert(type(metrics.activeSounds) == "number", "Active sounds is a number")
			assert(type(metrics.totalSoundsCreated) == "number", "Total sounds created is a number")
			assert(type(metrics.poolHits) == "number", "Pool hits is a number")
			assert(metrics.activeSounds >= 0, "Active sounds is non-negative")
			assert(metrics.totalSoundsCreated >= 0, "Total sounds created is non-negative")
			assert(metrics.poolHits >= 0, "Pool hits is non-negative")
		else
			print("⚠ Performance metrics not available")
		end
	end

--[[
    Error Handling Tests
]]
	function MAFSTests.TestErrorHandling()
		print("=== Testing Error Handling ===")

		-- Test invalid inputs
		local success = MAFSAPI.SetPartMaterial(nil, "Snow")
		assert(success == false, "SetPartMaterial handles nil part")

		success = MAFSAPI.SetPartMaterial(workspace, "Snow") -- workspace is not a BasePart
		assert(success == false, "SetPartMaterial handles non-BasePart")

		success = MAFSAPI.SetModelMaterial(nil, "Snow")
		assert(success == 0, "SetModelMaterial handles nil model")

		local configuredCount = MAFSAPI.SetPartsByPattern(nil, "Test_", "Snow")
		assert(configuredCount == 0, "SetPartsByPattern handles nil parent")

		configuredCount = MAFSAPI.SetPartsByMaterial(nil, Enum.Material.Grass, "Snow")
		assert(configuredCount == 0, "SetPartsByMaterial handles nil parent")

		-- Test getting material from invalid part
		local material = MAFSAPI.GetPartMaterial(nil)
		assert(material == nil, "GetPartMaterial handles nil part")

		material = MAFSAPI.GetPartMaterial(workspace) -- workspace is not a BasePart
		assert(material == nil, "GetPartMaterial handles non-BasePart")
	end

--[[
    Run all tests
]]
	function MAFSTests.RunAllTests()
		print("=== MAFS Test Suite Starting ===")
		print("")

		-- Reset test results
		testResults = {
			passed = 0,
			failed = 0,
			total = 0,
			failures = {},
		}

		-- Run test suites
		MAFSTests.TestConfiguration()
		print("")

		MAFSTests.TestAPI()
		print("")

		MAFSTests.TestMaterialConfiguration()
		print("")

		MAFSTests.TestClientSide()
		print("")

		MAFSTests.TestDebugAndPerformance()
		print("")

		MAFSTests.TestErrorHandling()
		print("")

		-- Clean up test objects
		cleanup()

		-- Print results
		print("=== MAFS Test Suite Results ===")
		print(string.format("Total Tests: %d", testResults.total))
		print(string.format("Passed: %d", testResults.passed))
		print(string.format("Failed: %d", testResults.failed))

		if testResults.failed > 0 then
			print("\nFailures:")
			for _, failure in ipairs(testResults.failures) do
				print("  - " .. failure)
			end
		end

		local successRate = testResults.total > 0 and (testResults.passed / testResults.total * 100) or 0
		print(string.format("Success Rate: %.1f%%", successRate))

		if testResults.failed == 0 then
			print("🎉 All tests passed!")
		else
			print("❌ Some tests failed. Please review the failures above.")
		end

		return testResults.failed == 0
	end

	return MAFSTests
