--!strict
--[[
    TBRDS Remotes Management Module

    Centralized remote event management for TBRDS

    ARCHITECTURE ROLE:
    - Manages all remote events for client-server communication
    - Provides type-safe remote event access
    - Handles remote event creation and cleanup
    - Ensures consistent remote event naming

    USAGE:
    - Use to get remote events for communication
    - Handles both client and server initialization
    - Provides error handling for missing remotes

    *Dynamic Innovative Studio*
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Import configuration
local TBRDSConfig = require(ReplicatedStorage.Configurations.Systems.TBRDS_Configuration)

local TBRDSRemotes = {}

-- Remote event storage
local remoteEvents: { [string]: RemoteEvent } = {}
local remotesFolder: Folder? = nil

-- Constants
local REMOTES_FOLDER_NAME = "Remotes"
local TBRDS_FOLDER_NAME = "TBRDS"

-- Debug logging
local function debugLog(message: string)
  if TBRDSConfig.Settings.DebugMode then
    print(string.format("[TBRDS:Remotes]: %s", message))
  end
end

-- Get or create the TBRDS folder in ReplicatedStorage
local function getTBRDSFolder(): Folder
  local tbrdsFolder = ReplicatedStorage:FindFirstChild(TBRDS_FOLDER_NAME)

  if not tbrdsFolder then
    if RunService:IsServer() then
      tbrdsFolder = Instance.new("Folder")
      tbrdsFolder.Name = TBRDS_FOLDER_NAME
      tbrdsFolder.Parent = ReplicatedStorage
      debugLog("Created TBRDS folder")
    else
      -- Client waits for server to create it
      tbrdsFolder = ReplicatedStorage:WaitForChild(TBRDS_FOLDER_NAME, 10)
      if not tbrdsFolder then
        error("TBRDS: Failed to find TBRDS folder after timeout")
      end
    end
  end

  return tbrdsFolder
end

-- Get or create the remotes folder
function TBRDSRemotes.GetRemotesFolder(): Folder?
  if remotesFolder and remotesFolder.Parent then
    return remotesFolder
  end

  local tbrdsFolder = getTBRDSFolder()
  if not tbrdsFolder then
    warn("TBRDS: Cannot access TBRDS folder")
    return nil
  end

  remotesFolder = tbrdsFolder:FindFirstChild(REMOTES_FOLDER_NAME)

  if not remotesFolder then
    if RunService:IsServer() then
      remotesFolder = Instance.new("Folder")
      remotesFolder.Name = REMOTES_FOLDER_NAME
      remotesFolder.Parent = tbrdsFolder
      debugLog("Created remotes folder")
    else
      -- Client waits for server to create it
      remotesFolder = tbrdsFolder:WaitForChild(REMOTES_FOLDER_NAME, 10)
      if not remotesFolder then
        warn("TBRDS: Failed to find remotes folder after timeout")
        return nil
      end
    end
  end

  return remotesFolder
end

-- Get or create a specific remote event
local function getRemoteEvent(eventName: string): RemoteEvent?
  -- Check cache first
  if remoteEvents[eventName] and remoteEvents[eventName].Parent then
    return remoteEvents[eventName]
  end

  local folder = TBRDSRemotes.GetRemotesFolder()
  if not folder then
    warn("TBRDS: Cannot access remotes folder")
    return nil
  end

  -- Look for existing event
  local remoteEvent = folder:FindFirstChild(eventName)

  if not remoteEvent then
    if RunService:IsServer() then
      remoteEvent = Instance.new("RemoteEvent")
      remoteEvent.Name = eventName
      remoteEvent.Parent = folder
      debugLog(string.format("Created remote event: %s", eventName))
    else
      -- Client waits for server to create it
      remoteEvent = folder:WaitForChild(eventName, 10)
      if not remoteEvent then
        warn(string.format("TBRDS: Failed to find remote event '%s' after timeout", eventName))
        return nil
      end
    end
  end

  -- Cache the remote event
  remoteEvents[eventName] = remoteEvent
  return remoteEvent
end

-- Get the tag update remote event
function TBRDSRemotes.GetTagUpdateRemote(): RemoteEvent?
  return getRemoteEvent(TBRDSConfig.Remotes.TagUpdate)
end

-- Get the tag request remote event
function TBRDSRemotes.GetTagRequestRemote(): RemoteEvent?
  return getRemoteEvent(TBRDSConfig.Remotes.TagRequest)
end

-- Get the tag validation remote event
function TBRDSRemotes.GetTagValidationRemote(): RemoteEvent?
  return getRemoteEvent(TBRDSConfig.Remotes.TagValidation)
end

-- Get the security report remote event
function TBRDSRemotes.GetSecurityReportRemote(): RemoteEvent?
  return getRemoteEvent(TBRDSConfig.Remotes.SecurityReport)
end

-- Initialize all remote events (server-side)
function TBRDSRemotes.InitializeRemotes(): boolean
  if not RunService:IsServer() then
    warn("TBRDS: InitializeRemotes should only be called on the server")
    return false
  end

  debugLog("Initializing remote events...")

  local success = true

  -- Create all configured remote events
  for _, remoteName in pairs(TBRDSConfig.Remotes) do
    local remoteEvent = getRemoteEvent(remoteName)
    if not remoteEvent then
      warn(string.format("TBRDS: Failed to create remote event: %s", remoteName))
      success = false
    end
  end

  if success then
    debugLog("All remote events initialized successfully")
  else
    warn("TBRDS: Some remote events failed to initialize")
  end

  return success
end

-- Wait for all remote events to be available (client-side)
function TBRDSRemotes.WaitForRemotes(): boolean
  if RunService:IsServer() then
    warn("TBRDS: WaitForRemotes should only be called on the client")
    return false
  end

  debugLog("Waiting for remote events...")

  local success = true

  -- Wait for all configured remote events
  for _, remoteName in pairs(TBRDSConfig.Remotes) do
    local remoteEvent = getRemoteEvent(remoteName)
    if not remoteEvent then
      warn(string.format("TBRDS: Failed to find remote event: %s", remoteName))
      success = false
    end
  end

  if success then
    debugLog("All remote events found successfully")
  else
    warn("TBRDS: Some remote events were not found")
  end

  return success
end

-- Clean up remote events (for testing/cleanup)
function TBRDSRemotes.CleanupRemotes(): ()
  if not RunService:IsServer() then
    warn("TBRDS: CleanupRemotes should only be called on the server")
    return
  end

  debugLog("Cleaning up remote events...")

  -- Clear cache
  remoteEvents = {}

  -- Remove remotes folder if it exists
  if remotesFolder and remotesFolder.Parent then
    remotesFolder:Destroy()
    remotesFolder = nil
  end

  debugLog("Remote events cleaned up")
end

-- Get all remote events (for debugging)
function TBRDSRemotes.GetAllRemotes(): { [string]: RemoteEvent }
  local allRemotes = {}

  for configKey, remoteName in pairs(TBRDSConfig.Remotes) do
    local remoteEvent = getRemoteEvent(remoteName)
    if remoteEvent then
      allRemotes[configKey] = remoteEvent
    end
  end

  return allRemotes
end

-- Check if all remotes are available
function TBRDSRemotes.AreRemotesReady(): boolean
  for _, remoteName in pairs(TBRDSConfig.Remotes) do
    local remoteEvent = getRemoteEvent(remoteName)
    if not remoteEvent then
      return false
    end
  end

  return true
end

-- Initialize the remotes system
if RunService:IsServer() then
  -- Server initializes remotes immediately
  TBRDSRemotes.InitializeRemotes()
else
  -- Client waits for remotes to be available
  task.spawn(function()
    TBRDSRemotes.WaitForRemotes()
  end)
end

return TBRDSRemotes
