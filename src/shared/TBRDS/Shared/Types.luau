--!strict
--[[
    TBRDS Types Module

    Type definitions and interfaces for the Tag-Based Role Display System

    ARCHITECTURE ROLE:
    - Provides strict type definitions for all TBRDS components
    - Ensures type safety across client-server communication
    - Documents data structures and interfaces

    USAGE:
    - Import types for strict type checking
    - Use for API contracts and validation

    *Dynamic Innovative Studio*
]]

-- Player and Role Types
export type PlayerId = number
export type RoleName = string
export type GroupId = number
export type GroupRank = number

-- Style Configuration Types
export type TextStrokeStyle = {
  Color: Color3,
  Transparency: number,
}

export type GradientStyle = {
  Colors: { Color3 },
  Rotation: number,
}

export type RoleStyle = {
  Color: Color3,
  Font: Enum.Font,
  Image: string?,
  Gradient: GradientStyle?,
  FontStyle: string?,
  TextStroke: TextStrokeStyle?,
  GetText: ((Player) -> string)?,
}

-- Role Handler Types
export type RoleHandler = {
  Check: (Player) -> boolean,
  Style: RoleStyle,
}

export type RoleHandlersModule = {
  GetPlayerRole: (Player) -> <PERSON><PERSON><PERSON>,
  User: <PERSON><PERSON><PERSON><PERSON>,
  Supporter: <PERSON><PERSON><PERSON><PERSON>,
  Trusted: <PERSON><PERSON><PERSON><PERSON>,
  Developer: <PERSON><PERSON><PERSON><PERSON>,
  ["Junior Moderator"]: <PERSON><PERSON><PERSON><PERSON>,
  Investors: RoleHandler,
  ["Game Moderator"]: RoleHandler,
  ["Senior Moderator"]: RoleHandler,
  Anonmancer: RoleHandler,
  BleckWolf25: RoleHandler,
}

-- Player Data Types
export type PlayerTagData = {
  Role: RoleName,
  BillboardGUI: BillboardGui?,
  LastUpdated: number,
  ValidationCount: number,
  SecurityFlags: { string }?,
}

export type PlayerCache = {
  [Player]: PlayerTagData,
}

-- Event System Types
export type TagEventType =
  "TagChanged"
  | "TagAssigned"
  | "TagRemoved"
  | "RoleValidated"
  | "SecurityViolation"

export type TagEventData = {
  Player: Player,
  OldRole: RoleName?,
  NewRole: RoleName,
  Timestamp: number,
  Source: string,
  Metadata: { [string]: any }?,
}

export type EventCallback = (TagEventData) -> ()

export type EventSystem = {
  Subscribe: (TagEventType, EventCallback) -> string,
  Unsubscribe: (string) -> boolean,
  Fire: (TagEventType, TagEventData) -> (),
  GetSubscribers: (TagEventType) -> { EventCallback },
}

-- Validation Types
export type ValidationResult = {
  Success: boolean,
  Role: RoleName?,
  ErrorCode: string?,
  ErrorMessage: string?,
  SecurityFlags: { string }?,
}

export type ValidationContext = {
  Player: Player,
  RequestedRole: RoleName?,
  CurrentRole: RoleName?,
  Timestamp: number,
  Source: string,
}

-- Security Types
export type SecurityEvent = {
  Type: string,
  Player: Player,
  Severity: "Low" | "Medium" | "High" | "Critical",
  Description: string,
  Timestamp: number,
  Data: { [string]: any }?,
}

export type RateLimitData = {
  Requests: { number },
  LastReset: number,
}

export type RateLimiter = {
  Window: number,
  Limit: number,
  Requests: { [PlayerId]: RateLimitData },
  CheckLimit: (PlayerId) -> (boolean, number),
  Reset: (PlayerId) -> (),
}

-- Performance Types
export type PerformanceMetrics = {
  TagAssignments: number,
  ValidationTime: number,
  SecurityEvents: number,
  CacheHits: number,
  CacheMisses: number,
  ErrorCount: number,
  LastReset: number,
}

export type PerformanceMonitor = {
  RecordTagAssignment: () -> (),
  RecordValidationTime: (number) -> (),
  RecordSecurityEvent: () -> (),
  RecordCacheHit: () -> (),
  RecordCacheMiss: () -> (),
  RecordError: () -> (),
  GetMetrics: () -> PerformanceMetrics,
  Reset: () -> (),
}

-- API Types
export type TBRDSApi = {
  GetPlayerRole: (Player) -> RoleName,
  SetPlayerRole: (Player, RoleName) -> ValidationResult,
  RefreshPlayerTag: (Player) -> ValidationResult,
  GetPlayerTagData: (Player) -> PlayerTagData?,
  IsRoleValid: (Player, RoleName) -> boolean,
  GetRoleStyle: (RoleName) -> RoleStyle?,
  SubscribeToTagChanges: (EventCallback) -> string,
  UnsubscribeFromTagChanges: (string) -> boolean,
  GetPerformanceMetrics: () -> PerformanceMetrics,
  SetDebugMode: (boolean) -> (),
}

-- Configuration Types
export type BillboardSettings = {
  MaxDistance: number,
  StudsOffset: Vector3,
  Size: UDim2,
  AlwaysOnTop: boolean,
  LightInfluence: number,
}

export type AntiExploitSettings = {
  EnablePositionValidation: boolean,
  EnableRoleValidation: boolean,
  EnableDataStoreValidation: boolean,
  MaxRoleChangesPerMinute: number,
  SuspiciousActivityThreshold: number,
}

export type TBRDSConfiguration = {
  Settings: {
    DebugMode: boolean,
    EnablePerformanceMetrics: boolean,
    EnableEventSystem: boolean,
    RateLimit: {
      Window: number,
      MaxRequests: number,
    },
    MaxTagLength: number,
    MaxDisplayNameLength: number,
    TagValidationInterval: number,
    GroupRankCheckInterval: number,
    MaxCachedPlayers: number,
    BillboardSettings: BillboardSettings,
    AntiExploit: AntiExploitSettings,
  },
  Groups: {
    Primary: {
      Id: GroupId,
      Name: string,
    },
  },
  RolePriority: { RoleName },
  GamePasses: { [string]: number },
  Events: { [string]: string },
  Remotes: { [string]: string },
  ErrorCodes: { [string]: string },
}

-- Remote Communication Types
export type RemoteEventData = {
  Player: Player,
  Role: RoleName,
  Style: RoleStyle,
  Image: string?,
  Timestamp: number?,
}

export type TagRequestData = {
  Player: Player,
  RequestType: "Initial" | "Refresh" | "Validation",
  Timestamp: number,
}

return {}
