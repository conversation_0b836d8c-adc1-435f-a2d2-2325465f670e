--!strict

--[[
    - file: TBRDS_Configuration.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - TBRDS Configuration Module
      - Central configuration for the Tag-Based Role Display System (TBRDS)
      - Manages role priority and validation rules
      - Enables debug mode and performance monitoring
      - Defines security and rate limiting parameters
      - Used by both client and server components
]]

-- ============================================================================
-- TYPE ANNOTATIONS & INFER USAGE
-- ============================================================================

-- Rate limit configuration
type RateLimitConfig = {
	Window: number,
	MaxRequests: number,
}

-- Billboard display settings
type BillboardSettings = {
	MaxDistance: number,
	StudsOffset: Vector3,
	Size: UDim2,
	AlwaysOnTop: boolean,
	LightInfluence: number,
}

-- Anti-exploit configuration
type AntiExploitConfig = {
	EnablePositionValidation: boolean,
	EnableRoleValidation: boolean,
	EnableDataStoreValidation: boolean,
	MaxRoleChangesPerMinute: number,
	SuspiciousActivityThreshold: number,
}

-- TBRDS Settings structure
type TBRDSSettings = {
	DebugMode: boolean,
	EnablePerformanceMetrics: boolean,
	EnableEventSystem: boolean,
	RateLimit: RateLimitConfig,
	MaxTagLength: number,
	MaxDisplayNameLength: number,
	TagValidationInterval: number,
	GroupRankCheckInterval: number,
	MaxCachedPlayers: number,
	BillboardSettings: BillboardSettings,
	AntiExploit: AntiExploitConfig,
}

-- Group configuration
type GroupConfig = {
	Id: number,
	Name: string,
}

-- Cache configuration
type CacheConfig = {
	PlayerDataTTL: number,
	RoleDataTTL: number,
	GroupDataTTL: number,
	MaxCacheSize: number,
}

-- Debug configuration
type DebugConfig = {
	LogTagAssignments: boolean,
	LogValidationEvents: boolean,
	LogSecurityEvents: boolean,
	LogPerformanceMetrics: boolean,
	VerboseLogging: boolean,
}

-- Metrics configuration
type MetricsConfig = {
	TrackTagAssignments: boolean,
	TrackValidationTime: boolean,
	TrackSecurityEvents: boolean,
	TrackPerformance: boolean,
	MetricsRetentionTime: number,
}

-- Validation rules
type ValidationRules = {
	RequireCharacter: boolean,
	RequireValidUserId: boolean,
	RequireGroupMembership: boolean,
	ValidateRolePermissions: boolean,
	CheckGamePassOwnership: boolean,
}

-- TBRDS Configuration structure
type TBRDSConfigType = {
	Settings: TBRDSSettings,
	Groups: { [string]: GroupConfig },
	RolePriority: { string },
	GamePasses: { [string]: number },
	Events: { [string]: string },
	Remotes: { [string]: string },
	ErrorCodes: { [string]: string },
	Metrics: MetricsConfig,
	Debug: DebugConfig,
	ValidationRules: ValidationRules,
	Cache: CacheConfig,

	GetRolePriority: (role: string) -> number?,
	IsValidRole: (role: string) -> boolean,
	IsDebugMode: () -> boolean,
	SetDebugMode: (enabled: boolean) -> (),
}

-- ============================================================================
-- DEFINE LUAU MODULE
-- ============================================================================

local TBRDSConfig = {} :: TBRDSConfigType

-- ============================================================================
-- TBRDS SYSTEM SETTINGS
-- ============================================================================

TBRDSConfig.Settings = {
	DebugMode = false,
	EnablePerformanceMetrics = true,
	EnableEventSystem = true,

	-- Security Settings
	RateLimit = {
		Window = 60, -- seconds
		MaxRequests = 5, -- requests per window
	},

	-- Validation Settings
	MaxTagLength = 50,
	MaxDisplayNameLength = 100,

	-- Performance Settings
	TagValidationInterval = 60, -- seconds
	GroupRankCheckInterval = 30, -- seconds
	MaxCachedPlayers = 200,

	-- Display Settings
	BillboardSettings = {
		MaxDistance = 100,
		StudsOffset = Vector3.new(0, 3.5, 0),
		Size = UDim2.new(8, 0, 2, 0),
		AlwaysOnTop = true,
		LightInfluence = 1,
	},

	-- Anti-Exploit Settings
	AntiExploit = {
		EnablePositionValidation = true,
		EnableRoleValidation = true,
		EnableDataStoreValidation = true,
		MaxRoleChangesPerMinute = 3,
		SuspiciousActivityThreshold = 10,
	},
} :: TBRDSSettings

-- ============================================================================
-- GROUP CONFIGURATION
-- ============================================================================

TBRDSConfig.Groups = {
	Primary = {
		Id = 34320208,
		Name = "Dynamic Innovative Studio",
	},
} :: { [string]: GroupConfig }

-- ============================================================================
-- ROLE PRIORITY CONFIGURATION (highest to lowest)
-- ============================================================================

TBRDSConfig.RolePriority = {
	"BleckWolf25", -- Founder (highest priority)
	"Anonmancer", -- Co-Founder
	"Senior Moderator",
	"Game Moderator",
	"Junior Moderator",
	"Developer",
	"Investors",
	"Trusted",
	"Supporter",
	"User", -- Default role (lowest priority)
} :: { string }

-- ============================================================================
-- GAMEPASS CONFIGURATION
-- ============================================================================

TBRDSConfig.GamePasses = {
	Supporter = 99445069658101,
} :: { [string]: number }

-- ============================================================================
-- EVENT NAMES
-- ============================================================================

TBRDSConfig.Events = {
	TagChanged = "TagChanged",
	TagAssigned = "TagAssigned",
	TagRemoved = "TagRemoved",
	RoleValidated = "RoleValidated",
	SecurityViolation = "SecurityViolation",
} :: { [string]: string }

-- ============================================================================
-- REMOTE EVENT NAMES
-- ============================================================================

TBRDSConfig.Remotes = {
	TagUpdate = "TagRemote",
	TagRequest = "TagRequestRemote",
	TagValidation = "TagValidationRemote",
	SecurityReport = "SecurityReportRemote",
} :: { [string]: string }

-- ============================================================================
-- ERROR CODES
-- ============================================================================

TBRDSConfig.ErrorCodes = {
	RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED",
	INVALID_PLAYER = "INVALID_PLAYER",
	INVALID_ROLE = "INVALID_ROLE",
	SECURITY_VIOLATION = "SECURITY_VIOLATION",
	DATASTORE_ERROR = "DATASTORE_ERROR",
	VALIDATION_FAILED = "VALIDATION_FAILED",
} :: { [string]: string }

-- ============================================================================
-- PERFORMANCE METRICS CONFIGURATION
-- ============================================================================

TBRDSConfig.Metrics = {
	TrackTagAssignments = true,
	TrackValidationTime = true,
	TrackSecurityEvents = true,
	TrackPerformance = true,
	MetricsRetentionTime = 3600, -- 1 hour in seconds
} :: MetricsConfig

-- ============================================================================
-- DEBUG CONFIGURATION
-- ============================================================================

TBRDSConfig.Debug = {
	LogTagAssignments = true,
	LogValidationEvents = true,
	LogSecurityEvents = true,
	LogPerformanceMetrics = false,
	VerboseLogging = false,
} :: DebugConfig

-- ============================================================================
-- VALIDATION RULES
-- ============================================================================

TBRDSConfig.ValidationRules = {
	RequireCharacter = true,
	RequireValidUserId = true,
	RequireGroupMembership = false, -- For default users
	ValidateRolePermissions = true,
	CheckGamePassOwnership = true,
} :: ValidationRules

-- ============================================================================
-- CACHE CONFIGURATION
-- ============================================================================

TBRDSConfig.Cache = {
	PlayerDataTTL = 300, -- 5 minutes
	RoleDataTTL = 600, -- 10 minutes
	GroupDataTTL = 1800, -- 30 minutes
	MaxCacheSize = 1000,
} :: CacheConfig

-- ============================================================================
-- TBRDS UTILITY FUNCTIONS
-- ============================================================================

-- Role priority retrieval
function TBRDSConfig.GetRolePriority(role: string): number?
	for index: number, roleName: string in ipairs(TBRDSConfig.RolePriority) do
		if roleName == role then
			return index
		end
	end
	return nil -- Role not found
end

-- Role validation
function TBRDSConfig.IsValidRole(role: string): boolean
	for _, roleName: string in ipairs(TBRDSConfig.RolePriority) do
		if roleName == role then
			return true
		end
	end
	return false
end

-- Debug mode utilities
function TBRDSConfig.IsDebugMode(): boolean
	return TBRDSConfig.Settings.DebugMode
end

-- Enables or disables debug mode
function TBRDSConfig.SetDebugMode(enabled: boolean): ()
	TBRDSConfig.Settings.DebugMode = enabled
end

-- ============================================================================
-- EXPORT LUAU MODULE
-- ============================================================================
return TBRDSConfig :: TBRDSConfigType
