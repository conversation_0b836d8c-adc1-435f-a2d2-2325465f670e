--[[
	MCS Utils Module
	- Split command text into tokens, handling quoted arguments
	- Consistent debug logging with configuration check
    - Performance tracking capabilities
]]

-- Services
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Config reference (can be nil during testing/development)
local Configuration = ReplicatedStorage:FindFirstChild("Configurations")
  and ReplicatedStorage.Configurations:FindFirstChild("Systems")
  and ReplicatedStorage.Configurations.Systems:FindFirstChild("MCS")

-- Utils
local Utils = {}

-- Performance timer utilities
local timers = {}

-- Check if Debug Mode is enabled
function Utils.isDebugMode()
  -- Default to false if Configuration is not found
  if not Configuration then
    return false
  end

  return Configuration:GetAttribute("DebugMode") == true
end

-- Debug print function with module identifier
function Utils.print(moduleName, message)
  if Utils.isDebugMode() then
    print(string.format("[MCS:%s]: %s", moduleName, message))
  end
end

function Utils.startTimer(name)
  if Utils.isDebugMode() then
    timers[name] = os.clock()
  end
end

function Utils.endTimer(moduleName, name)
  if Utils.isDebugMode() and timers[name] then
    local duration = os.clock() - timers[name]
    Utils.print(moduleName, string.format("%s took %.3f ms", name, duration * 1000))
    timers[name] = nil
  end
end

-- Split command text into tokens, handling quoted arguments
function Utils.splitCommandText(text)
  local tokens = {}
  local current = 1
  while current <= #text do
    -- Skip whitespace
    local whitespace = text:match("^%s+", current)
    if whitespace then
      current = current + #whitespace
    end
    if current > #text then
      break
    end

    -- Handle quoted arguments
    if text:sub(current, current) == '"' then
      local endQuote = text:find('"', current + 1, true)
      if endQuote then
        table.insert(tokens, text:sub(current + 1, endQuote - 1))
        current = endQuote + 1
      else
        table.insert(tokens, text:sub(current + 1))
        break
      end
    else
      -- Regular argument
      local nextSpace = text:find("%s", current) or #text + 1
      table.insert(tokens, text:sub(current, nextSpace - 1))
      current = nextSpace
    end
  end
  return tokens
end

-- Sanitize input to prevent exploits
function Utils.sanitize(text)
  return text:gsub("[^%w%s%p]", "") -- Remove non-printable characters
end

return Utils
