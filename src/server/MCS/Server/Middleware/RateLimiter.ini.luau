--[[
    Rate Limiter Middleware

    Prevents command spam by limiting how frequently players can use commands.
    Different rate limits can be set for different permission levels.
]]

local RateLimiter = {}

local ServerScriptService = game:GetService("ServerScriptService")
local PermissionService = require(ServerScriptService.MCS.Server.PermissionService)

-- Store last command timestamps per player
-- Format: {userId = {lastCommandTime = timestamp, commandCount = count}}
local userCommandHistory = {}

-- Configuration for rate limits based on permission level
local RATE_LIMITS = {
  -- Default player level
  [PermissionService.Levels.PLAYER] = { maxCommands = 3, periodSeconds = 5 },

  -- Staff levels
  [PermissionService.Levels.DISDevelopers] = { maxCommands = 5, periodSeconds = 5 },
  [PermissionService.Levels.JuniorModerators] = { maxCommands = 5, periodSeconds = 5 },
  [PermissionService.Levels.GameModerators] = { maxCommands = 8, periodSeconds = 5 },
  [PermissionService.Levels.SeniorModerators] = { maxCommands = 12, periodSeconds = 5 },
  [PermissionService.Levels.Anonmancer] = { maxCommands = 20, periodSeconds = 5 },
  [PermissionService.Levels.BleckWolf25] = { maxCommands = 20, periodSeconds = 5 },
}

-- Clean up old entries to prevent memory leak
local function cleanupOldEntries()
  local currentTime = os.time()
  for userId, data in pairs(userCommandHistory) do
    if currentTime - data.lastCommandTime > 60 then -- Remove after 1 minute of inactivity
      userCommandHistory[userId] = nil
    end
  end
end

-- Process middleware
function RateLimiter.process(player, commandName, args)
  local userId = player.UserId
  local permLevel = PermissionService.getPermissionLevel(player)
  local rateLimit = RATE_LIMITS[permLevel] or RATE_LIMITS[PermissionService.Levels.PLAYER]
  local currentTime = os.time()

  -- Clean up occasionally
  if math.random() < 0.05 then -- 5% chance to clean up on each command
    cleanupOldEntries()
  end

  -- Initialize or update user history
  if not userCommandHistory[userId] then
    userCommandHistory[userId] = {
      lastCommandTime = currentTime,
      commandCount = 1,
    }
    return true
  end

  local history = userCommandHistory[userId]

  -- Reset count if period has passed
  if currentTime - history.lastCommandTime > rateLimit.periodSeconds then
    history.commandCount = 1
    history.lastCommandTime = currentTime
    return true
  end

  -- Increment count and check limit
  history.commandCount += 1

  if history.commandCount > rateLimit.maxCommands then
    local waitTime = rateLimit.periodSeconds - (currentTime - history.lastCommandTime)
    return false, string.format("Please wait %d seconds before using another command", waitTime)
  end

  history.lastCommandTime = currentTime
  return true
end

return RateLimiter
