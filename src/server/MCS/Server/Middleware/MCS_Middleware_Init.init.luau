--[[
    Middleware System

    Handles pre-processing for commands:
    - Authentication (verify player identity)
    - Authorization (check permissions)
    - Logging (record command usage)
    - Rate limiting (prevent command spam)
    - Input validation (verify arguments)
]]

local Middleware = {}

-- Placeholder for middleware chain
local middlewareChain = nil

-- Load individual middleware modules
local function loadMiddleware()
  -- Use pcall for each middleware to prevent loading failures
  local chain = {}

  -- Authentication middleware
  local success, authModule = pcall(function()
    return require(script.Parent.Authentication)
  end)
  if success then
    table.insert(chain, authModule)
    print("MCS: Authentication middleware loaded")
  else
    warn("MCS: Failed to load Authentication middleware:", authModule)
  end

  -- Permission middleware (required for command authorization)
  local success, permissionModule = pcall(function()
    return require(script.Parent.Parent.PermissionService)
  end)
  if success then
    -- Create a wrapper middleware function
    local permissionMiddleware = {
      process = function(player, commandName, args)
        -- Check if player has permission to use this command
        if permissionModule.canUseCommand(player, commandName) then
          return true
        else
          return false, "Insufficient permissions to use this command"
        end
      end,
    }
    table.insert(chain, permissionMiddleware)
    print("MCS: Permission middleware loaded")
  else
    warn("MCS: Failed to load Permission middleware:", permissionModule)
  end

  -- Logger middleware
  local success, loggerModule = pcall(function()
    return require(script.Parent.Logger)
  end)
  if success then
    if type(loggerModule.process) ~= "function" then
      -- Create a default implementation if missing
      loggerModule.process = function(player, commandName, args)
        print(
          "MCS_LOG: Player",
          player.Name,
          "used command",
          commandName,
          "with args:",
          table.concat(args, ", ")
        )
        return true
      end
    end
    table.insert(chain, loggerModule)
    print("MCS: Logger middleware loaded")
  else
    -- Create a basic logger if module failed to load
    local basicLogger = {
      process = function(player, commandName, args)
        print(
          "MCS_LOG: Player",
          player.Name,
          "used command",
          commandName,
          "with args:",
          table.concat(args, ", ")
        )
        return true
      end,
    }
    table.insert(chain, basicLogger)
    print("MCS: Basic logger middleware loaded")
  end

  -- RateLimiter middleware
  local success, rateLimiterModule = pcall(function()
    return require(script.Parent.RateLimiter)
  end)
  if success then
    table.insert(chain, rateLimiterModule)
    print("MCS: RateLimiter middleware loaded")
  else
    -- Create a simple rate limiter if module failed to load
    local rateLimits = {}
    local simpleRateLimiter = {
      process = function(player, commandName, args)
        local userId = player.UserId
        local currentTime = os.time()

        -- Initialize player's rate limit entry if it doesn't exist
        if not rateLimits[userId] then
          rateLimits[userId] = {
            lastCommand = currentTime,
            commandCount = 0,
          }
          return true
        end

        local playerLimit = rateLimits[userId]

        -- Reset counter if more than 5 seconds have passed
        if currentTime - playerLimit.lastCommand > 5 then
          playerLimit.commandCount = 0
        end

        -- Update command count and timestamp
        playerLimit.commandCount = playerLimit.commandCount + 1
        playerLimit.lastCommand = currentTime

        -- Limit to 5 commands per 5 seconds
        if playerLimit.commandCount > 5 then
          return false, "You are sending commands too quickly. Please wait a moment."
        end

        return true
      end,
    }
    table.insert(chain, simpleRateLimiter)
    print("MCS: Simple RateLimiter middleware loaded")
  end

  return chain
end

-- Initialize middleware
function Middleware.init()
  -- Load middleware if not already loaded
  if not middlewareChain then
    middlewareChain = loadMiddleware()
    print("MCS: Middleware chain initialized with", #middlewareChain, "middleware modules")
  end
end

-- Execute middleware chain
function Middleware.run(player, commandName, args)
  -- Initialize middleware chain if not already loaded
  if not middlewareChain then
    print("MCS: Initializing middleware chain on first use")
    middlewareChain = loadMiddleware()
  end

  -- Ensure args is a table
  args = args or {}

  -- Debug information
  print("MCS: Running middleware for", player.Name, "command:", commandName)

  -- Run each middleware in the chain
  for i, middleware in ipairs(middlewareChain) do
    if type(middleware.process) ~= "function" then
      warn("MCS: Middleware at position", i, "has no process function")
      continue
    end

    local success, result = pcall(function()
      return middleware.process(player, commandName, args)
    end)

    if not success then
      warn("MCS: Middleware error:", result)
      return false, "Internal server error in middleware"
    end

    -- If middleware rejected the command
    if result == false then
      -- Check if there's an error message provided
      local errorMessage
      if type(result) == "table" and result[2] then
        errorMessage = result[2]
      else
        errorMessage = "Middleware check failed"
      end
      return false, errorMessage
    end
  end

  -- All middleware passed
  return true
end

-- Register a new middleware in the chain
function Middleware.register(middlewareModule)
  if not middlewareChain then
    middlewareChain = loadMiddleware()
  end

  if type(middlewareModule.process) ~= "function" then
    warn("MCS: Attempted to register invalid middleware (missing process function)")
    return false
  end

  table.insert(middlewareChain, middlewareModule)
  return true
end

return Middleware
