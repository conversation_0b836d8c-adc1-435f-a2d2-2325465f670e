--[[
    CommandDispatcher

    Central hub for command registration, processing, and execution:
    - <PERSON><PERSON> command registration and discovery
    - Processes command inputs from clients
    - Integrates with middleware chain
    - Provides autocomplete functionality
    - Routes commands to appropriate handlers
]]
local CommandDispatcher = {}

local ServerScriptService = game:GetService("ServerScriptService")
local Constants = require(game.ReplicatedStorage.MCS.Shared.Constants)
local Middleware = require(script.Parent.Middleware.MCS_MiddlewareInitiliazation)
local Utils = require(game.ReplicatedStorage.MCS.Shared.Utils)

-- Command registry - stores all loaded commands
-- format: {commandName = {module = moduleRef, aliases = {alias1, alias2}}}
local commandRegistry = {}

-- Command alias lookup for quick access
-- format: {alias = commandName}
local aliasLookup = {}

-- Command Category for better organization
local commandCategories = {
  MODERATION = "Moderation",
  UTILITY = "Utility",
  SYSTEM = "System",
  USER = "User",
  GAME = "Game",
}

-- Function to parse command string into command name and arguments
local function parseCommand(commandText)
  -- Debug message
  print("MCS Command Dispatcher parseCommand Used")

  -- Validate input
  if type(commandText) ~= "string" then
    print("MCS: Invalid command text type:", type(commandText))
    return nil, nil
  end

  local text = commandText:match("^%s*" .. Constants.Prefix .. "(.+)%s*$")
  if not text then
    print("MCS: Command text doesn't match prefix pattern:", commandText)
    return nil, nil
  end

  local tokens = Utils.splitCommandText(text)
  if #tokens == 0 then
    print("MCS: No tokens found in command text")
    return nil, nil
  end

  -- Get base command name
  local commandName = tokens[1]:lower()
  local args = { unpack(tokens, 2) }

  print("MCS: Parsed command:", commandName, "with", #args, "arguments")
  return commandName, args
end

-- Get Command List
function CommandDispatcher.getCommandList()
  local commands = {}
  for name in pairs(commandRegistry) do
    table.insert(commands, name)
  end
  return commands
end

-- Parse Command Version
local function parseVersion(commandName)
  local base, version = commandName:match("^(.-)_v(%d+)$")
  return base or commandName, tonumber(version)
end

-- Initialize by loading all command modules
function CommandDispatcher.init()
  -- Clear existing registries to prevent duplicates on reload
  commandRegistry = {}
  aliasLookup = {}

  -- Commands Folder for loading
  local commandsFolder = ServerScriptService.MCS.Commands

  -- Check if the commands folder exists
  if not commandsFolder then
    warn("MCS: Commands folder not found at ServerScriptService.MCS.Commands")
    return
  end

  -- Function to recursively search for command modules
  local function loadCommandsFromFolder(folder, commandPath)
    commandPath = commandPath or {}

    for _, item in ipairs(folder:GetChildren()) do
      if item:IsA("ModuleScript") then
        -- Get base command name without folders
        local baseName = item.Name:lower()

        -- Version handling
        local cleanName, version = parseVersion(baseName)

        -- Register command
        local existing = commandRegistry[cleanName]
        if not existing or (version and version > (existing.version or 0)) then
          local success, commandModule = pcall(require, item)
          if success then
            -- Validate command module structure
            if type(commandModule) ~= "table" or type(commandModule.Execute) ~= "function" then
              warn("MCS: Invalid command module format:", item.Name)
              continue
            end

            commandRegistry[cleanName] = {
              module = commandModule,
              version = version or 1,
              aliases = commandModule.Aliases or {},
              path = item:GetFullName(), -- Store path for debugging
            }

            print("MCS: Registered command:", cleanName, "from", item:GetFullName())

            -- Register aliases
            if commandModule.Aliases then
              for _, alias in ipairs(commandModule.Aliases) do
                aliasLookup[alias:lower()] = cleanName
                print("MCS: Registered alias:", alias, "->", cleanName)
              end
            end
          else
            warn("MCS: Failed to require command module:", item.Name, commandModule)
          end
        end
      elseif item:IsA("Folder") then
        -- Recurse into subfolders but don't include folder names in command paths
        loadCommandsFromFolder(item, {})
      end
    end
  end

  loadCommandsFromFolder(commandsFolder)
  print("MCS: Loaded", #commandRegistry, "commands")
end

-- Process a command from a player
function CommandDispatcher.processCommand(player, commandText)
  print("Raw commandText:", commandText) -- Debug log

  if not player or not player.Parent then
    warn("MCS: Invalid player reference in processCommand")
    return false, "Invalid player"
  end

  if type(commandText) ~= "string" then
    warn("MCS: Invalid commandText type:", type(commandText))
    return false, "Invalid command format"
  end

  local commandName, args = parseCommand(commandText)

  if args then
    print("Parsed commandName:", commandName, "args:", table.concat(args, ", ")) -- Debug log
  else
    print("Parsed commandName:", commandName, "args: none") -- Debug log
  end

  -- If not a commandName formatted, return invalid
  if not commandName then
    return false, "Invalid command format"
  end

  -- Resolve command through aliases
  local resolvedCommand = aliasLookup[commandName] or commandName
  print("Resolved command:", resolvedCommand) -- Debug log

  -- Get the command module
  local commandData = commandRegistry[resolvedCommand]
  print("Looked up command data for:", resolvedCommand)

  if not commandData then
    return false, "Unknown command: " .. commandName
  end

  -- Run middleware chain
  local passMiddleware, middlewareError = Middleware.run(player, resolvedCommand, args)
  if not passMiddleware then
    return false, middlewareError or "Command execution denied"
  end

  -- Execute command
  local success, result = pcall(function()
    return commandData.module.Execute(player, args)
  end)

  if not success then
    warn("MCS: Command execution error:", result)
    return false, "An error occurred while executing the command"
  end

  -- Check for correct result format
  if type(result) ~= "table" or result.success == nil then
    warn("MCS: Command returned invalid result format:", result)
    return false, "Command returned invalid format"
  end

  return result.success, result.message
end

-- Get autocomplete suggestions for partial command
function CommandDispatcher.getAutocompleteSuggestions(player, partialCommand)
  -- Basic implementation
  local suggestions = {}
  local partialLower = partialCommand:lower()

  -- Check if command prefix is included
  local prefix = partialCommand:match("^%s*!(.*)")
  local searchText = prefix or partialLower

  -- If no prefix found, return empty suggestions (not a command)
  if not prefix and partialCommand:sub(1, 1) ~= "!" then
    return {}
  end

  -- If empty search with prefix, suggest all commands
  if searchText == "" then
    for commandName, _ in pairs(commandRegistry) do
      table.insert(suggestions, "!" .. commandName)
    end
    return suggestions
  end

  -- Search for matching commands and aliases
  for commandName, commandData in pairs(commandRegistry) do
    if commandName:sub(1, #searchText) == searchText then
      table.insert(suggestions, "!" .. commandName)
    end

    -- Check aliases
    for _, alias in ipairs(commandData.aliases or {}) do
      if alias:sub(1, #searchText) == searchText then
        table.insert(suggestions, "!" .. alias)
      end
    end
  end

  return suggestions
end

-- Dynamic Loading (hot-reload)
function CommandDispatcher.reload()
  -- Clear existing registries
  commandRegistry = {}
  aliasLookup = {}

  -- Reinitialize
  CommandDispatcher.init()

  return true, "Command system reloaded successfully"
end

-- Return available commands for the specified player (respects permissions)
function CommandDispatcher.getAvailableCommands(player)
  local PermissionService = require(script.Parent.PermissionService)
  local availableCommands = {}

  for commandName, commandData in pairs(commandRegistry) do
    if PermissionService.canUseCommand(player, commandName) then
      table.insert(availableCommands, {
        name = commandName,
        description = commandData.module.Description or "No description",
        usage = commandData.module.Usage or "!" .. commandName,
        aliases = commandData.aliases or {},
      })
    end
  end

  return availableCommands
end

return CommandDispatcher
