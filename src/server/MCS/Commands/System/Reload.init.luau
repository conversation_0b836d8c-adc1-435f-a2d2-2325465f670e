--[[
    Reload System Command

    Reloads all the commands (hot-reload).
    Permissions: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, SeniorModerators level required
]]
return {
  Description = "Reload all commands",
  Usage = "!reloadcommands",
  PermissionLevel = 200, -- BleckWolf25 level
  Aliases = { "rc" },

  Execute = function(player)
    local CommandDispatcher = require(game.ServerScriptService.MCS.Server.CommandDispatcher)
    CommandDispatcher.reload()
    return {
      success = true,
      message = "Successfully reloaded all commands",
    }
  end,
}
