--[[
    FootStep_Server_Initialization.server.luau

    Server script for initializing the footstep system

    ARCHITECTURE ROLE:
    - Entry point for the server footstep system
    - Creates necessary folders and references
    - Loads and initializes the FootstepManager module

    PLACEMENT:
    - This script should be placed in ServerScriptService

    *Dynamic Innovative Studio*
]]

-- Services

-- Reference the server-side FootstepManager module
local FootstepManager = script.Parent:FindFirstChild("FootStep_Server_Manager")
if not FootstepManager then
  warn("MAFS: FootstepManager module not found in expected location")
  return
end

-- Initialize the server-side footstep system
local footstepSystem = require(FootstepManager)
local success = footstepSystem.Initialize()

if success then
  print("MAFS: Server-side footstep system initialized successfully")
else
  warn("MAFS: Failed to initialize server-side footstep system")
end
