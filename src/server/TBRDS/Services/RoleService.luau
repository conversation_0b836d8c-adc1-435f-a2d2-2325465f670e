--!strict
--[[
    TBRDS Role Service

    Role determination and validation service for TBRDS

    ARCHITECTURE ROLE:
    - Determines player roles based on various criteria
    - Validates role assignments and permissions
    - Manages role handlers and priority system
    - Provides role-related utilities and caching

    RESPONSIBILITIES:
    - Load and manage role handlers
    - Determine player roles based on priority
    - Validate role permissions and requirements
    - Cache role determinations for performance
    - Handle role change notifications

    *Dynamic Innovative Studio*
]]

local MarketplaceService = game:GetService("MarketplaceService")
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Import modules
local ConfigurationService = require(script.Parent.ConfigurationService)
local EventSystem = require(ReplicatedStorage.TBRDS.Shared.EventSystem)
local PerformanceMonitor = require(ReplicatedStorage.TBRDS.Shared.PerformanceMonitor)
local Types = require(ReplicatedStorage.TBRDS.Shared.Types)

type RoleHandler = Types.RoleHandler
type RoleStyle = Types.RoleStyle
type ValidationResult = Types.ValidationResult

local RoleService = {}

-- Service state
local isInitialized = false
local roleHandlers: { [string]: RoleHandler } = {}
local roleCache: { [Player]: { role: string, timestamp: number, validationCount: number } } = {}
local rolePriority: { string } = {}

-- Cache settings
local CACHE_TTL = 300 -- 5 minutes
local MAX_CACHE_SIZE = 1000

-- Debug logging
local function debugLog(message: string)
  if ConfigurationService.IsDebugMode() then
    print(string.format("[TBRDS:RoleService]: %s", message))
  end
end

-- Initialize the role service
function RoleService.Initialize(): boolean
  if isInitialized then
    debugLog("Role service already initialized")
    return true
  end

  debugLog("Initializing role service...")

  -- Load configuration
  local config = ConfigurationService.GetConfiguration()
  rolePriority = config.RolePriority

  -- Load role handlers
  local success, err = pcall(function()
    local RoleHandlers = require(script.Parent.Parent.TBRDS_RoleHandlers)

    -- Validate and load each role handler
    for _, roleName in ipairs(rolePriority) do
      if RoleHandlers[roleName] then
        roleHandlers[roleName] = RoleHandlers[roleName]
        debugLog(string.format("Loaded role handler: %s", roleName))
      else
        warn(string.format("TBRDS: Missing role handler for: %s", roleName))
      end
    end
  end)

  if not success then
    warn("TBRDS: Failed to load role handlers: " .. tostring(err))
    return false
  end

  -- Set up cache cleanup
  task.spawn(function()
    while isInitialized do
      task.wait(60) -- Clean every minute
      RoleService.CleanupCache()
    end
  end)

  isInitialized = true
  debugLog("Role service initialized successfully")
  return true
end

-- Get a player's role with caching
function RoleService.GetPlayerRole(player: Player): string
  if not player or not player.Parent then
    return "User"
  end

  -- Check cache first
  local cached = roleCache[player]
  if cached and (os.time() - cached.timestamp) < CACHE_TTL then
    PerformanceMonitor.RecordCacheHit()
    return cached.role
  end

  PerformanceMonitor.RecordCacheMiss()

  -- Determine role
  local startTime = tick()
  local role = RoleService.DeterminePlayerRole(player)
  local validationTime = (tick() - startTime) * 1000

  PerformanceMonitor.RecordValidationTime(validationTime)

  -- Update cache
  roleCache[player] = {
    role = role,
    timestamp = os.time(),
    validationCount = (cached and cached.validationCount or 0) + 1,
  }

  -- Clean cache if it's getting too large
  if RoleService.GetCacheSize() > MAX_CACHE_SIZE then
    RoleService.CleanupCache()
  end

  return role
end

-- Determine player role based on priority
function RoleService.DeterminePlayerRole(player: Player): string
  -- Check roles in priority order (highest to lowest)
  for _, roleName in ipairs(rolePriority) do
    local roleHandler = roleHandlers[roleName]
    if roleHandler and roleHandler.Check then
      local success, result = pcall(roleHandler.Check, player)
      if success and result then
        debugLog(string.format("Player %s assigned role: %s", player.Name, roleName))
        return roleName
      end
    end
  end

  -- Default to User if no other role matches
  debugLog(string.format("Player %s assigned default role: User", player.Name))
  return "User"
end

-- Validate if a player qualifies for a specific role
function RoleService.ValidatePlayerRole(player: Player, roleName: string): ValidationResult
  local result: ValidationResult = {
    Success = false,
    Role = nil,
    ErrorCode = nil,
    ErrorMessage = nil,
    SecurityFlags = nil,
  }

  -- Check if player exists
  if not player or not player.Parent then
    result.ErrorCode = ConfigurationService.GetErrorCodes().INVALID_PLAYER
    result.ErrorMessage = "Invalid player"
    return result
  end

  -- Check if role exists
  local roleHandler = roleHandlers[roleName]
  if not roleHandler then
    result.ErrorCode = ConfigurationService.GetErrorCodes().INVALID_ROLE
    result.ErrorMessage = string.format("Unknown role: %s", roleName)
    return result
  end

  -- Validate role requirements
  local success, qualifies = pcall(roleHandler.Check, player)
  if not success then
    result.ErrorCode = ConfigurationService.GetErrorCodes().VALIDATION_FAILED
    result.ErrorMessage = string.format("Role validation failed: %s", tostring(qualifies))
    PerformanceMonitor.RecordError("ROLE_VALIDATION_ERROR")
    return result
  end

  if qualifies then
    result.Success = true
    result.Role = roleName
  else
    result.ErrorCode = ConfigurationService.GetErrorCodes().VALIDATION_FAILED
    result.ErrorMessage = string.format("Player does not qualify for role: %s", roleName)
  end

  return result
end

-- Get role style information
function RoleService.GetRoleStyle(roleName: string): RoleStyle?
  local roleHandler = roleHandlers[roleName]
  if roleHandler and roleHandler.Style then
    return roleHandler.Style
  end
  return nil
end

-- Check if a role exists
function RoleService.IsValidRole(roleName: string): boolean
  return roleHandlers[roleName] ~= nil
end

-- Get all available roles
function RoleService.GetAvailableRoles(): { string }
  local roles = {}
  for roleName in pairs(roleHandlers) do
    table.insert(roles, roleName)
  end
  return roles
end

-- Get role priority list
function RoleService.GetRolePriority(): { string }
  return rolePriority
end

-- Refresh a player's role (force re-evaluation)
function RoleService.RefreshPlayerRole(player: Player): string
  -- Clear cache for this player
  roleCache[player] = nil

  -- Get fresh role determination
  local role = RoleService.GetPlayerRole(player)

  debugLog(string.format("Refreshed role for %s: %s", player.Name, role))
  return role
end

-- Get players with a specific role
function RoleService.GetPlayersWithRole(roleName: string): { Player }
  local players = {}

  for player in pairs(roleCache) do
    if player.Parent and roleCache[player].role == roleName then
      table.insert(players, player)
    end
  end

  return players
end

-- Get role statistics
function RoleService.GetRoleStatistics(): { [string]: number }
  local stats = {}

  for player in pairs(roleCache) do
    if player.Parent then
      local role = roleCache[player].role
      stats[role] = (stats[role] or 0) + 1
    end
  end

  return stats
end

-- Clean up expired cache entries
function RoleService.CleanupCache(): ()
  local currentTime = os.time()
  local removedCount = 0

  for player, data in pairs(roleCache) do
    -- Remove if player left or cache expired
    if not player.Parent or (currentTime - data.timestamp) > CACHE_TTL then
      roleCache[player] = nil
      removedCount = removedCount + 1
    end
  end

  if removedCount > 0 then
    debugLog(string.format("Cleaned up %d expired cache entries", removedCount))
  end
end

-- Get cache size
function RoleService.GetCacheSize(): number
  local count = 0
  for _ in pairs(roleCache) do
    count = count + 1
  end
  return count
end

-- Get cache information for debugging
function RoleService.GetCacheInfo(): { [string]: any }
  local info = {
    size = RoleService.GetCacheSize(),
    maxSize = MAX_CACHE_SIZE,
    ttl = CACHE_TTL,
    entries = {},
  }

  for player, data in pairs(roleCache) do
    if player.Parent then
      table.insert(info.entries, {
        playerName = player.Name,
        role = data.role,
        age = os.time() - data.timestamp,
        validationCount = data.validationCount,
      })
    end
  end

  return info
end

-- Handle player leaving
function RoleService.HandlePlayerLeaving(player: Player): ()
  if roleCache[player] then
    roleCache[player] = nil
    debugLog(string.format("Removed cache entry for leaving player: %s", player.Name))
  end
end

-- Get service status
function RoleService.GetServiceStatus(): { [string]: any }
  return {
    initialized = isInitialized,
    roleHandlersLoaded = (function()
      local count = 0
      for _ in pairs(roleHandlers) do
        count = count + 1
      end
      return count
    end)(),
    cacheSize = RoleService.GetCacheSize(),
    rolePriorityCount = #rolePriority,
  }
end

-- Cleanup service
function RoleService.Cleanup(): ()
  roleCache = {}
  roleHandlers = {}
  rolePriority = {}
  isInitialized = false
  debugLog("Role service cleaned up")
end

return RoleService
