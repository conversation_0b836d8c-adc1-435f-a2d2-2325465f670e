--[[
    TBRDS_Main_Server.server.luau

    - Server Manager Script of Tag-Based Role Display System (TBRDS)
    - This script handles server-side logic for assigning and managing player tags
    - Tags are now server-side and replicated to all clients

    *Dynamic Innovative Studio*
]]

-- Services
local DataStoreService = game:GetService("DataStoreService")
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerStorage = game:GetService("ServerStorage")

-- Data Storage
local SpecialTagsStore = DataStoreService:GetDataStore("SpecialTags")

-- Create the remotes for the system
local RemotesFolder = ReplicatedStorage.TBRDS.Remotes

-- TagRemote: Used to update clients when tags change
local TagRemote = Instance.new("RemoteEvent")
TagRemote.Name = "TagRemote"
TagRemote.Parent = RemotesFolder

-- TagRequestRemote: Used by clients to request their tag
local TagRequestRemote = Instance.new("RemoteEvent")
TagRequestRemote.Name = "TagRequestRemote"
TagRequestRemote.Parent = RemotesFolder

-- Ensure proper folder structure
local TBRDS = ReplicatedStorage:FindFirstChild("TBRDS") or Instance.new("Folder")
TBRDS.Name = "TBRDS"
TBRDS.Parent = ReplicatedStorage

-- Place remotes in the proper location
RemotesFolder.Parent = TBRDS

-- Create Shared folder if it doesn't exist
local Shared = TBRDS:FindFirstChild("Shared") or Instance.new("Folder")
Shared.Name = "Shared"
Shared.Parent = TBRDS

-- Modules
local RoleHandlers = require(script.Parent.TBRDS_Role_Handlers)
local Utils = require(ReplicatedStorage.TBRDS.Shared.Utils)

-- Constants
local GROUP_ID = { 34320208 } -- Array of group IDs to monitor for rank changes

-- Debug logging
local function debugLog(message)
  Utils.print("ServerManager", message)
end

-- PlayerTags storage
-- Format: {[Player] = {Role = "TagName", BillboardGUI = Instance?}}
-- Define structure with comments instead of TypeScript
-- local PlayerTagData = {
--    Role = "",  -- string
--    BillboardGUI = nil  -- Instance?
-- }

local PlayerTags = {} -- {[Player]: PlayerTagData}

-- Create rate limiter
local RateLimiter = Utils.createRateLimiter(60, 5) -- 5 requests per 60 seconds

-- Error Handling for loading role handlers
local success, err = pcall(function()
  RoleHandlers = require(script.Parent.TBRDS_Role_Handlers)
end)

if not success then
  warn("Failed to load RoleHandlers: " .. tostring(err))
  RoleHandlers = {
    User = {
      Check = function()
        return true
      end,
      Style = { Color = Color3.new(1, 1, 1), Font = Enum.Font.SourceSans },
    },
    GetPlayerRole = function()
      return "User"
    end,
  }
end

-- Function to create tag billboards directly from the server
local function CreateTagBillboard(player, tag, style)
  if not player.Character then
    return nil
  end

  local head = player.Character:FindFirstChild("Head")
  if not head then
    return nil
  end

  -- Remove existing billboard if present
  local existingBillboard = head:FindFirstChild("TagGui")
  if existingBillboard then
    existingBillboard:Destroy()
  end

  -- Create new billboard
  local billboard = Instance.new("BillboardGui")
  billboard.Name = "TagGui"
  billboard.Adornee = head
  billboard.Size = UDim2.new(8, 0, 2, 0)
  billboard.StudsOffset = Vector3.new(0, 3.5, 0)
  billboard.AlwaysOnTop = true
  billboard.MaxDistance = 10
  billboard.Active = true
  billboard.ZIndexBehavior = Enum.ZIndexBehavior.Global
  billboard.LightInfluence = 1
  billboard.Parent = head

  -- Create a frame to hold the tag elements
  local frame = Instance.new("Frame")
  frame.Name = "TagFrame"
  frame.Size = UDim2.new(1, 0, 1, 0)
  frame.BackgroundTransparency = 1
  frame.Parent = billboard

  -- Add UIListLayout to the frame for horizontal arrangement
  local layout = Instance.new("UIListLayout")
  layout.FillDirection = Enum.FillDirection.Horizontal
  layout.HorizontalAlignment = Enum.HorizontalAlignment.Center
  layout.VerticalAlignment = Enum.VerticalAlignment.Center
  layout.SortOrder = Enum.SortOrder.LayoutOrder
  layout.Padding = UDim.new(0, 10) -- 10-pixel gap between image and text
  layout.Parent = frame

  -- Add icon if specified
  if style.Image then
    debugLog("Assigning image to " .. player.Name .. ": " .. style.Image)
    local icon = Instance.new("ImageLabel")
    icon.Name = "Icon"
    icon.Size = UDim2.new(0, 30, 0, 30) -- Fixed size for the image
    icon.BackgroundTransparency = 1
    icon.Image = style.Image
    icon.LayoutOrder = 1
    icon.ImageTransparency = 0
    icon.ScaleType = Enum.ScaleType.Fit
    icon.AnchorPoint = Vector2.new(0.5, 0.5)
    icon.Position = UDim2.new(0.5, 0, 0.5, 0)
    icon.Parent = frame
  else
    debugLog("No image for role: " .. tag)
  end

  -- Create the tag label
  local label = Instance.new("TextLabel")
  label.Name = "TagLabel"
  label.Size = UDim2.new(0, 150, 0, 30) -- Fixed width, adjust as needed
  label.BackgroundTransparency = 1
  label.Text = style.GetText and style.GetText(player) or "[" .. tag .. "]"
  label.TextColor3 = style.Color
  label.Font = style.Font
  label.TextScaled = true
  label.TextStrokeTransparency = 0.5
  label.LayoutOrder = 2
  label.Parent = frame

  -- Apply gradient if specified
  if style.Gradient then
    local gradient = Instance.new("UIGradient")
    if #style.Gradient.Colors == 2 then
      local keypoints = {
        ColorSequenceKeypoint.new(0, style.Gradient.Colors[1]),
        ColorSequenceKeypoint.new(1, style.Gradient.Colors[2]),
      }
      gradient.Color = ColorSequence.new(keypoints)
    else
      local keypoints = {}
      for i, color in ipairs(style.Gradient.Colors) do
        local position = (i - 1) / (#style.Gradient.Colors - 1)
        table.insert(keypoints, ColorSequenceKeypoint.new(position, color))
      end
      gradient.Color = ColorSequence.new(keypoints)
    end
    gradient.Rotation = style.Gradient.Rotation
    gradient.Parent = label
  end

  -- Apply text effects if specified
  if style.TextStroke then
    label.TextStrokeColor3 = style.TextStroke.Color
    label.TextStrokeTransparency = style.TextStroke.Transparency
  end

  -- Apply font style if specified
  if style.FontStyle and Enum.Font[style.FontStyle] then
    label.Font = Enum.Font[style.FontStyle]
  end

  return billboard
end

-- Assign Tag function
local function AssignTag(player)
  -- Get player role
  local role = RoleHandlers.GetPlayerRole(player)

  -- Validate if the tag exists in TBRDS_Role_Handlers
  if not RoleHandlers[role] then
    debugLog("Invalid Tag/role: " .. role .. " for player: " .. player.Name .. ".")
    role = "User" -- Default user role if it's invalid
  end

  -- Debug Log
  debugLog("Assigning tag for player: " .. player.Name .. " with role: " .. role)

  -- Get style information for the role
  local style = RoleHandlers[role].Style

  -- Check if player needs a tag update
  local needsUpdate = false
  if not PlayerTags[player] then
    PlayerTags[player] = {
      Role = role,
      BillboardGUI = nil,
    }
    needsUpdate = true
  elseif PlayerTags[player].Role ~= role then
    -- Role changed, update the tag
    PlayerTags[player].Role = role
    needsUpdate = true
  end

  if needsUpdate then
    -- Create/update server-side billboard
    if player.Character and player.Character:FindFirstChild("Head") then
      PlayerTags[player].BillboardGUI = CreateTagBillboard(player, role, style)
    end

    -- Notify all clients about the tag update
    TagRemote:FireAllClients(player, role, style, style.Image)

    debugLog("Assigned Tag: " .. role .. " to player: " .. player.Name)
  end
end

-- Function to track group rank changes
local function trackGroupChanges(player)
  task.spawn(function()
    local lastKnownRanks = {}
    for _, groupId in ipairs(GROUP_ID) do
      lastKnownRanks[groupId] = player:GetRankInGroup(groupId)
    end

    while player.Parent == Players do
      task.wait(30) -- Check every 30 seconds
      local rankChanged = false

      for _, groupId in ipairs(GROUP_ID) do
        local currentRank = player:GetRankInGroup(groupId)
        if currentRank ~= lastKnownRanks[groupId] then
          lastKnownRanks[groupId] = currentRank
          rankChanged = true
          debugLog(
            string.format("%s's group %d rank changed to %d", player.Name, groupId, currentRank)
          )
        end
      end

      if rankChanged then
        AssignTag(player)
      end
    end
  end)
end

-- Player joined event
Players.PlayerAdded:Connect(function(player)
  debugLog("Player joined: " .. player.Name)

  -- Start tracking group changes
  trackGroupChanges(player)

  -- Assign initial tag
  AssignTag(player)

  -- Track property changes that might affect tags
  player:GetPropertyChangedSignal("UserId"):Connect(function()
    AssignTag(player)
  end)

  -- Handle team changes
  player:GetPropertyChangedSignal("Team"):Connect(function()
    AssignTag(player)
  end)

  -- Handle character spawning
  player.CharacterAdded:Connect(function(character)
    debugLog("Character added for: " .. player.Name)

    -- Get current role
    local currentRole = PlayerTags[player] and PlayerTags[player].Role or "User"
    local style = RoleHandlers[currentRole].Style

    -- Create new billboard
    PlayerTags[player].BillboardGUI = CreateTagBillboard(player, currentRole, style)

    -- Update all clients
    TagRemote:FireAllClients(player, currentRole, style, style.Image)
  end)
end)

-- Player leaving event
Players.PlayerRemoving:Connect(function(player)
  debugLog("Player left: " .. player.Name)

  -- Clean up player data
  PlayerTags[player] = nil
end)

-- Handle tag requests from clients
TagRequestRemote.OnServerEvent:Connect(function(player)
  if not Players:GetPlayerByUserId(player.UserId) then
    return
  end

  -- Check rate limit
  local allowed, count = RateLimiter:CheckLimit(player.UserId)
  if not allowed then
    debugLog(
      string.format(
        "Rate limit exceeded for %s (%d requests in %d seconds)",
        player.Name,
        count,
        RateLimiter.Window
      )
    )
    return
  end

  -- Send current tag information
  local role = PlayerTags[player] and PlayerTags[player].Role or "User"
  local style = RoleHandlers[role].Style

  -- Send tag information to the requesting client
  TagRemote:FireClient(player, player, role, style)
end)

-- Save special tag to data store
local function SaveSpecialTag(userId, tag)
  local success, err = pcall(function()
    SpecialTagsStore:SetAsync(tostring(userId), tag)
  end)

  if not success then
    warn("Failed to save tag for " .. tostring(userId) .. ": " .. tostring(err))
  end
end

-- Load special tag from data store
local function LoadSpecialTag(userId)
  local success, result = pcall(function()
    return SpecialTagsStore:GetAsync(tostring(userId))
  end)

  if success then
    return result
  else
    warn("Failed to load tag for " .. tostring(userId) .. ": " .. tostring(result))
    return nil
  end
end

-- Periodically validate all tags (anti-exploit)
task.spawn(function()
  while true do
    task.wait(60) -- Check every minute

    for player, tagData in pairs(PlayerTags) do
      if player.Parent == Players then
        -- Revalidate the player's role
        local currentRole = RoleHandlers.GetPlayerRole(player)
        if currentRole ~= tagData.Role then
          debugLog(
            "Revalidating tag for "
              .. player.Name
              .. " - Role changed from "
              .. tagData.Role
              .. " to "
              .. currentRole
          )
          AssignTag(player)
        end
      else
        -- Clean up players that have left
        PlayerTags[player] = nil
      end
    end
  end
end)

-- Debug command to check all player tags
if Utils.isDebugMode() then
  local function listAllTags()
    print("===== CURRENT PLAYER TAGS =====")
    for player, tagData in pairs(PlayerTags) do
      print(player.Name .. ": " .. tagData.Role)
    end
    print("===============================")
  end

  -- Debug command
  game:GetService("Players").PlayerAdded:Connect(function(player)
    player.Chatted:Connect(function(msg)
      -- Check if player has sufficient rank (252 or above) and proper command
      if player:GetRankInGroup(34320208) >= 252 then
        if msg:lower() == "/listtags" then
          -- Add debug print to verify command reception
          print("Command received from:", player.Name)
          listAllTags()
        end
      end
    end)
  end)

  -- Handle existing players
  for _, player in ipairs(game:GetService("Players"):GetPlayers()) do
    player.Chatted:Connect(function(msg)
      if player:GetRankInGroup(34320208) >= 252 then
        if msg:lower() == "/listtags" then
          -- Add debug print to verify command reception
          print("Command received from:", player.Name)
          listAllTags()
        end
      end
    end)
  end
end

return {
  AssignTag = AssignTag,
  SaveSpecialTag = SaveSpecialTag,
  LoadSpecialTag = LoadSpecialTag,
}
