--[[
    TBRDS_RoleHandlers.luau

    Role Handlers Module for Tag-Based Role Display System
    Defines role logic, check functions, and visual styles for all tags

    *Dynamic Innovative Studio*
]]

-- Role Handler structure:
-- {
--    Check = function(player) -> boolean, -- Returns true if player qualifies for this role
--    Style = {
--        Color = Color3,       -- Text color of the tag
--        Font = Enum.Font,     -- Font to use for the tag
--        Image = string?,      -- Optional image asset for the tag
--        Gradient = {          -- Optional gradient effect
--            Colors = {Color3},
--            Rotation = number
--        },
--        FontStyle = string?,  -- Optional font style
--        TextStroke = {        -- Optional text stroke effect
--            Color = Color3,
--            Transparency = number
--        },
--    }
-- }

-- Explicit Type Declaration (using comments as type annotations)
-- This helps with type checking and documentation
-- Note: This is using <PERSON><PERSON>'s strict mode typing system

--[[
    -- Define RoleHandler type
    type RoleHandler = {
        Check: (player: Player) -> boolean,
        Style: {
            Color: Color3,
            Font: Enum.Font,
            Image: string?,
            Gradient: {
                Colors: {Color3},
                Rotation: number
            }?,
            FontStyle: string?,
            TextStroke: {
                Color: Color3,
                Transparency: number
            }?,
        }
    }

    -- Define RoleHandlers module type
    type RoleHandlersModule = {
        GetPlayerRole: (player: Player) -> string,
        User: RoleHandler,
        Supporter: RoleHandler,
        Trusted: RoleHandler,
        Developer: RoleHandler,
        ["Junior Moderator"]: RoleHandler,
        Investors: RoleHandler,
        ["Game Moderator"]: RoleHandler,
        ["Senior Moderator"]: RoleHandler,
        Anonmancer: RoleHandler,
        BleckWolf25: RoleHandler,
    }
]]

-- Role handlers table
local RoleHandlers = {}

-- | -------------- | Default user role | -------------- | --
RoleHandlers.User = {
  Check = function(player)
    return true -- Always return true as this is the default role
  end,
  Style = {
    Color = Color3.fromRGB(255, 255, 255), -- White
    Font = Enum.Font.SourceSans,
    GetText = function(player)
      local teamName = player.Team and player.Team.Name or "No Team"
      return string.format("[%s] - %s", teamName, player.Name)
    end,
  },
}

-- | -------------- | Supporter role (for those who bought donation gamepass | -------------- | --
RoleHandlers.Supporter = {
  Check = function(player)
    --Check for gamepass ownership
    local MarketplaceService = game:GetService("MarketplaceService")
    local hasPass = false

    -- Use pcall to safely check gamepass ownership
    local success, result = pcall(function()
      return MarketplaceService:UserOwnsGamePassAsync(player.UserId, 99445069658101) -- Gamepass ID
    end)

    if success then
      hasPass = result
    end

    return hasPass, player:GetRankInGroup(34320208) == 2
  end,
  Style = {
    Color = Color3.fromRGB(128, 0, 128), -- Purple
    Font = Enum.Font.SourceSansBold,
    Image = "http://www.roblox.com/asset/?id=75475742951834",
  },
}

-- | -------------- | Trusted user role | -------------- | --
RoleHandlers.Trusted = {
  Check = function(player)
    -- Check if player is in a specific group with appropriate rank
    return player:GetRankInGroup(34320208) == 248
  end,
  Style = {
    Color = Color3.fromRGB(14, 149, 1), -- Light Blue
    Font = Enum.Font.SourceSansBold,
    Image = "http://www.roblox.com/asset/?id=124860305809176",
    TextStroke = {
      Color = Color3.fromRGB(100, 100, 200),
      Transparency = 0.7,
    },
  },
}

-- | -------------- | Developer role | -------------- | --
RoleHandlers.Developer = {
  Check = function(player)
    -- Check if player is in a specific group with appropriate rank
    return player:GetRankInGroup(34320208) == 249
  end,
  Style = {
    Color = Color3.fromRGB(255, 17, 1), -- Red
    Font = Enum.Font.SourceSansBold,
    Image = "http://www.roblox.com/asset/?id=121618039480779",
  },
}

-- | -------------- | Junior Moderator role | -------------- | --
RoleHandlers["Junior Moderator"] = {
  Check = function(player)
    return player:GetRankInGroup(34320208) == 250 and player:GetRankInGroup(34320208) < 251
  end,
  Style = {
    Color = Color3.fromRGB(99, 225, 181), -- Cyan-green
    Font = Enum.Font.SourceSansBold,
    Image = "http://www.roblox.com/asset/?id=127518605052393",
    FontStyle = "Bold",
  },
}

-- | -------------- | Investors role | -------------- | --
RoleHandlers.Investors = {
  Check = function(player)
    -- Check if player is in a specific group with appropriate rank
    return player:GetRankInGroup(34320208) == 251
  end,
  Style = {
    Color = Color3.fromRGB(255, 215, 0), -- Gold
    Font = Enum.Font.SourceSansBold,
    Image = "http://www.roblox.com/asset/?id=118942866906979",
    Gradient = {
      Colors = { Color3.fromRGB(255, 215, 0), Color3.fromRGB(255, 165, 0) },
      Rotation = 90,
    },
  },
}

-- | -------------- | Game Moderator role | -------------- | --
RoleHandlers["Game Moderator"] = {
  Check = function(player)
    -- Check if player is in a specific group with appropriate rank
    return player:GetRankInGroup(34320208) == 252
  end,
  Style = {
    Color = Color3.fromRGB(0, 0, 255), -- Blue
    Font = Enum.Font.SourceSansBold,
    Image = "http://www.roblox.com/asset/?id=82778826477473",
    TextStroke = {
      Color = Color3.fromRGB(0, 0, 100),
      Transparency = 0.6,
    },
  },
}

-- | -------------- | Senior Moderator role | -------------- | --
RoleHandlers["Senior Moderator"] = {
  Check = function(player)
    -- Check if player is in a specific group with appropriate rank
    return player:GetRankInGroup(34320208) == 253
  end,
  Style = {
    Color = Color3.fromRGB(0, 0, 139), -- Dark Blue
    Font = Enum.Font.SourceSansBold,
    Image = "http://www.roblox.com/asset/?id=115347218194547",
    Gradient = {
      Colors = { Color3.fromRGB(0, 0, 139), Color3.fromRGB(25, 25, 180) },
      Rotation = 135,
    },
    TextStroke = {
      Color = Color3.fromRGB(0, 0, 70),
      Transparency = 0.5,
    },
  },
}

-- | -------------- | Co-Founder role | -------------- | --
RoleHandlers.Anonmancer = {
  Check = function(player)
    -- Check if player is in a specific group with appropriate rank
    return player:GetRankInGroup(34320208) == 254
  end,
  Style = {
    Color = Color3.fromRGB(3, 164, 3), -- Emeral Green that beautiful damion likes
    Font = Enum.Font.Kalam,
    Image = "http://www.roblox.com/asset/?id=130206582408033",
  },
  TextStroke = {
    Color = Color3.fromRGB(0, 100, 100),
    Transparency = 0.3,
  },
}

-- | -------------- | Founder role | -------------- | --
RoleHandlers.BleckWolf25 = {
  Check = function(player)
    -- Check if player is in a specific group with appropriate rank
    return player:GetRankInGroup(34320208) == 255
  end,
  Style = {
    Color = Color3.fromRGB(0, 255, 255), -- Cyan
    Font = Enum.Font.Kalam,
    Image = "http://www.roblox.com/asset/?id=84748237738215",
    Gradient = {
      Colors = { Color3.fromRGB(0, 255, 255), Color3.fromRGB(255, 0, 255) },
      Rotation = 180,
    },
    TextStroke = {
      Color = Color3.fromRGB(0, 100, 100),
      Transparency = 0.3,
    },
  },
}

-- Function to get a player's highest role based on priority
-- Higher priority roles should be checked first
function RoleHandlers.GetPlayerRole(player)
  -- Check roles in priority order (highest to lowest)
  local rolePriority = {
    "BleckWolf25", -- Founder (highest priority)
    "Anonmancer", -- Co-Founder
    "Senior Moderator",
    "Game Moderator",
    "Junior Moderator",
    "Developer",
    "Investors",
    "Trusted",
    "Supporter",
    "User", -- Default role (lowest priority)
  }

  -- Check each role in order of priority
  for _, roleName in ipairs(rolePriority) do
    local roleHandler = RoleHandlers[roleName]
    if roleHandler and roleHandler.Check(player) then
      return roleName
    end
  end

  -- Default to User if no other role matches
  return "User"
end

return RoleHandlers
