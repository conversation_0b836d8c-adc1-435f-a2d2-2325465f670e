--[[
    Console Module
    Provides command input functionality for MCS

    Modern terminal-inspired UI with consistent styling
]]

-- Services
local ContextActionService = game:GetService("ContextActionService")
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")

-- Config
local Configuration = ReplicatedStorage.Configurations.Systems.MCS

-- Modules
local AutocompleteService = require(script.Parent.Parent.Client.MCS_AutocompleteService)
local Constants = require(ReplicatedStorage.MCS.Shared.Constants)
local UI_THEME = require(ReplicatedStorage.MCS.Shared.UI_Constants)
local Utils = require(ReplicatedStorage.MCS.Shared.Utils)

-- Check if Debug Mode is enabled
local function isDebugMode()
  return Configuration:GetAttribute("DebugMode") == true
end

-- Debug print function
local function debugPrint(message)
  if isDebugMode() then
    print("[MCS_DEBUG_MESSAGE]: " .. message)
  end
end

-- Aliases
local MCS_Shared = ReplicatedStorage.MCS

-- Modules
local AutocompleteService = require(script.Parent.Parent.Client.MCS_AutocompleteService)
local CommandRemote = MCS_Shared:WaitForChild("Remotes"):WaitForChild("CommandRemote")
local Constants = require(MCS_Shared.Shared.Constants)

-- Player references
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Console data store
local Console = {}

-- Command History data store
local commandHistory = {}

-- Variables
local historyIndex = 0
local currentInput = ""

-- Configuration
local CONFIG = {
  MAX_SUGGESTIONS = Constants.MAX_SUGGESTIONS,
}

-- Animation constants
local ANIMATION_TIME = 0.3

-- UI references (Pick from PlayerGui->MCS_Console and then from there it's the references necessary for the coding to work)
local uiConsole = playerGui:WaitForChild("MCS_Console")
local uiMainFrame = uiConsole.MainContainer
local uiHeaderBar = uiMainFrame.HeaderBar
local uiCloseButton = uiHeaderBar.CloseButton
local uiTextBox = uiMainFrame.CommandInput
local uiConsoleFrame = uiMainFrame.ConsoleFrame
local uiSuggestions = uiMainFrame.Suggestions

-- Console Initialization
function Console.init()
  -- Debug message
  debugPrint("Console Initializing...")

  uiCloseButton.MouseButton1Click:Connect(function()
    Console.hide()
    debugPrint("Console close button (M1) clicked, closing console")
  end)

  -- Bind "F2" to toggle console using ContextActionService
  ContextActionService:BindActionAtPriority(
    "ToggleConsole",
    function(actionName, inputState, inputObject)
      debugPrint("F2 Console triggered")
      if inputState == Enum.UserInputState.Begin then
        Console.toggle()
        debugPrint("Console toggled")
        return Enum.ContextActionResult.Sink -- Prevents chat from receiving the input
      end
      return Enum.ContextActionResult.Pass
    end,
    false,
    3000,
    Enum.KeyCode.F2
  ) -- Priority 3000 to override everything

  -- Submit command on Enter
  uiTextBox.FocusLost:Connect(function(enterPressed)
    if enterPressed then
      local commandText = uiTextBox.Text
      if commandText ~= "" then
        -- Add to command history
        table.insert(commandHistory, 1, commandText)
        debugPrint("Command added to history")
        if #commandHistory > 20 then
          debugPrint("Command history is full, removing a command history")
          table.remove(commandHistory)
        end
        historyIndex = 0

        -- Send command to server
        CommandRemote:FireServer(commandText)
      end
      uiTextBox.Text = ""
      Console.hide()
    end
  end)

  -- Update suggestions as player types
  uiTextBox:GetPropertyChangedSignal("Text"):Connect(function()
    Console.updateSuggestions(uiTextBox.Text)
    debugPrint("Console suggestions had been updated.")
  end)

  -- Command history navigation
  UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if not uiConsoleFrame.Visible or gameProcessed then
      return
    end

    if input.KeyCode == Enum.KeyCode.Up then
      debugPrint("Nagivate to History 1")
      Console.navigateHistory(1)
      debugPrint("Nagivated - to History 1")
    elseif input.KeyCode == Enum.KeyCode.Down then
      debugPrint("Nagivate to History -1")
      Console.navigateHistory(-1)
      debugPrint("Nagivated - to History -1")
    end
  end)
end

-- Show Console
function Console.show()
  local mainContainer = uiConsole:FindFirstChild("MainContainer")
  if not mainContainer then
    return
  end

  -- Reset for new session
  uiTextBox.Text = ""
  uiSuggestions.Visible = false

  -- Prepare animation
  mainContainer.Position = UDim2.new(0.5, -250, 0.2, -350)
  mainContainer.Visible = true

  -- Animate in
  local tween = TweenService:Create(
    mainContainer,
    TweenInfo.new(ANIMATION_TIME, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
    { Position = UDim2.new(0.5, -250, 0.3, -150) }
  )
  tween:Play()

  -- Focus the uiTextBox
  uiTextBox:CaptureFocus()

  -- Bind Escape to hide console
  ContextActionService:BindAction("HideConsole", function(actionName, inputState, inputObject)
    if inputState == Enum.UserInputState.Begin then
      Console.hide()
      return Enum.ContextActionResult.Sink
    end
    return Enum.ContextActionResult.Pass
  end, false, Enum.KeyCode.Escape)
end

-- Hide Console
function Console.hide()
  local mainContainer = uiConsole:FindFirstChild("MainContainer")
  if not mainContainer or not mainContainer.Visible then
    return
  end

  -- Animate out
  local tween = TweenService:Create(
    mainContainer,
    TweenInfo.new(ANIMATION_TIME * 0.7, Enum.EasingStyle.Quad, Enum.EasingDirection.In),
    { Position = UDim2.new(0.5, -250, 0.2, -350) }
  )

  tween.Completed:Connect(function()
    mainContainer.Visible = false
  end)

  tween:Play()

  -- Unbind Escape action
  ContextActionService:UnbindAction("HideConsole")
end

-- Toggle Console (show/hide)
function Console.toggle()
  local mainContainer = uiConsole:FindFirstChild("MainContainer")
  if mainContainer and mainContainer.Visible then
    Console.hide()
    debugPrint("Console hidden")
  else
    Console.show()
    debugPrint("Console shown")
  end
end

-- Update suggestions
function Console.updateSuggestions(partialText)
  -- Get suggestions from the AutocompleteService
  local suggestions = AutocompleteService.getSuggestions(partialText)

  debugPrint("Get suggestions")
  -- IMPORTANT: Before using suggestions, make sure it's not nil
  if not suggestions then
    suggestions = {} -- A check to make sure we have an empty table if nil is returned
  end

  -- Clear existing suggestions
  for _, child in ipairs(uiSuggestions:GetChildren()) do
    if child:IsA("TextButton") then
      child:Destroy()
    end
  end

  -- Update suggestions list
  local totalHeight = 0
  for i, suggestion in ipairs(suggestions) do
    local button = Instance.new("TextButton")
    button.Name = "Suggestion_" .. i
    button.Size = UDim2.new(1, 0, 0, 28)
    button.BackgroundTransparency = 0.9
    button.BackgroundColor3 = UI_THEME.BACKGROUND
    button.TextSize = 12
    button.TextColor3 = UI_THEME.TEXT
    button.Text = suggestion
    button.TextXAlignment = Enum.TextXAlignment.Left
    button.BorderSizePixel = 0
    button.LayoutOrder = i

    local buttonCorner = Instance.new("UICorner")
    buttonCorner.CornerRadius = UDim.new(0, 3)
    buttonCorner.Parent = button

    button.MouseEnter:Connect(function()
      button.BackgroundTransparency = 0.7
      button.TextColor3 = UI_THEME.SUCCESS_TEXT
    end)

    button.MouseLeave:Connect(function()
      button.BackgroundTransparency = 0.9
      button.TextColor3 = UI_THEME.TEXT
    end)

    button.MouseButton1Click:Connect(function()
      -- Get current text and cursor position
      local currentText = uiTextBox.Text
      local cursorPos = uiTextBox.CursorPosition

      -- Split into command and partial argument
      local commandParts = string.split(currentText, " ")
      table.remove(commandParts, #commandParts) -- Remove partial argument

      -- Build new command with suggestion
      local newCommand = table.concat(commandParts, " ") .. " " .. suggestion
      uiTextBox.Text = newCommand

      -- Move cursor to end
      uiTextBox.CursorPosition = #newCommand + 1
      uiSuggestions.Visible = false
    end)

    button.Parent = uiSuggestions
    totalHeight = totalHeight + 30 -- button height + padding

    -- Limit number of visible suggestions
    if i >= CONFIG.MAX_SUGGESTIONS then
      break
    end
    debugPrint("Suggestions updated")
  end

  -- Update suggestion frame size and visibility
  if #suggestions > 0 then
    local targetHeight = math.min(150, totalHeight) -- Cap at 150px max

    -- Animate suggestion panel expansion/contraction
    uiSuggestions.Visible = true
    TweenService
      :Create(uiSuggestions, TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
        Size = UDim2.new(1, -20, 0, targetHeight),
        Position = UDim2.new(0, 10, 1, -60 - targetHeight),
      })
      :Play()

    uiSuggestions.CanvasSize = UDim2.new(0, 0, 0, totalHeight)
  else
    uiSuggestions.Visible = false
  end
end

-- History Navigation
function Console.navigateHistory(direction)
  -- Save current input if we're starting navigation
  if historyIndex == 0 and direction > 0 then
    currentInput = uiTextBox.Text
  end

  -- Calculate new index
  local newIndex = historyIndex + direction

  -- Bounds check
  if newIndex < 0 then
    newIndex = 0
  elseif newIndex > #commandHistory then
    newIndex = #commandHistory
  end

  -- Update text based on navigation
  if newIndex == 0 then
    uiTextBox.Text = currentInput
  elseif commandHistory[newIndex] then
    uiTextBox.Text = commandHistory[newIndex]
  end

  -- Update cursor position to end
  uiTextBox.CursorPosition = #uiTextBox.Text + 1

  -- Update history index
  historyIndex = newIndex
end

return Console
