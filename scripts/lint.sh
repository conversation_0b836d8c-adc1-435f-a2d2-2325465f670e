#!/bin/bash
# 🔍 Local Linting Script for Site-112 Project

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if tools are installed
check_tools() {
    print_status "Checking required tools..."
    
    if ! command -v selene &> /dev/null; then
        print_error "Selene not found. Please install it with: aftman install"
        exit 1
    fi
    
    if ! command -v stylua &> /dev/null; then
        print_error "StyLua not found. Please install it with: aftman install"
        exit 1
    fi
    
    print_success "All required tools are available"
}

# Run Selene linter
run_selene() {
    print_status "Running Selene linter..."
    
    if selene src/; then
        print_success "Selene linting passed!"
        return 0
    else
        print_error "Selene linting failed!"
        return 1
    fi
}

# Check StyLua formatting
check_stylua() {
    print_status "Checking StyLua formatting..."
    
    if stylua --check src/; then
        print_success "Code is properly formatted!"
        return 0
    else
        print_error "Code formatting issues found!"
        print_warning "Run 'stylua src/' to fix formatting issues"
        return 1
    fi
}

# Auto-fix formatting
fix_formatting() {
    print_status "Auto-fixing code formatting..."
    
    if stylua src/; then
        print_success "Code formatting fixed!"
        return 0
    else
        print_error "Failed to fix formatting!"
        return 1
    fi
}

# Generate lint report
generate_report() {
    print_status "Generating lint report..."
    
    local report_file="lint-report.txt"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    {
        echo "# Site-112 Lint Report"
        echo "Generated: $timestamp"
        echo ""
        echo "## Selene Results"
        if selene src/ 2>&1; then
            echo "✅ No issues found"
        else
            echo "❌ Issues found (see above)"
        fi
        echo ""
        echo "## StyLua Results"
        if stylua --check src/ 2>&1; then
            echo "✅ Code is properly formatted"
        else
            echo "❌ Formatting issues found"
        fi
        echo ""
        echo "## File Statistics"
        echo "- Luau files: $(find src/ -name "*.luau" | wc -l)"
        echo "- Total lines: $(find src/ -name "*.luau" -exec wc -l {} + | tail -1 | awk '{print $1}')"
        echo "- TODO/FIXME comments: $(grep -r "TODO\|FIXME\|HACK" src/ --include="*.luau" | wc -l)"
    } > "$report_file"
    
    print_success "Report generated: $report_file"
}

# Main function
main() {
    echo "🔍 Site-112 Linting Script"
    echo "=========================="
    
    local fix_mode=false
    local report_mode=false
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --fix)
                fix_mode=true
                shift
                ;;
            --report)
                report_mode=true
                shift
                ;;
            --help|-h)
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --fix     Auto-fix formatting issues"
                echo "  --report  Generate detailed lint report"
                echo "  --help    Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                echo "Use --help for usage information"
                exit 1
                ;;
        esac
    done
    
    # Check tools
    check_tools
    
    local exit_code=0
    
    # Run linting
    if ! run_selene; then
        exit_code=1
    fi
    
    # Handle formatting
    if [ "$fix_mode" = true ]; then
        if ! fix_formatting; then
            exit_code=1
        fi
    else
        if ! check_stylua; then
            exit_code=1
        fi
    fi
    
    # Generate report if requested
    if [ "$report_mode" = true ]; then
        generate_report
    fi
    
    # Final status
    if [ $exit_code -eq 0 ]; then
        print_success "All checks passed! 🎉"
    else
        print_error "Some checks failed! Please fix the issues above."
    fi
    
    exit $exit_code
}

# Run main function with all arguments
main "$@"
