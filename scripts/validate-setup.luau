--!strict
-- Setup Validation Script for Site-112 VSCode Environment
-- This script helps validate that your development environment is properly configured

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

-- Type annotations test
local player: Player = Players.LocalPlayer
local heartbeat: RBXScriptConnection

-- Function with proper typing
local function validateEnvironment(): boolean
	print("🔍 Validating Site-112 Development Environment...")
	
	-- Test 1: Basic Luau syntax
	local testTable = {
		name = "Site-112",
		version = "1.0.0",
		modules = {"MAFS", "TBRDS", "CGS", "MCS"}
	}
	
	-- Test 2: Type checking
	local function processPlayer(targetPlayer: Player): string
		return `Player: {targetPlayer.Name} (ID: {targetPlayer.UserId})`
	end
	
	-- Test 3: Service usage
	if player then
		print("✅ Player service working:", processPlayer(player))
	else
		warn("❌ Player service not available")
		return false
	end
	
	-- Test 4: Modern Luau features
	local success = pcall(function()
		-- String interpolation
		local message = `Environment validation for {testTable.name} v{testTable.version}`
		print("✅ String interpolation working:", message)
		
		-- Table operations
		for i, module in testTable.modules do
			print(`  Module {i}: {module}`)
		end
	end)
	
	if not success then
		warn("❌ Modern Luau features not working")
		return false
	end
	
	print("🎉 Environment validation complete!")
	print("📝 If you see this message with proper syntax highlighting and no errors:")
	print("   • Luau LSP is working correctly")
	print("   • Type checking is enabled") 
	print("   • Roblox API integration is active")
	print("   • Your development environment is ready!")
	
	return true
end

-- Test connection handling
heartbeat = RunService.Heartbeat:Connect(function()
	-- This should show proper autocomplete for RunService
	-- and type information for the connection
end)

-- Cleanup function
local function cleanup(): ()
	if heartbeat then
		heartbeat:Disconnect()
		print("🧹 Cleanup completed")
	end
end

-- Main execution
local isValid = validateEnvironment()

if isValid then
	print("✅ Setup validation passed!")
	print("💡 Tips:")
	print("   • Use Ctrl+Space for autocomplete")
	print("   • Hover over variables to see types")
	print("   • F12 to go to definitions")
	print("   • Shift+F12 to find references")
else
	warn("❌ Setup validation failed!")
	warn("🔧 Check your VSCode configuration and extensions")
end

-- Cleanup after 5 seconds
task.wait(5)
cleanup()

-- TODO: Add more validation tests
-- FIXME: Consider adding network validation
-- NOTE: This script should work in both Studio and synced files
