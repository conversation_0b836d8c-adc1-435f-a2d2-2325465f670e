#!/bin/bash
# 🚀 Development Environment Setup Script for Site-112

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on supported OS
check_os() {
    print_status "Checking operating system..."

    case "$(uname -s)" in
        Darwin*)
            OS="macOS"
            ;;
        Linux*)
            OS="Linux"
            ;;
        MINGW*|MSYS*|CYGWIN*)
            OS="Windows"
            ;;
        *)
            print_error "Unsupported operating system: $(uname -s)"
            exit 1
            ;;
    esac

    print_success "Detected OS: $OS"
}

# Install Aftman if not present
install_aftman() {
    print_status "Checking for Aftman..."

    if command -v aftman &> /dev/null; then
        print_success "Aftman is already installed"
        return 0
    fi

    print_status "Installing Aftman..."

    case "$OS" in
        macOS|Linux)
            curl -L https://github.com/LPGhatguy/aftman/releases/latest/download/aftman-$OS.zip -o aftman.zip
            unzip aftman.zip
            chmod +x aftman

            # Try to install to a directory in PATH
            if [ -d "$HOME/.local/bin" ]; then
                mv aftman "$HOME/.local/bin/"
            elif [ -d "$HOME/bin" ]; then
                mv aftman "$HOME/bin/"
            else
                mkdir -p "$HOME/.local/bin"
                mv aftman "$HOME/.local/bin/"
                print_warning "Added aftman to ~/.local/bin - make sure this is in your PATH"
            fi

            rm aftman.zip
            ;;
        Windows)
            print_error "Please install Aftman manually from: https://github.com/LPGhatguy/aftman/releases"
            print_error "Then run this script again"
            exit 1
            ;;
    esac

    print_success "Aftman installed successfully"
}

# Install development tools
install_tools() {
    print_status "Installing development tools with Aftman..."

    if [ ! -f "aftman.toml" ]; then
        print_error "aftman.toml not found! Are you in the project root?"
        exit 1
    fi

    aftman install
    print_success "Development tools installed"
}

# Setup Git hooks
setup_git_hooks() {
    print_status "Setting up Git hooks..."

    if [ ! -d ".git" ]; then
        print_warning "Not a Git repository - skipping Git hooks setup"
        return 0
    fi

    # Create hooks directory if it doesn't exist
    mkdir -p .git/hooks

    # Install pre-commit hook
    cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# Site-112 Pre-commit Hook
exec ./scripts/pre-commit.sh
EOF

    chmod +x .git/hooks/pre-commit

    # Install commit-msg hook for conventional commits
    cat > .git/hooks/commit-msg << 'EOF'
#!/bin/bash
# Site-112 Commit Message Hook
exec ./scripts/pre-commit.sh "$1"
EOF

    chmod +x .git/hooks/commit-msg

    print_success "Git hooks installed"
}

# Validate project structure
validate_project() {
    print_status "Validating project structure..."

    local required_files=(
        "selene.toml"
        "stylua.toml"
        ".luaurc"
        "src"
    )

    local missing_files=()

    for file in "${required_files[@]}"; do
        if [ ! -e "$file" ]; then
            missing_files+=("$file")
        fi
    done

    if [ ${#missing_files[@]} -gt 0 ]; then
        print_error "Missing required files/directories:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        exit 1
    fi

    print_success "Project structure is valid"
}

# Test tools installation
test_tools() {
    print_status "Testing tool installations..."

    local tools=("selene" "stylua" "luau-lsp")
    local failed_tools=()

    for tool in "${tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            local version=$($tool --version 2>&1 | head -1 || echo "unknown")
            print_success "$tool: $version"
        else
            failed_tools+=("$tool")
        fi
    done

    if [ ${#failed_tools[@]} -gt 0 ]; then
        print_error "Failed to install tools: ${failed_tools[*]}"
        exit 1
    fi

    print_success "All tools are working correctly"
}

# Run initial linting
run_initial_lint() {
    print_status "Running initial code quality check..."

    if [ -x "./scripts/lint.sh" ]; then
        if ./scripts/lint.sh; then
            print_success "Initial lint check passed"
        else
            print_warning "Initial lint check found issues - you may want to fix them"
        fi
    else
        print_warning "Lint script not found or not executable"
    fi
}

# Create helpful aliases
create_aliases() {
    print_status "Creating helpful development aliases..."

    local alias_file="$HOME/.site112_aliases"

    cat > "$alias_file" << 'EOF'
# Site-112 Development Aliases
alias s112-lint='./scripts/lint.sh'
alias s112-lint-fix='./scripts/lint.sh --fix'
alias s112-format='stylua src/'
alias s112-check='selene src/ && stylua --check src/'
# Note: Using Roblox Studio Script Sync instead of Rojo
alias s112-studio='echo "Open Roblox Studio and enable Script Sync in File > Beta Features"'
EOF

    print_success "Aliases created in $alias_file"
    print_status "Add 'source $alias_file' to your shell profile to use them"
}

# Display final instructions
show_final_instructions() {
    print_header "🎉 Setup Complete!"

    echo ""
    echo "Your Site-112 development environment is ready!"
    echo ""
    echo "📋 Quick Commands:"
    echo "  ./scripts/lint.sh          - Run linting"
    echo "  ./scripts/lint.sh --fix    - Fix formatting issues"
    echo "  stylua src/                - Format code"
    echo "  selene src/                - Run linter"
    echo ""
    echo "🔧 VSCode Setup:"
    echo "  1. Open the workspace: site-112-workspace.code-workspace"
    echo "  2. Install recommended extensions when prompted"
    echo "  3. Configure Luau Language Server"
    echo ""
    echo "🚀 Next Steps:"
    echo "  1. Open Roblox Studio"
    echo "  2. Enable Script Sync in File > Beta Features"
    echo "  3. Open your place and start coding!"
    echo ""
    echo "📚 Documentation:"
    echo "  - VSCODE_SETUP_GUIDE.md - Complete VSCode setup"
    echo "  - lua-luau.md - Lua-luau coding standards"
    echo "  - overview.md - Project overview"
    echo ""
    print_success "Happy coding! 🎮"
}

# Main setup function
main() {
    print_header "🚀 Site-112 Development Setup"

    echo "This script will set up your development environment for Site-112."
    echo "It will install tools, configure Git hooks, and validate the project."
    echo ""
    echo "Continue? (Y/n)"
    read -r response
    if [[ "$response" =~ ^[Nn]$ ]]; then
        echo "Setup cancelled."
        exit 0
    fi

    # Run setup steps
    check_os
    install_aftman
    validate_project
    install_tools
    test_tools
    setup_git_hooks
    run_initial_lint
    create_aliases
    show_final_instructions
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "Site-112 Development Environment Setup"
        echo ""
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Options:"
        echo "  --help    Show this help message"
        echo "  --tools   Only install tools (skip Git hooks and validation)"
        echo "  --hooks   Only setup Git hooks"
        echo ""
        echo "This script will:"
        echo "  - Install Aftman (if needed)"
        echo "  - Install development tools (Selene, StyLua, Luau LSP)"
        echo "  - Setup Git hooks for code quality"
        echo "  - Validate project structure"
        echo "  - Run initial code quality checks"
        exit 0
        ;;
    --tools)
        check_os
        install_aftman
        install_tools
        test_tools
        print_success "Tools installation complete!"
        ;;
    --hooks)
        setup_git_hooks
        print_success "Git hooks setup complete!"
        ;;
    *)
        main
        ;;
esac
