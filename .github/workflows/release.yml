name: 🚀 Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version (e.g., v1.0.0)'
        required: true
        type: string

jobs:
  build-and-release:
    name: 🚀 Build and Release
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🛠️ Setup Aftman
        uses: ok-nick/setup-aftman@v0.4.2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: 📦 Install Tools
        run: aftman install

      - name: 🔍 Run Pre-Release Checks
        run: |
          echo "🔍 Running pre-release checks..."
          
          # Run linting
          echo "🔍 Running Selene..."
          selene src/
          
          # Check formatting
          echo "🎨 Checking StyLua formatting..."
          stylua --check src/
          
          echo "✅ Pre-release checks passed!"

      - name: 🏗️ Build Project
        run: |
          echo "🏗️ Building project..."
          
          # Build the main project
          rojo build --output "Site-112-Game.rbxl"
          
          # Build individual places if needed
          if [ -f "places/lobby.project.json" ]; then
            rojo build places/lobby.project.json --output "Site-112-Lobby.rbxl"
          fi
          
          echo "✅ Build completed successfully!"

      - name: 📋 Generate Release Notes
        id: release_notes
        run: |
          echo "📋 Generating release notes..."
          
          # Get version from tag or input
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            VERSION="${{ github.event.inputs.version }}"
          else
            VERSION="${GITHUB_REF#refs/tags/}"
          fi
          
          # Generate changelog since last tag
          LAST_TAG=$(git describe --tags --abbrev=0 HEAD^ 2>/dev/null || echo "")
          
          if [ -n "$LAST_TAG" ]; then
            CHANGELOG=$(git log --pretty=format:"- %s" $LAST_TAG..HEAD)
          else
            CHANGELOG=$(git log --pretty=format:"- %s" --max-count=10)
          fi
          
          # Create release notes
          cat > release_notes.md << EOF
          # Site-112 Release $VERSION
          
          ## 🎮 What's New
          
          $CHANGELOG
          
          ## 📦 Assets
          
          - **Site-112-Game.rbxl** - Main game file
          - **Source Code** - Complete source code archive
          
          ## 🔧 Technical Details
          
          - **Luau Files**: $(find src/ -name "*.luau" | wc -l)
          - **Total Lines**: $(find src/ -name "*.luau" -exec wc -l {} + | tail -1 | awk '{print $1}')
          - **Build Date**: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
          - **Commit**: ${{ github.sha }}
          
          ## 🚀 Installation
          
          1. Download the \`Site-112-Game.rbxl\` file
          2. Open in Roblox Studio
          3. Publish to your game or test locally
          
          ## 📝 Notes
          
          - This release requires Roblox Studio
          - Compatible with current Roblox platform
          - See documentation for setup instructions
          EOF
          
          echo "VERSION=$VERSION" >> $GITHUB_OUTPUT

      - name: 📤 Create Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ steps.release_notes.outputs.VERSION }}
          name: Site-112 ${{ steps.release_notes.outputs.VERSION }}
          body_path: release_notes.md
          files: |
            Site-112-Game.rbxl
            Site-112-Lobby.rbxl
          draft: false
          prerelease: ${{ contains(steps.release_notes.outputs.VERSION, 'alpha') || contains(steps.release_notes.outputs.VERSION, 'beta') }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: 📊 Release Summary
        run: |
          echo "## 🚀 Release Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "✅ **Version**: ${{ steps.release_notes.outputs.VERSION }}" >> $GITHUB_STEP_SUMMARY
          echo "📦 **Assets**: Site-112-Game.rbxl" >> $GITHUB_STEP_SUMMARY
          echo "🔗 **Release URL**: ${{ github.server_url }}/${{ github.repository }}/releases/tag/${{ steps.release_notes.outputs.VERSION }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📋 Build Details" >> $GITHUB_STEP_SUMMARY
          echo "- Build completed successfully ✅" >> $GITHUB_STEP_SUMMARY
          echo "- All quality checks passed ✅" >> $GITHUB_STEP_SUMMARY
          echo "- Release published ✅" >> $GITHUB_STEP_SUMMARY

  notify-team:
    name: 📢 Notify Team
    runs-on: ubuntu-latest
    needs: build-and-release
    if: success()
    
    steps:
      - name: 📢 Post to Summary
        run: |
          echo "## 🎉 Release Published Successfully!" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "The new release has been published and is ready for deployment." >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Next Steps:**" >> $GITHUB_STEP_SUMMARY
          echo "1. Download the release assets" >> $GITHUB_STEP_SUMMARY
          echo "2. Test in Roblox Studio" >> $GITHUB_STEP_SUMMARY
          echo "3. Deploy to production when ready" >> $GITHUB_STEP_SUMMARY
