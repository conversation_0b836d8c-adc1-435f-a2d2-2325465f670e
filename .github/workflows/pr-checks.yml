name: 🔍 PR Quality Checks

on:
  pull_request:
    types: [opened, synchronize, reopened]
    branches: [ main, dev ]

jobs:
  pr-validation:
    name: 🔍 PR Validation
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🛠️ Setup Aftman
        uses: ok-nick/setup-aftman@v0.4.2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: 📦 Install Tools
        run: aftman install

      - name: 📋 Validate PR Title
        run: |
          echo "📋 Validating PR title..."
          
          PR_TITLE="${{ github.event.pull_request.title }}"
          
          # Check if title follows conventional commit format
          if [[ "$PR_TITLE" =~ ^(feat|fix|docs|style|refactor|test|chore|perf|ci|build)(\(.+\))?: .+ ]]; then
            echo "✅ PR title follows conventional commit format"
          else
            echo "❌ PR title should follow conventional commit format"
            echo "Examples:"
            echo "  feat: add new MAFS sound system"
            echo "  fix: resolve client-server sync issue"
            echo "  docs: update API documentation"
            exit 1
          fi

      - name: 📊 Analyze Changes
        run: |
          echo "📊 Analyzing PR changes..."
          
          # Get changed files
          git diff --name-only origin/${{ github.event.pull_request.base.ref }}...HEAD > changed_files.txt
          
          # Count different file types
          luau_files=$(grep "\.luau$" changed_files.txt | wc -l)
          lua_files=$(grep "\.lua$" changed_files.txt | wc -l)
          json_files=$(grep "\.json$" changed_files.txt | wc -l)
          md_files=$(grep "\.md$" changed_files.txt | wc -l)
          
          echo "📊 Change Summary:"
          echo "- Luau files: $luau_files"
          echo "- Lua files: $lua_files"
          echo "- JSON files: $json_files"
          echo "- Markdown files: $md_files"
          
          # Check for large changes
          total_changes=$(cat changed_files.txt | wc -l)
          if [ "$total_changes" -gt 20 ]; then
            echo "⚠️ Large PR detected ($total_changes files changed)"
            echo "Consider breaking this into smaller PRs for easier review"
          fi
          
          # Generate summary for GitHub
          echo "## 📊 PR Analysis" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| File Type | Count |" >> $GITHUB_STEP_SUMMARY
          echo "|-----------|-------|" >> $GITHUB_STEP_SUMMARY
          echo "| 🔧 Luau | $luau_files |" >> $GITHUB_STEP_SUMMARY
          echo "| 📜 Lua | $lua_files |" >> $GITHUB_STEP_SUMMARY
          echo "| 📋 JSON | $json_files |" >> $GITHUB_STEP_SUMMARY
          echo "| 📝 Markdown | $md_files |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Total files changed**: $total_changes" >> $GITHUB_STEP_SUMMARY

      - name: 🔍 Check Code Quality
        run: |
          echo "🔍 Running code quality checks on changed files..."
          
          # Only check changed Luau files
          changed_luau_files=$(git diff --name-only origin/${{ github.event.pull_request.base.ref }}...HEAD | grep "\.luau$" || true)
          
          if [ -n "$changed_luau_files" ]; then
            echo "Checking files:"
            echo "$changed_luau_files"
            
            # Run selene on changed files
            echo "$changed_luau_files" | xargs selene
            
            # Check formatting on changed files
            echo "$changed_luau_files" | xargs stylua --check
            
            echo "✅ Code quality checks passed!"
          else
            echo "ℹ️ No Luau files changed in this PR"
          fi

      - name: 📝 Check Documentation
        run: |
          echo "📝 Checking documentation requirements..."
          
          # Check if significant changes require documentation updates
          changed_luau_files=$(git diff --name-only origin/${{ github.event.pull_request.base.ref }}...HEAD | grep "\.luau$" | wc -l)
          changed_md_files=$(git diff --name-only origin/${{ github.event.pull_request.base.ref }}...HEAD | grep "\.md$" | wc -l)
          
          if [ "$changed_luau_files" -gt 5 ] && [ "$changed_md_files" -eq 0 ]; then
            echo "⚠️ Significant code changes detected without documentation updates"
            echo "Consider updating relevant documentation files"
            
            echo "## 📝 Documentation Reminder" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "⚠️ This PR includes significant code changes ($changed_luau_files Luau files) but no documentation updates." >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "Consider updating:" >> $GITHUB_STEP_SUMMARY
            echo "- API documentation" >> $GITHUB_STEP_SUMMARY
            echo "- README files" >> $GITHUB_STEP_SUMMARY
            echo "- Code comments" >> $GITHUB_STEP_SUMMARY
          else
            echo "✅ Documentation requirements satisfied"
          fi

      - name: 🧪 Test Impact Analysis
        run: |
          echo "🧪 Analyzing test impact..."
          
          # Check if changes affect core systems
          core_systems=("MAFS" "TBRDS" "CGS" "MCS")
          affected_systems=()
          
          for system in "${core_systems[@]}"; do
            if git diff --name-only origin/${{ github.event.pull_request.base.ref }}...HEAD | grep -i "$system" > /dev/null; then
              affected_systems+=("$system")
            fi
          done
          
          if [ ${#affected_systems[@]} -gt 0 ]; then
            echo "⚠️ Core systems affected: ${affected_systems[*]}"
            echo "Ensure thorough testing of these systems"
            
            echo "## 🧪 Test Impact" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "⚠️ **Core systems affected**: ${affected_systems[*]}" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "**Recommended testing:**" >> $GITHUB_STEP_SUMMARY
            for system in "${affected_systems[@]}"; do
              echo "- Test $system functionality" >> $GITHUB_STEP_SUMMARY
            done
          else
            echo "✅ No core systems directly affected"
          fi

  size-check:
    name: 📏 Size Check
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 📏 Check PR Size
        run: |
          echo "📏 Checking PR size..."
          
          # Calculate lines added/removed
          additions=$(git diff --numstat origin/${{ github.event.pull_request.base.ref }}...HEAD | awk '{sum += $1} END {print sum}')
          deletions=$(git diff --numstat origin/${{ github.event.pull_request.base.ref }}...HEAD | awk '{sum += $2} END {print sum}')
          
          # Handle empty values
          additions=${additions:-0}
          deletions=${deletions:-0}
          
          total_changes=$((additions + deletions))
          
          echo "📊 PR Size Analysis:"
          echo "- Lines added: $additions"
          echo "- Lines removed: $deletions"
          echo "- Total changes: $total_changes"
          
          # Determine PR size category
          if [ "$total_changes" -lt 50 ]; then
            size="🟢 Small"
            recommendation="Perfect size for quick review!"
          elif [ "$total_changes" -lt 200 ]; then
            size="🟡 Medium"
            recommendation="Good size for thorough review"
          elif [ "$total_changes" -lt 500 ]; then
            size="🟠 Large"
            recommendation="Consider breaking into smaller PRs"
          else
            size="🔴 Extra Large"
            recommendation="Strongly consider breaking into smaller PRs"
          fi
          
          echo "📏 PR Size: $size"
          echo "💡 Recommendation: $recommendation"
          
          # Add to GitHub summary
          echo "## 📏 PR Size Analysis" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Metric | Value |" >> $GITHUB_STEP_SUMMARY
          echo "|--------|-------|" >> $GITHUB_STEP_SUMMARY
          echo "| ➕ Lines Added | $additions |" >> $GITHUB_STEP_SUMMARY
          echo "| ➖ Lines Removed | $deletions |" >> $GITHUB_STEP_SUMMARY
          echo "| 📊 Total Changes | $total_changes |" >> $GITHUB_STEP_SUMMARY
          echo "| 📏 Size Category | $size |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**💡 Recommendation**: $recommendation" >> $GITHUB_STEP_SUMMARY
