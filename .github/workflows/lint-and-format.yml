name: 🔍 Lint and Format

on:
  push:
    branches: [ main, dev, dev-* ]
  pull_request:
    branches: [ main, dev ]
  workflow_dispatch:

jobs:
  lint-and-format:
    name: 🔍 Lin<PERSON> and Format Check
    runs-on: ubuntu-latest

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 🛠️ Setup Aftman
        uses: ok-nick/setup-aftman@v0.4.2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: 📦 Install Tools
        run: aftman install

      - name: 🔍 Run Selene Linter
        run: |
          echo "🔍 Running Selene linter..."
          selene src/
          echo "✅ Selene linting completed successfully!"

      - name: 🎨 Check StyLua Formatting
        run: |
          echo "🎨 Checking StyLua formatting..."
          stylua --check src/
          echo "✅ StyLua formatting check completed successfully!"

      - name: 📊 Generate Lint Report
        if: always()
        run: |
          echo "📊 Generating lint report..."
          echo "## 🔍 Lint Results" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          # Run selene with JSON output for detailed reporting
          if selene src/ --display-style json > selene-report.json 2>&1; then
            echo "✅ **Selene**: No issues found" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ **Selene**: Issues found" >> $GITHUB_STEP_SUMMARY
            echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
            cat selene-report.json >> $GITHUB_STEP_SUMMARY
            echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
          fi

          echo "" >> $GITHUB_STEP_SUMMARY

          # Check stylua formatting
          if stylua --check src/ > stylua-report.txt 2>&1; then
            echo "✅ **StyLua**: Code is properly formatted" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ **StyLua**: Formatting issues found" >> $GITHUB_STEP_SUMMARY
            echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
            cat stylua-report.txt >> $GITHUB_STEP_SUMMARY
            echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
          fi

      - name: 💬 Comment PR with Results
        if: github.event_name == 'pull_request' && failure()
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');

            let comment = '## 🔍 Lint and Format Check Results\n\n';

            // Add selene results if file exists
            if (fs.existsSync('selene-report.json')) {
              const seleneReport = fs.readFileSync('selene-report.json', 'utf8');
              comment += '### ❌ Selene Issues\n```json\n' + seleneReport + '\n```\n\n';
            }

            // Add stylua results if file exists
            if (fs.existsSync('stylua-report.txt')) {
              const styluaReport = fs.readFileSync('stylua-report.txt', 'utf8');
              comment += '### ❌ StyLua Formatting Issues\n```\n' + styluaReport + '\n```\n\n';
            }

            comment += '**Please fix these issues before merging.**\n\n';
            comment += '💡 **Tip**: Run `stylua src/` locally to auto-fix formatting issues.';

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

  auto-format:
    name: 🎨 Auto-format Code
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && contains(github.event.head_commit.message, '[auto-format]')

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: 🛠️ Setup Aftman
        uses: ok-nick/setup-aftman@v0.4.2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: 📦 Install Tools
        run: aftman install

      - name: 🎨 Format Code with StyLua
        run: |
          echo "🎨 Formatting code with StyLua..."
          stylua src/
          echo "✅ Code formatting completed!"

      - name: 📤 Commit Changes
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add .
          if git diff --staged --quiet; then
            echo "No formatting changes needed"
          else
            git commit -m "🎨 Auto-format code with StyLua [skip ci]"
            git push
          fi
