--[[
    SCP Realistic FPS Game - Main Menu UI
    Modern holographic military facility interface
    Responsive design with cinematic third-person perspective
    
    Author: Game Development Team
    Version: 1.0
    Last Updated: 2025
]]
--

local Players = game:GetService("Players")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")
local GuiService = game:GetService("GuiService")

local player = Players.LocalPlayer
local playerGui = player:WaitFor<PERSON>hild("PlayerGui")

-- UI Constants (Predefined)
local UI_CONSTANTS = {
	BACKGROUND = Color3.fromRGB(30, 30, 35), -- Dark background for mysterious SCP vibe
	BORDER = Color3.fromRGB(240, 240, 230), -- Teal highlight for modern accents
	SUCCESS_TEXT = Color3.fromRGB(80, 250, 123), -- Bright green for positive feedback
	ERROR_TEXT = Color3.fromRGB(255, 85, 85), -- Bright red for errors
	TEXT = Color3.fromRGB(248, 248, 242), -- Off-white for readable text
	CORNER_RADIUS = UDim.new(0, 6), -- Rounded corners for sleek look

	-- Additional holographic theme colors
	HOLOGRAM_BLUE = Color3.fromRGB(0, 162, 255),
	HOLOGRAM_CYAN = Color3.fromRGB(0, 255, 255),
	PANEL_DARK = Color3.fromRGB(20, 25, 30),
	GLASS_OVERLAY = Color3.fromRGB(255, 255, 255),
}

-- Animation constants for smooth transitions
local TWEEN_INFO = {
	FAST = TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
	MEDIUM = TweenInfo.new(0.4, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
	SLOW = TweenInfo.new(0.8, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
	GLOW = TweenInfo.new(1.5, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
}

--[[
    Main Menu UI Manager Class
    Handles all UI creation, animations, and responsive scaling
]]
--
local MainMenuUI = {}
MainMenuUI.__index = MainMenuUI

function MainMenuUI.new()
	local self = setmetatable({}, MainMenuUI)
	self.screenGui = nil
	self.mainFrame = nil
	self.buttonsContainer = nil
	self.backgroundEffects = {}
	self.activePopup = nil

	self:Initialize()
	return self
end

function MainMenuUI:Initialize()
	-- Create main ScreenGui with responsive scaling
	self.screenGui = Instance.new("ScreenGui")
	self.screenGui.Name = "SCPMainMenuUI"
	self.screenGui.ResetOnSpawn = false
	self.screenGui.IgnoreGuiInset = true
	self.screenGui.Parent = playerGui

	-- Enable UI scaling for different devices
	self.screenGui.ScreenInsets = Enum.ScreenInsets.DeviceSafeInsets

	self:CreateBackground()
	self:CreateMainInterface()
	self:CreateHolographicEffects()
	self:SetupResponsiveScaling()
end

--[[
    Creates the military facility background with environmental effects
]]
--
function MainMenuUI:CreateBackground()
	-- Main background frame
	local backgroundFrame = Instance.new("Frame")
	backgroundFrame.Name = "BackgroundFrame"
	backgroundFrame.Size = UDim2.new(1, 0, 1, 0)
	backgroundFrame.Position = UDim2.new(0, 0, 0, 0)
	backgroundFrame.BackgroundColor3 = UI_CONSTANTS.BACKGROUND
	backgroundFrame.BorderSizePixel = 0
	backgroundFrame.Parent = self.screenGui

	-- Subtle gradient overlay for depth
	local gradientOverlay = Instance.new("Frame")
	gradientOverlay.Name = "GradientOverlay"
	gradientOverlay.Size = UDim2.new(1, 0, 1, 0)
	gradientOverlay.BackgroundTransparency = 0.3
	gradientOverlay.BorderSizePixel = 0
	gradientOverlay.Parent = backgroundFrame

	local gradient = Instance.new("UIGradient")
	gradient.Color = ColorSequence.new({
		ColorSequenceKeypoint.new(0, Color3.fromRGB(40, 45, 50)),
		ColorSequenceKeypoint.new(0.5, Color3.fromRGB(25, 30, 35)),
		ColorSequenceKeypoint.new(1, Color3.fromRGB(15, 20, 25)),
	})
	gradient.Rotation = 135
	gradient.Parent = gradientOverlay

	-- Animated grid pattern for tech aesthetic
	self:CreateGridPattern(backgroundFrame)

	-- Flickering light effects (simulating facility lighting)
	self:CreateLightingEffects(backgroundFrame)
end

--[[
    Creates animated grid pattern for military tech aesthetic
]]
--
function MainMenuUI:CreateGridPattern(parent)
	local gridFrame = Instance.new("Frame")
	gridFrame.Name = "GridPattern"
	gridFrame.Size = UDim2.new(1, 0, 1, 0)
	gridFrame.BackgroundTransparency = 1
	gridFrame.Parent = parent

	-- Create subtle grid lines
	for i = 1, 10 do
		local verticalLine = Instance.new("Frame")
		verticalLine.Name = "VLine" .. i
		verticalLine.Size = UDim2.new(0, 1, 1, 0)
		verticalLine.Position = UDim2.new(i * 0.1, 0, 0, 0)
		verticalLine.BackgroundColor3 = UI_CONSTANTS.BORDER
		verticalLine.BackgroundTransparency = 0.9
		verticalLine.BorderSizePixel = 0
		verticalLine.Parent = gridFrame

		local horizontalLine = Instance.new("Frame")
		horizontalLine.Name = "HLine" .. i
		horizontalLine.Size = UDim2.new(1, 0, 0, 1)
		horizontalLine.Position = UDim2.new(0, 0, i * 0.1, 0)
		horizontalLine.BackgroundColor3 = UI_CONSTANTS.BORDER
		horizontalLine.BackgroundTransparency = 0.9
		horizontalLine.BorderSizePixel = 0
		horizontalLine.Parent = gridFrame
	end
end

--[[
    Creates main holographic interface panel
]]
--
function MainMenuUI:CreateMainInterface()
	-- Main interface container (center-positioned for all devices)
	self.mainFrame = Instance.new("Frame")
	self.mainFrame.Name = "MainInterface"
	self.mainFrame.AnchorPoint = Vector2.new(0.5, 0.5)
	self.mainFrame.Position = UDim2.new(0.5, 0, 0.5, 0)
	self.mainFrame.Size = UDim2.new(0, 800, 0, 600) -- Will be scaled responsively
	self.mainFrame.BackgroundColor3 = UI_CONSTANTS.PANEL_DARK
	self.mainFrame.BackgroundTransparency = 0.1
	self.mainFrame.BorderSizePixel = 0
	self.mainFrame.Parent = self.screenGui

	-- Rounded corners for modern look
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 12)
	corner.Parent = self.mainFrame

	-- Holographic border glow effect
	local glowFrame = Instance.new("Frame")
	glowFrame.Name = "GlowBorder"
	glowFrame.Size = UDim2.new(1, 4, 1, 4)
	glowFrame.Position = UDim2.new(0, -2, 0, -2)
	glowFrame.BackgroundColor3 = UI_CONSTANTS.HOLOGRAM_CYAN
	glowFrame.BackgroundTransparency = 0.8
	glowFrame.BorderSizePixel = 0
	glowFrame.ZIndex = self.mainFrame.ZIndex - 1
	glowFrame.Parent = self.mainFrame

	local glowCorner = Instance.new("UICorner")
	glowCorner.CornerRadius = UDim.new(0, 14)
	glowCorner.Parent = glowFrame

	-- Animate the glow effect
	local glowTween = TweenService:Create(glowFrame, TWEEN_INFO.GLOW, {
		BackgroundTransparency = 0.3,
	})
	glowTween:Play()

	self:CreateHeader()
	self:CreateMenuButtons()
end

--[[
    Creates the header section with SCP branding
]]
--
function MainMenuUI:CreateHeader()
	local headerFrame = Instance.new("Frame")
	headerFrame.Name = "Header"
	headerFrame.Size = UDim2.new(1, 0, 0, 120)
	headerFrame.Position = UDim2.new(0, 0, 0, 0)
	headerFrame.BackgroundTransparency = 1
	headerFrame.Parent = self.mainFrame

	-- Game title with holographic effect
	local titleLabel = Instance.new("TextLabel")
	titleLabel.Name = "GameTitle"
	titleLabel.Size = UDim2.new(1, -40, 0, 60)
	titleLabel.Position = UDim2.new(0, 20, 0, 20)
	titleLabel.BackgroundTransparency = 1
	titleLabel.Text = "SCP: FACILITY BREACH"
	titleLabel.TextColor3 = UI_CONSTANTS.HOLOGRAM_CYAN
	titleLabel.TextScaled = true
	titleLabel.Font = Enum.Font.SourceSansBold
	titleLabel.Parent = headerFrame

	-- Subtitle
	local subtitleLabel = Instance.new("TextLabel")
	subtitleLabel.Name = "Subtitle"
	subtitleLabel.Size = UDim2.new(1, -40, 0, 30)
	subtitleLabel.Position = UDim2.new(0, 20, 0, 80)
	subtitleLabel.BackgroundTransparency = 1
	subtitleLabel.Text = "TACTICAL OPERATIONS CENTER"
	subtitleLabel.TextColor3 = UI_CONSTANTS.TEXT
	subtitleLabel.TextScaled = true
	subtitleLabel.Font = Enum.Font.SourceSans
	subtitleLabel.TextTransparency = 0.3
	subtitleLabel.Parent = headerFrame

	-- Animated typing effect for subtitle
	self:AnimateTextReveal(subtitleLabel)
end

--[[
    Creates main menu buttons with holographic styling
]]
--
function MainMenuUI:CreateMenuButtons()
	self.buttonsContainer = Instance.new("Frame")
	self.buttonsContainer.Name = "ButtonsContainer"
	self.buttonsContainer.Size = UDim2.new(1, -60, 1, -180)
	self.buttonsContainer.Position = UDim2.new(0, 30, 0, 140)
	self.buttonsContainer.BackgroundTransparency = 1
	self.buttonsContainer.Parent = self.mainFrame

	-- Layout for responsive button arrangement
	local listLayout = Instance.new("UIListLayout")
	listLayout.SortOrder = Enum.SortOrder.LayoutOrder
	listLayout.Padding = UDim.new(0, 15)
	listLayout.HorizontalAlignment = Enum.HorizontalAlignment.Center
	listLayout.VerticalAlignment = Enum.VerticalAlignment.Top
	listLayout.Parent = self.buttonsContainer

	-- Menu button configurations
	local menuButtons = {
		{ name = "PLAY", primary = true, order = 1, description = "Enter the Facility" },
		{ name = "LOADOUT", primary = false, order = 2, description = "Equipment & Customization" },
		{ name = "SHOP", primary = false, order = 3, description = "Upgrades & Purchases" },
		{ name = "SETTINGS", primary = false, order = 4, description = "Graphics • Audio • Controls" },
		{ name = "CHANGELOG", primary = false, order = 5, description = "Recent Updates" },
		{ name = "CREDITS", primary = false, order = 6, description = "Development Team" },
	}

	-- Create each menu button
	for _, buttonConfig in ipairs(menuButtons) do
		self:CreateHolographicButton(buttonConfig)
	end
end

--[[
    Creates individual holographic-styled buttons
]]
--
function MainMenuUI:CreateHolographicButton(config)
	local buttonFrame = Instance.new("Frame")
	buttonFrame.Name = config.name .. "Button"
	buttonFrame.Size = UDim2.new(1, 0, 0, config.primary and 70 or 55)
	buttonFrame.BackgroundColor3 = UI_CONSTANTS.PANEL_DARK
	buttonFrame.BackgroundTransparency = 0.3
	buttonFrame.BorderSizePixel = 0
	buttonFrame.LayoutOrder = config.order
	buttonFrame.Parent = self.buttonsContainer

	-- Rounded corners
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UI_CONSTANTS.CORNER_RADIUS
	corner.Parent = buttonFrame

	-- Holographic border
	local borderFrame = Instance.new("Frame")
	borderFrame.Name = "HoloBorder"
	borderFrame.Size = UDim2.new(1, 2, 1, 2)
	borderFrame.Position = UDim2.new(0, -1, 0, -1)
	borderFrame.BackgroundColor3 = config.primary and UI_CONSTANTS.HOLOGRAM_CYAN or UI_CONSTANTS.BORDER
	borderFrame.BackgroundTransparency = 0.7
	borderFrame.BorderSizePixel = 0
	borderFrame.ZIndex = buttonFrame.ZIndex - 1
	borderFrame.Parent = buttonFrame

	local borderCorner = Instance.new("UICorner")
	borderCorner.CornerRadius = UDim.new(0, 8)
	borderCorner.Parent = borderFrame

	-- Button text
	local buttonText = Instance.new("TextLabel")
	buttonText.Name = "ButtonText"
	buttonText.Size = UDim2.new(1, -20, 0.6, 0)
	buttonText.Position = UDim2.new(0, 10, 0, 5)
	buttonText.BackgroundTransparency = 1
	buttonText.Text = config.name
	buttonText.TextColor3 = config.primary and UI_CONSTANTS.HOLOGRAM_CYAN or UI_CONSTANTS.TEXT
	buttonText.TextScaled = true
	buttonText.Font = config.primary and Enum.Font.SourceSansBold or Enum.Font.SourceSans
	buttonText.Parent = buttonFrame

	-- Description text
	local descText = Instance.new("TextLabel")
	descText.Name = "Description"
	descText.Size = UDim2.new(1, -20, 0.4, 0)
	descText.Position = UDim2.new(0, 10, 0.6, 0)
	descText.BackgroundTransparency = 1
	descText.Text = config.description
	descText.TextColor3 = UI_CONSTANTS.TEXT
	descText.TextTransparency = 0.5
	descText.TextScaled = true
	descText.Font = Enum.Font.SourceSans
	descText.Parent = buttonFrame

	-- Interactive button behavior
	local button = Instance.new("TextButton")
	button.Name = "InteractiveButton"
	button.Size = UDim2.new(1, 0, 1, 0)
	button.BackgroundTransparency = 1
	button.Text = ""
	button.Parent = buttonFrame

	-- Hover and click animations
	self:SetupButtonAnimations(button, buttonFrame, borderFrame, buttonText, config.primary)
end

--[[
    Sets up hover and click animations for buttons
]]
--
function MainMenuUI:SetupButtonAnimations(button, buttonFrame, borderFrame, buttonText, isPrimary)
	local originalBgTransparency = buttonFrame.BackgroundTransparency
	local originalBorderTransparency = borderFrame.BackgroundTransparency
	local originalTextColor = buttonText.TextColor3

	-- Hover effects
	button.MouseEnter:Connect(function()
		local hoverTween = TweenService:Create(buttonFrame, TWEEN_INFO.FAST, {
			BackgroundTransparency = 0.1,
		})
		local borderTween = TweenService:Create(borderFrame, TWEEN_INFO.FAST, {
			BackgroundTransparency = 0.3,
		})
		local textTween = TweenService:Create(buttonText, TWEEN_INFO.FAST, {
			TextColor3 = isPrimary and UI_CONSTANTS.SUCCESS_TEXT or UI_CONSTANTS.HOLOGRAM_CYAN,
		})

		hoverTween:Play()
		borderTween:Play()
		textTween:Play()
	end)

	button.MouseLeave:Connect(function()
		local unhoverTween = TweenService:Create(buttonFrame, TWEEN_INFO.FAST, {
			BackgroundTransparency = originalBgTransparency,
		})
		local borderTween = TweenService:Create(borderFrame, TWEEN_INFO.FAST, {
			BackgroundTransparency = originalBorderTransparency,
		})
		local textTween = TweenService:Create(buttonText, TWEEN_INFO.FAST, {
			TextColor3 = originalTextColor,
		})

		unhoverTween:Play()
		borderTween:Play()
		textTween:Play()
	end)

	-- Click effect
	button.MouseButton1Click:Connect(function()
		-- Store original size
		local originalSize = buttonFrame.Size

		-- Click animation (scale down)
		local clickTween = TweenService:Create(buttonFrame, TWEEN_INFO.FAST, {
			Size = UDim2.new(
				originalSize.X.Scale * 0.95,
				originalSize.X.Offset * 0.95,
				originalSize.Y.Scale * 0.95,
				originalSize.Y.Offset * 0.95
			),
		})
		clickTween:Play()

		-- Return to original size after animation completes
		clickTween.Completed:Connect(function()
			local returnTween = TweenService:Create(buttonFrame, TWEEN_INFO.FAST, {
				Size = originalSize,
			})
			returnTween:Play()
		end)

		-- Handle button functionality here
		self:HandleButtonClick(button.Parent.Name)
	end)
end

--[[
    Creates holographic visual effects and particles
]]
--
function MainMenuUI:CreateHolographicEffects()
	-- Scanning line effect
	local scanLine = Instance.new("Frame")
	scanLine.Name = "ScanLine"
	scanLine.Size = UDim2.new(1, 0, 0, 2)
	scanLine.Position = UDim2.new(0, 0, 0, 0)
	scanLine.BackgroundColor3 = UI_CONSTANTS.HOLOGRAM_CYAN
	scanLine.BackgroundTransparency = 0.3
	scanLine.BorderSizePixel = 0
	scanLine.Parent = self.mainFrame

	-- Animate scanning line
	local scanTween = TweenService:Create(
		scanLine,
		TweenInfo.new(3, Enum.EasingStyle.Linear, Enum.EasingDirection.InOut, -1, false),
		{
			Position = UDim2.new(0, 0, 1, 0),
		}
	)
	scanTween:Play()

	-- Add to effects table for cleanup
	table.insert(self.backgroundEffects, scanTween)
end

--[[
    Creates lighting effects for facility atmosphere
]]
--
function MainMenuUI:CreateLightingEffects(parent)
	-- Emergency light flickering simulation
	local lightOverlay = Instance.new("Frame")
	lightOverlay.Name = "LightOverlay"
	lightOverlay.Size = UDim2.new(1, 0, 1, 0)
	lightOverlay.BackgroundColor3 = Color3.fromRGB(255, 100, 100)
	lightOverlay.BackgroundTransparency = 0.95
	lightOverlay.BorderSizePixel = 0
	lightOverlay.Parent = parent

	-- Random flicker effect
	task.spawn(function()
		while lightOverlay.Parent do
			task.wait(math.random(3, 8))
			local flickerTween = TweenService:Create(lightOverlay, TweenInfo.new(0.1, Enum.EasingStyle.Linear), {
				BackgroundTransparency = 0.85,
			})
			flickerTween:Play()
			flickerTween.Completed:Connect(function()
				local returnTween = TweenService:Create(lightOverlay, TweenInfo.new(0.2, Enum.EasingStyle.Linear), {
					BackgroundTransparency = 0.95,
				})
				returnTween:Play()
			end)
		end
	end)
end

--[[
    Sets up responsive scaling for different screen sizes
]]
--
function MainMenuUI:SetupResponsiveScaling()
	local function updateScale()
		local viewportSize = workspace.CurrentCamera.ViewportSize
		local minDimension = math.min(viewportSize.X, viewportSize.Y)

		-- Scale factor based on screen size
		local scaleFactor = math.max(0.6, math.min(1.2, minDimension / 800))

		if self.mainFrame then
			local newSize = UDim2.new(0, 800 * scaleFactor, 0, 600 * scaleFactor)
			local scaleTween = TweenService:Create(self.mainFrame, TWEEN_INFO.MEDIUM, {
				Size = newSize,
			})
			scaleTween:Play()
		end
	end

	-- Update scale on viewport change
	workspace.CurrentCamera:GetPropertyChangedSignal("ViewportSize"):Connect(updateScale)
	updateScale() -- Initial scale
end

--[[
    Handles button click events with complete navigation logic
]]
--
function MainMenuUI:HandleButtonClick(buttonName)
	print("Button clicked:", buttonName)

	-- Handle different button actions
	if buttonName == "PLAYButton" then
		self:HandlePlayButton()
	elseif buttonName == "LOADOUTButton" then
		self:HandleLoadoutButton()
	elseif buttonName == "SHOPButton" then
		self:HandleShopButton()
	elseif buttonName == "SETTINGSButton" then
		self:HandleSettingsButton()
	elseif buttonName == "CHANGELOGButton" then
		self:ShowChangelogPopup()
	elseif buttonName == "CREDITSButton" then
		self:ShowCreditsPopup()
	end
end

--[[
    Handles Play button - starts matchmaking or game selection
]]
--
function MainMenuUI:HandlePlayButton()
	-- Create loading overlay
	local loadingOverlay = self:CreateLoadingOverlay("INITIALIZING FACILITY ACCESS...")

	-- Simulate connection/matchmaking process
	task.spawn(function()
		task.wait(2)
		loadingOverlay:Destroy()

		-- In a real implementation, this would connect to game servers
		-- For now, show a placeholder message
		self:ShowNotificationPopup(
			"FACILITY ACCESS",
			"Connecting to SCP Facility...\n\nThis would normally start the game or show server selection."
		)
	end)
end

--[[
    Handles Loadout button - opens equipment customization
]]
--
function MainMenuUI:HandleLoadoutButton()
	self:ShowNotificationPopup(
		"LOADOUT SYSTEM",
		"Equipment customization interface would open here.\n\n• Weapon Selection\n• Tactical Gear\n• Character Customization\n• Skill Trees"
	)
end

--[[
    Handles Shop button - opens in-game store
]]
--
function MainMenuUI:HandleShopButton()
	self:ShowNotificationPopup(
		"TACTICAL STORE",
		"In-game store interface would open here.\n\n• Weapon Upgrades\n• Equipment Packs\n• Cosmetic Items\n• Battle Pass"
	)
end

--[[
    Handles Settings button - opens game settings
]]
--
function MainMenuUI:HandleSettingsButton()
	self:ShowSettingsPopup()
end

--[[
    Creates a loading overlay with animated text
]]
--
function MainMenuUI:CreateLoadingOverlay(message)
	local overlay = Instance.new("Frame")
	overlay.Name = "LoadingOverlay"
	overlay.Size = UDim2.new(1, 0, 1, 0)
	overlay.Position = UDim2.new(0, 0, 0, 0)
	overlay.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
	overlay.BackgroundTransparency = 0.3
	overlay.BorderSizePixel = 0
	overlay.ZIndex = 1000
	overlay.Parent = self.screenGui

	local loadingFrame = Instance.new("Frame")
	loadingFrame.Name = "LoadingFrame"
	loadingFrame.AnchorPoint = Vector2.new(0.5, 0.5)
	loadingFrame.Position = UDim2.new(0.5, 0, 0.5, 0)
	loadingFrame.Size = UDim2.new(0, 400, 0, 150)
	loadingFrame.BackgroundColor3 = UI_CONSTANTS.PANEL_DARK
	loadingFrame.BackgroundTransparency = 0.1
	loadingFrame.BorderSizePixel = 0
	loadingFrame.Parent = overlay

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 10)
	corner.Parent = loadingFrame

	local loadingText = Instance.new("TextLabel")
	loadingText.Size = UDim2.new(1, -20, 0.6, 0)
	loadingText.Position = UDim2.new(0, 10, 0, 10)
	loadingText.BackgroundTransparency = 1
	loadingText.Text = message
	loadingText.TextColor3 = UI_CONSTANTS.HOLOGRAM_CYAN
	loadingText.TextScaled = true
	loadingText.Font = Enum.Font.SourceSansBold
	loadingText.Parent = loadingFrame

	-- Animated loading dots
	local dotsText = Instance.new("TextLabel")
	dotsText.Size = UDim2.new(1, -20, 0.4, 0)
	dotsText.Position = UDim2.new(0, 10, 0.6, 0)
	dotsText.BackgroundTransparency = 1
	dotsText.Text = ""
	dotsText.TextColor3 = UI_CONSTANTS.TEXT
	dotsText.TextScaled = true
	dotsText.Font = Enum.Font.SourceSans
	dotsText.Parent = loadingFrame

	-- Animate loading dots
	task.spawn(function()
		local dots = { "", ".", "..", "..." }
		local index = 1
		while overlay.Parent do
			dotsText.Text = dots[index]
			index = index % 4 + 1
			task.wait(0.5)
		end
	end)

	return overlay
end

--[[
    Shows a general notification popup
]]
--
function MainMenuUI:ShowNotificationPopup(title, message)
	if self.activePopup then
		self.activePopup:Destroy()
	end

	local popup = Instance.new("Frame")
	popup.Name = "NotificationPopup"
	popup.AnchorPoint = Vector2.new(0.5, 0.5)
	popup.Position = UDim2.new(0.5, 0, 0.5, 0)
	popup.Size = UDim2.new(0, 500, 0, 350)
	popup.BackgroundColor3 = UI_CONSTANTS.PANEL_DARK
	popup.BackgroundTransparency = 0.1
	popup.BorderSizePixel = 0
	popup.Parent = self.screenGui

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 10)
	corner.Parent = popup

	-- Close button
	local closeButton = Instance.new("TextButton")
	closeButton.Size = UDim2.new(0, 30, 0, 30)
	closeButton.Position = UDim2.new(1, -40, 0, 10)
	closeButton.Text = "×"
	closeButton.TextColor3 = UI_CONSTANTS.TEXT
	closeButton.TextScaled = true
	closeButton.BackgroundTransparency = 1
	closeButton.Font = Enum.Font.SourceSansBold
	closeButton.Parent = popup

	closeButton.MouseButton1Click:Connect(function()
		popup:Destroy()
		self.activePopup = nil
	end)

	-- Title
	local titleLabel = Instance.new("TextLabel")
	titleLabel.Size = UDim2.new(1, -20, 0, 50)
	titleLabel.Position = UDim2.new(0, 10, 0, 10)
	titleLabel.Text = title
	titleLabel.TextColor3 = UI_CONSTANTS.HOLOGRAM_CYAN
	titleLabel.TextScaled = true
	titleLabel.BackgroundTransparency = 1
	titleLabel.Font = Enum.Font.SourceSansBold
	titleLabel.Parent = popup

	-- Message
	local messageLabel = Instance.new("TextLabel")
	messageLabel.Size = UDim2.new(1, -40, 1, -120)
	messageLabel.Position = UDim2.new(0, 20, 0, 70)
	messageLabel.Text = message
	messageLabel.TextColor3 = UI_CONSTANTS.TEXT
	messageLabel.TextScaled = true
	messageLabel.BackgroundTransparency = 1
	messageLabel.Font = Enum.Font.SourceSans
	messageLabel.TextYAlignment = Enum.TextYAlignment.Top
	messageLabel.Parent = popup

	self.activePopup = popup

	-- Animate popup appearance
	popup.Size = UDim2.new(0, 0, 0, 0)
	local popupTween = TweenService:Create(popup, TWEEN_INFO.MEDIUM, {
		Size = UDim2.new(0, 500, 0, 350),
	})
	popupTween:Play()
end

--[[
    Shows settings popup with game configuration options
]]
--
function MainMenuUI:ShowSettingsPopup()
	if self.activePopup then
		self.activePopup:Destroy()
	end

	local popup = Instance.new("Frame")
	popup.Name = "SettingsPopup"
	popup.AnchorPoint = Vector2.new(0.5, 0.5)
	popup.Position = UDim2.new(0.5, 0, 0.5, 0)
	popup.Size = UDim2.new(0, 600, 0, 450)
	popup.BackgroundColor3 = UI_CONSTANTS.PANEL_DARK
	popup.BackgroundTransparency = 0.1
	popup.BorderSizePixel = 0
	popup.Parent = self.screenGui

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 10)
	corner.Parent = popup

	-- Close button
	local closeButton = Instance.new("TextButton")
	closeButton.Size = UDim2.new(0, 30, 0, 30)
	closeButton.Position = UDim2.new(1, -40, 0, 10)
	closeButton.Text = "×"
	closeButton.TextColor3 = UI_CONSTANTS.TEXT
	closeButton.TextScaled = true
	closeButton.BackgroundTransparency = 1
	closeButton.Font = Enum.Font.SourceSansBold
	closeButton.Parent = popup

	closeButton.MouseButton1Click:Connect(function()
		popup:Destroy()
		self.activePopup = nil
	end)

	-- Title
	local title = Instance.new("TextLabel")
	title.Size = UDim2.new(1, -20, 0, 40)
	title.Position = UDim2.new(0, 10, 0, 10)
	title.Text = "SETTINGS"
	title.TextColor3 = UI_CONSTANTS.HOLOGRAM_CYAN
	title.TextScaled = true
	title.BackgroundTransparency = 1
	title.Font = Enum.Font.SourceSansBold
	title.Parent = popup

	-- Settings content placeholder
	local settingsText = Instance.new("TextLabel")
	settingsText.Size = UDim2.new(1, -40, 1, -100)
	settingsText.Position = UDim2.new(0, 20, 0, 60)
	settingsText.Text =
		"GRAPHICS SETTINGS\n• Resolution: Auto\n• Quality: High\n• FOV: 90°\n\nAUDIO SETTINGS\n• Master Volume: 100%\n• SFX Volume: 80%\n• Music Volume: 60%\n\nCONTROLS\n• Mouse Sensitivity: 50%\n• Key Bindings: Default\n\n[Settings interface would be implemented here]"
	settingsText.TextColor3 = UI_CONSTANTS.TEXT
	settingsText.TextScaled = true
	settingsText.BackgroundTransparency = 1
	settingsText.Font = Enum.Font.SourceSans
	settingsText.TextYAlignment = Enum.TextYAlignment.Top
	settingsText.Parent = popup

	self.activePopup = popup

	-- Animate popup appearance
	popup.Size = UDim2.new(0, 0, 0, 0)
	local popupTween = TweenService:Create(popup, TWEEN_INFO.MEDIUM, {
		Size = UDim2.new(0, 600, 0, 450),
	})
	popupTween:Play()
end

--[[
    Creates animated text reveal effect
]]
--
function MainMenuUI:AnimateTextReveal(textLabel)
	local originalText = textLabel.Text
	textLabel.Text = ""

	task.spawn(function()
		for i = 1, #originalText do
			textLabel.Text = string.sub(originalText, 1, i)
			task.wait(0.05)
		end
	end)
end

--[[
    Shows changelog popup (example implementation)
]]
--
function MainMenuUI:ShowChangelogPopup()
	if self.activePopup then
		self.activePopup:Destroy()
	end

	-- Create popup frame
	local popup = Instance.new("Frame")
	popup.Name = "ChangelogPopup"
	popup.AnchorPoint = Vector2.new(0.5, 0.5)
	popup.Position = UDim2.new(0.5, 0, 0.5, 0)
	popup.Size = UDim2.new(0, 500, 0, 400)
	popup.BackgroundColor3 = UI_CONSTANTS.PANEL_DARK
	popup.BackgroundTransparency = 0.1
	popup.BorderSizePixel = 0
	popup.Parent = self.screenGui

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 10)
	corner.Parent = popup

	-- Close button
	local closeButton = Instance.new("TextButton")
	closeButton.Size = UDim2.new(0, 30, 0, 30)
	closeButton.Position = UDim2.new(1, -40, 0, 10)
	closeButton.Text = "×"
	closeButton.TextColor3 = UI_CONSTANTS.TEXT
	closeButton.TextScaled = true
	closeButton.BackgroundTransparency = 1
	closeButton.Font = Enum.Font.SourceSansBold
	closeButton.Parent = popup

	closeButton.MouseButton1Click:Connect(function()
		popup:Destroy()
		self.activePopup = nil
	end)

	-- Title
	local title = Instance.new("TextLabel")
	title.Size = UDim2.new(1, -20, 0, 40)
	title.Position = UDim2.new(0, 10, 0, 10)
	title.Text = "CHANGELOG"
	title.TextColor3 = UI_CONSTANTS.HOLOGRAM_CYAN
	title.TextScaled = true
	title.BackgroundTransparency = 1
	title.Font = Enum.Font.SourceSansBold
	title.Parent = popup

	-- Scrolling changelog container
	local changelogFrame = Instance.new("ScrollingFrame")
	changelogFrame.Size = UDim2.new(1, -20, 1, -70)
	changelogFrame.Position = UDim2.new(0, 10, 0, 60)
	changelogFrame.BackgroundTransparency = 1
	changelogFrame.BorderSizePixel = 0
	changelogFrame.ScrollBarThickness = 8
	changelogFrame.ScrollBarImageColor3 = UI_CONSTANTS.HOLOGRAM_CYAN
	changelogFrame.Parent = popup

	-- Changelog content
	local changelogText = Instance.new("TextLabel")
	changelogText.Size = UDim2.new(1, -20, 0, 600) -- Will be adjusted based on content
	changelogText.Position = UDim2.new(0, 10, 0, 0)
	changelogText.BackgroundTransparency = 1
	changelogText.TextColor3 = UI_CONSTANTS.TEXT
	changelogText.TextScaled = false
	changelogText.TextSize = 14
	changelogText.Font = Enum.Font.SourceSans
	changelogText.TextYAlignment = Enum.TextYAlignment.Top
	changelogText.TextXAlignment = Enum.TextXAlignment.Left
	changelogText.Parent = changelogFrame

	-- Changelog content text
	local changelogContent = [[
VERSION 1.2.0 - FACILITY EXPANSION
Released: January 2025

NEW FEATURES:
• Added new SCP containment zones
• Implemented advanced lighting system
• New tactical equipment loadouts
• Enhanced multiplayer matchmaking
• Voice chat integration
• Spectator mode for eliminated players

IMPROVEMENTS:
• Optimized rendering performance by 30%
• Improved UI responsiveness on mobile devices
• Enhanced holographic effects and animations
• Better audio mixing and spatial sound
• Reduced loading times significantly

BUG FIXES:
• Fixed issue with button animations not playing
• Resolved memory leak in background effects
• Fixed text scaling on ultrawide monitors
• Corrected collision detection in certain areas
• Fixed rare crash when switching between menus

---

VERSION 1.1.5 - HOTFIX
Released: December 2024

BUG FIXES:
• Emergency fix for server connection issues
• Resolved UI scaling problems on tablets
• Fixed audio not playing in certain scenarios

---

VERSION 1.1.0 - TACTICAL UPDATE
Released: November 2024

NEW FEATURES:
• New weapon customization system
• Added night vision equipment
• Implemented team communication tools
• New SCP entities and behaviors

IMPROVEMENTS:
• Enhanced graphics quality options
• Better mobile device support
• Improved server stability
• Updated UI with military aesthetic

---

VERSION 1.0.0 - INITIAL RELEASE
Released: October 2024

FEATURES:
• Core SCP facility gameplay
• Multiplayer support up to 16 players
• Basic weapon and equipment system
• Immersive facility environment
• Cross-platform compatibility
]]

	changelogText.Text = changelogContent

	-- Update canvas size based on text
	changelogFrame.CanvasSize = UDim2.new(0, 0, 0, changelogText.TextBounds.Y + 50)

	self.activePopup = popup

	-- Animate popup appearance
	popup.Size = UDim2.new(0, 0, 0, 0)
	local popupTween = TweenService:Create(popup, TWEEN_INFO.MEDIUM, {
		Size = UDim2.new(0, 500, 0, 400),
	})
	popupTween:Play()
end

--[[
    Shows credits popup with movie-like presentation
]]
--
function MainMenuUI:ShowCreditsPopup()
	if self.activePopup then
		self.activePopup:Destroy()
	end

	-- Create popup frame
	local popup = Instance.new("Frame")
	popup.Name = "CreditsPopup"
	popup.AnchorPoint = Vector2.new(0.5, 0.5)
	popup.Position = UDim2.new(0.5, 0, 0.5, 0)
	popup.Size = UDim2.new(0, 600, 0, 500)
	popup.BackgroundColor3 = UI_CONSTANTS.PANEL_DARK
	popup.BackgroundTransparency = 0.05
	popup.BorderSizePixel = 0
	popup.Parent = self.screenGui

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 12)
	corner.Parent = popup

	-- Holographic border
	local glowFrame = Instance.new("Frame")
	glowFrame.Name = "GlowBorder"
	glowFrame.Size = UDim2.new(1, 4, 1, 4)
	glowFrame.Position = UDim2.new(0, -2, 0, -2)
	glowFrame.BackgroundColor3 = UI_CONSTANTS.HOLOGRAM_CYAN
	glowFrame.BackgroundTransparency = 0.7
	glowFrame.BorderSizePixel = 0
	glowFrame.ZIndex = popup.ZIndex - 1
	glowFrame.Parent = popup

	local glowCorner = Instance.new("UICorner")
	glowCorner.CornerRadius = UDim.new(0, 14)
	glowCorner.Parent = glowFrame

	-- Close button
	local closeButton = Instance.new("TextButton")
	closeButton.Size = UDim2.new(0, 35, 0, 35)
	closeButton.Position = UDim2.new(1, -45, 0, 10)
	closeButton.Text = "×"
	closeButton.TextColor3 = UI_CONSTANTS.TEXT
	closeButton.TextScaled = true
	closeButton.BackgroundTransparency = 1
	closeButton.Font = Enum.Font.SourceSansBold
	closeButton.Parent = popup

	closeButton.MouseButton1Click:Connect(function()
		popup:Destroy()
		self.activePopup = nil
	end)

	-- Title
	local title = Instance.new("TextLabel")
	title.Size = UDim2.new(1, -20, 0, 50)
	title.Position = UDim2.new(0, 10, 0, 10)
	title.Text = "DEVELOPMENT TEAM"
	title.TextColor3 = UI_CONSTANTS.HOLOGRAM_CYAN
	title.TextScaled = true
	title.BackgroundTransparency = 1
	title.Font = Enum.Font.SourceSansBold
	title.Parent = popup

	-- Scrolling credits container
	local creditsFrame = Instance.new("ScrollingFrame")
	creditsFrame.Size = UDim2.new(1, -20, 1, -80)
	creditsFrame.Position = UDim2.new(0, 10, 0, 70)
	creditsFrame.BackgroundTransparency = 1
	creditsFrame.BorderSizePixel = 0
	creditsFrame.ScrollBarThickness = 8
	creditsFrame.ScrollBarImageColor3 = UI_CONSTANTS.HOLOGRAM_CYAN
	creditsFrame.Parent = popup

	-- Credits content
	local creditsText = Instance.new("TextLabel")
	creditsText.Size = UDim2.new(1, -20, 0, 800) -- Will be adjusted based on content
	creditsText.Position = UDim2.new(0, 10, 0, 0)
	creditsText.BackgroundTransparency = 1
	creditsText.TextColor3 = UI_CONSTANTS.TEXT
	creditsText.TextScaled = false
	creditsText.TextSize = 16
	creditsText.Font = Enum.Font.SourceSans
	creditsText.TextYAlignment = Enum.TextYAlignment.Top
	creditsText.TextXAlignment = Enum.TextXAlignment.Center
	creditsText.Parent = creditsFrame

	-- Credits content text
	local creditsContent = [[
SCP: FACILITY BREACH
Tactical Operations Center

DEVELOPMENT TEAM

Game Director
BleckWolf

Lead Programmer
Dynamic Innovative Studio

UI/UX Design
Game Development Team

3D Modeling & Animation
Asset Creation Team

Sound Design
Audio Engineering Team

Quality Assurance
Testing Division

SPECIAL THANKS

SCP Foundation Community
For the incredible lore and inspiration

Roblox Developer Community
For tools, resources, and support

Beta Testers
For their valuable feedback

TECHNOLOGY

Built with Roblox Studio
Luau Programming Language
Advanced UI Framework
Custom Animation System

COPYRIGHT

This game is a fan creation inspired by
the SCP Foundation universe.

All SCP-related content is licensed under
Creative Commons Attribution-ShareAlike 3.0

Original SCP Foundation content created by
the SCP Foundation community

© 2025 Dynamic Innovative Studio
All rights reserved.

Thank you for playing!
]]

	creditsText.Text = creditsContent

	-- Update canvas size based on text
	creditsFrame.CanvasSize = UDim2.new(0, 0, 0, creditsText.TextBounds.Y + 50)

	self.activePopup = popup

	-- Animate popup appearance
	popup.Size = UDim2.new(0, 0, 0, 0)
	local popupTween = TweenService:Create(popup, TWEEN_INFO.MEDIUM, {
		Size = UDim2.new(0, 600, 0, 500),
	})
	popupTween:Play()
end

--[[
    Cleanup function for proper resource management
]]
--
function MainMenuUI:Cleanup()
	-- Stop all background effect tweens
	for _, tween in ipairs(self.backgroundEffects) do
		if tween then
			tween:Cancel()
		end
	end

	-- Destroy main UI
	if self.screenGui then
		self.screenGui:Destroy()
	end
end

-- Initialize the main menu
local mainMenu = MainMenuUI.new()

-- Keep reference for potential cleanup or external access
-- mainMenu can be used later for cleanup: mainMenu:Cleanup()

-- Export for external access (if needed)
return MainMenuUI
