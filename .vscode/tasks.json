{"version": "2.0.0", "tasks": [{"label": "Selene: <PERSON>t Current File", "type": "shell", "command": "selene", "args": ["${file}"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": {"owner": "selene", "fileLocation": "absolute", "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}, {"label": "Selene: <PERSON><PERSON>", "type": "shell", "command": "selene", "args": ["src/"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": {"owner": "selene", "fileLocation": "absolute", "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}, {"label": "Format: Current File", "type": "shell", "command": "${command:editor.action.formatDocument}", "group": "build", "presentation": {"echo": false, "reveal": "never"}}]}