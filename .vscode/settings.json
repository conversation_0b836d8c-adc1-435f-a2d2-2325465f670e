{
  // You can freely change the settings in this file to your liking. But do not commit them.
  // This file makes everyone editor's experience consistent, beautiful, productive and efficient.
  // Avoid changing too much.

  // ===== EDITOR CONFIGURATION =====
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.formatOnType": false,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.trimAutoWhitespace": true,
  "editor.renderWhitespace": "boundary",
  "editor.wordWrap": "bounded",
  "editor.wordWrapColumn": 100,
  "editor.rulers": [100, 180],
  "editor.lineHeight": 1.6,
  "editor.fontSize": 14,
  "editor.fontFamily": "'Fira Code', 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', <PERSON><PERSON><PERSON>, 'Courier New', monospace",
  "editor.fontLigatures": true,
  "editor.cursorBlinking": "smooth",
  "editor.cursorSmoothCaretAnimation": "on",
  "editor.smoothScrolling": true,
  "editor.minimap.enabled": true,
  "scm.compactFolders": false,
  "editor.minimap.maxColumn": 80,
  "editor.bracketPairColorization.enabled": true,
  "editor.guides.bracketPairs": "active",
  "editor.guides.indentation": true,
  "editor.linkedEditing": true,
  "editor.suggest.insertMode": "replace",
  "editor.suggest.preview": true,
  "editor.acceptSuggestionOnCommitCharacter": false,
  "editor.acceptSuggestionOnEnter": "on",
  "editor.quickSuggestions": {
    "other": "on",
    "comments": "off",
    "strings": "on"
  },
  "editor.parameterHints.enabled": true,
  "editor.hover.delay": 300,
  "editor.hover.sticky": true,

  // ===== CODE ACTIONS ON SAVE =====
  "editor.codeActionsOnSave": {
    "source.fixAll": "explicit",
    "source.fixAll.eslint": "explicit",
    "source.fixAll.ts": "explicit",
    "source.organizeImports": "explicit",
    "source.removeUnusedImports": "explicit",
    "source.sortImports": "never"
  },

  // ===== LUAU LANGUAGE SERVER CONFIGURATION =====
  "luau-lsp.require.mode": "relativeToFile",
  "luau-lsp.platform.type": "roblox",
  "luau-lsp.types.robloxSecurityLevel": "None",

  // ===== SELENE LINTER CONFIGURATION =====
  "selene.config": "selene.toml",

  // ===== ESLINT CONFIGURATION =====
  "eslint.enable": true,
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "json",
    "jsonc"
  ],
  "eslint.useFlatConfig": true,
  "eslint.workingDirectories": [
    {
      "mode": "auto"
    }
  ],
  "eslint.run": "onType",
  "eslint.format.enable": false,
  "eslint.lintTask.enable": true,
  "eslint.quiet": false,
  "eslint.debug": false,

  // ===== TYPESCRIPT CONFIGURATION =====
  "typescript.updateImportsOnFileMove.enabled": "always",
  "typescript.suggest.autoImports": true,
  "typescript.preferences.includePackageJsonAutoImports": "auto",
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.preferences.quoteStyle": "single",
  "typescript.format.enable": false,
  "typescript.validate.enable": true,
  "typescript.suggest.completeFunctionCalls": true,
  "typescript.suggest.enabled": true,
  "typescript.suggest.paths": true,
  "typescript.workspaceSymbols.scope": "allOpenProjects",
  "typescript.inlayHints.parameterNames.enabled": "literals",
  "typescript.inlayHints.parameterTypes.enabled": false,
  "typescript.inlayHints.variableTypes.enabled": false,
  "typescript.inlayHints.propertyDeclarationTypes.enabled": false,
  "typescript.inlayHints.functionLikeReturnTypes.enabled": false,
  "typescript.inlayHints.enumMemberValues.enabled": false,

  // ===== JAVASCRIPT CONFIGURATION =====
  "javascript.updateImportsOnFileMove.enabled": "always",
  "javascript.suggest.autoImports": true,
  "javascript.preferences.importModuleSpecifier": "relative",
  "javascript.preferences.quoteStyle": "single",
  "javascript.format.enable": false,
  "javascript.validate.enable": true,
  "javascript.suggest.completeFunctionCalls": true,
  "javascript.suggest.enabled": true,
  "javascript.suggest.names": true,
  "javascript.suggest.paths": true,

  // ===== PRETTIER CONFIGURATION =====
  "prettier.enable": true,
  "prettier.requireConfig": true,
  "prettier.useEditorConfig": true,
  "prettier.resolveGlobalModules": true,
  "prettier.withNodeModules": false,
  "prettier.documentSelectors": [
    "**/*.{js,jsx,ts,tsx,json,css,scss,md,html,vue}"
  ],

  // ===== EMMET CONFIGURATION =====
  "emmet.includeLanguages": {
    "javascript": "javascriptreact",
    "typescript": "typescriptreact"
  },
  "emmet.triggerExpansionOnTab": true,
  "emmet.showExpandedAbbreviation": "always",
  "emmet.showSuggestionsAsSnippets": true,

  // ===== FILE ASSOCIATIONS =====
  "files.associations": {
    "*.js": "javascript",
    "*.jsx": "javascriptreact",
    "*.ts": "typescript",
    "*.tsx": "typescriptreact",
    "*.mjs": "javascript",
    "*.cjs": "javascript",
    "*.json": "jsonc",
    ".eslintrc": "jsonc",
    ".prettierrc": "jsonc",
    ".babelrc": "jsonc",
    "tsconfig*.json": "jsonc",
    "vite.config.*": "javascript",
    "vitest.config.*": "javascript",
    "*.luau": "luau",
    "*.lua": "luau",
    "default.project.json": "jsonc",
    "*.project.json": "jsonc",
    "selene.toml": "toml",
    "aftman.toml": "toml",
    "foreman.toml": "toml"
  },

  // ===== FILES CONFIGURATION =====
  "files.encoding": "utf8",
  "files.eol": "\n",
  "files.insertFinalNewline": true,
  "files.trimFinalNewlines": true,
  "files.trimTrailingWhitespace": true,
  "files.autoSave": "onFocusChange",
  "files.autoSaveDelay": 1000,
  "files.defaultLanguage": "typescript",

  // ===== SEARCH CONFIGURATION =====
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/build": true,
    "**/coverage": true,
    "**/.git": true,
    "**/.DS_Store": true,
    "**/logs": true,
    "**/*.log": true,
    "**/.firebase": true,
    "**/.vscode": false,
    "**/packages": true
  },
  "search.useIgnoreFiles": true,
  "search.useGlobalIgnoreFiles": true,
  "search.smartCase": true,

  // ===== EXPLORER CONFIGURATION =====
  "explorer.confirmDelete": false,
  "explorer.confirmDragAndDrop": false,
  "explorer.sortOrder": "type",
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.expand": false,
  "explorer.fileNesting.patterns": {
    "*.ts": "${capture}.js, ${capture}.d.ts.map, ${capture}.d.ts, ${capture}.js.map",
    "*.tsx": "${capture}.ts, ${capture}.js, ${capture}.js.map, ${capture}.d.ts, ${capture}.d.ts.map",
    "*.js": "${capture}.js.map, ${capture}.min.js, ${capture}.d.ts",
    "*.jsx": "${capture}.js, ${capture}.*.jsx, ${capture}_*.js, ${capture}_*.jsx",
    "package.json": "package-lock.json, yarn.lock, pnpm-lock.yaml, bun.lockb",
    "tsconfig.json": "tsconfig.*.json",
    "vite.config.*": "vitest.config.*, cypress.config.*, playwright.config.*",
    ".eslintrc.*": ".eslintignore, .eslintcache",
    ".prettierrc.*": ".prettierignore",
    ".gitignore": ".gitattributes, .gitmodules, .gitmessage, .mailmap, .git-blame*",
    "readme.*": "authors, backers.*, changelog*, citation*, code_of_conduct.*, codeowners, contributing.*, contributors, copying, credits, governance.*, history.*, license*, maintainers, readme*, security.*, sponsors.*",
    "*.luau": "${capture}.lua, ${capture}.server.luau, ${capture}.client.luau",
    "default.project.json": "*.project.json, foreman.toml, aftman.toml, selene.toml, .luaurc"
  },

  // ===== LANGUAGE-SPECIFIC SETTINGS =====
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.suggest.insertMode": "replace",
    "editor.maxTokenizationLineLength": 2500
  },

  "[javascriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.suggest.insertMode": "replace",
    "editor.maxTokenizationLineLength": 2500
  },

  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.suggest.insertMode": "replace",
    "editor.maxTokenizationLineLength": 2500
  },

  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.suggest.insertMode": "replace",
    "editor.maxTokenizationLineLength": 2500
  },

  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.quickSuggestions": {
      "strings": true
    },
    "editor.suggest.insertMode": "replace"
  },

  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.quickSuggestions": {
      "strings": true
    },
    "editor.suggest.insertMode": "replace"
  },

  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.suggest.insertMode": "replace"
  },

  "[scss]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.suggest.insertMode": "replace"
  },

  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.suggest.insertMode": "replace"
  },

  "[markdown]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.wordWrap": "on",
    "editor.quickSuggestions": {
      "comments": "off",
      "strings": "off",
      "other": "off"
    }
  },

  "[luau]": {
    "editor.defaultFormatter": "JohnnyMorganz.stylua",
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.formatOnSave": true,
    "editor.semanticHighlighting.enabled": true,
    "editor.suggest.insertMode": "replace",
    "editor.suggest.showKeywords": true,
    "editor.suggest.showSnippets": true
  },

  "[lua]": {
    "editor.defaultFormatter": "JohnnyMorganz.stylua",
    "editor.formatOnSave": true
  },

  // ===== SPELLING CONFIGURATION =====
  "cSpell.enabled": true,
  "cSpell.language": "en",
  "cSpell.enabledLanguageIds": [
    "autofetch",
    "commitlint",
    "devops",
    "esbenp",
    "eslint.config.js",
    "prettierrc"
  ],
  "cSpell.words": [
    "luau",
    "roblox",
    "datastores",
    "bindable",
    "remoteevent",
    "remotefunction",
    "runservice",
    "userinputservice",
    "contextactionservice",
    "tweenservice",
    "soundservice",
    "workspace",
    "serverstorage",
    "replicatedstorage",
    "starterpack",
    "startergui",
    "serverscriptservice",
    "MAFS",
    "TBRDS",
    "CGS",
    "MCS"
  ],

  // ===== GIT CONFIGURATION =====
  "git.enableSmartCommit": true,
  "git.confirmSync": false,
  "git.autofetch": true,
  "git.autofetchPeriod": 180,
  "git.decorations.enabled": true,
  "git.enableStatusBarSync": true,
  "git.ignoreLimitWarning": true,

  // ===== TERMINAL CONFIGURATION =====
  "terminal.integrated.fontSize": 13,
  "terminal.integrated.fontFamily": "'Fira Code', 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace",
  "terminal.integrated.cursorBlinking": true,
  "terminal.integrated.cursorStyle": "line",
  "terminal.integrated.scrollback": 10000,

  // ===== WORKBENCH CONFIGURATION =====
  "workbench.editor.enablePreview": false,
  "workbench.editor.enablePreviewFromQuickOpen": false,
  "workbench.editor.closeEmptyGroups": false,
  "workbench.editor.highlightModifiedTabs": true,
  "workbench.editor.limit.enabled": true,
  "workbench.editor.limit.value": 10,
  "workbench.editor.limit.perEditorGroup": true,
  "workbench.startupEditor": "newUntitledFile",
  "workbench.settings.editor": "json",
  "workbench.settings.useSplitJSON": true,

  // ===== PERFORMANCE OPTIMIZATIONS =====
  "npm.fetchOnlinePackageInfo": false,

  // ===== BREADCRUMBS =====
  "breadcrumbs.enabled": true,
  "breadcrumbs.filePath": "on",
  "breadcrumbs.symbolPath": "on",

  // ===== PROBLEMS PANEL =====
  "problems.decorations.enabled": true,
  "problems.showCurrentInStatus": true,

  // ===== OUTLINE =====
  "outline.showFiles": true,
  "outline.showModules": true,
  "outline.showNamespaces": true,
  "outline.showPackages": true,
  "outline.showClasses": true,
  "outline.showMethods": true,
  "outline.showProperties": true,
  "outline.showFields": true,
  "outline.showConstructors": true,
  "outline.showEnums": true,
  "outline.showInterfaces": true,
  "outline.showFunctions": true,
  "outline.showVariables": true,
  "outline.showConstants": true,
  "outline.showStrings": true,
  "outline.showNumbers": true,
  "outline.showBooleans": true,
  "outline.showArrays": true,
  "outline.showObjects": true,
  "outline.showKeys": true,
  "outline.showNull": true,

  // ===== ROBLOX/LUAU SPECIFIC EXTENSIONS =====
  "errorLens.enabledDiagnosticLevels": [
    "error",
    "warning",
    "info"
  ],
  "errorLens.followCursor": "allLines",

  "todo-tree.general.tags": [
    "BUG",
    "HACK",
    "FIXME",
    "TODO",
    "XXX",
    "[ ]",
    "[x]",
    "NOTE"
  ],
  "todo-tree.regex.regex": "(//|#|<!--|;|/\\*|^|^\\s*(-|\\*|\\+))\\s*($TAGS)",

  "gitlens.currentLine.enabled": true,
  "gitlens.hovers.currentLine.over": "line",

  // ===== WORKSPACE SPECIFIC =====
  "files.watcherExclude": {
    "**/packages/**": true,
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/node_modules/**": true
  }
}
